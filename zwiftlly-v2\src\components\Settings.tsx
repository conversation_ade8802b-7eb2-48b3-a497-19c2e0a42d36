import React, { useState } from 'react';
import {
  Settings as SettingsIcon,
  User,
  Users,
  Save,
  Upload,
  Plus,
  Edit,
  Trash2,
  Building,
  Sliders,
  Key,
  Shield,
  Bot,
  Mail,
  Lock
} from 'lucide-react';
import { useAuth } from '../AuthContext';
import { useCompany } from '../contexts/CompanyContext';
import { usePermissions } from './ProtectedRoute';
import ProtectedRoute from './ProtectedRoute';

interface SettingsProps {
  darkMode?: boolean;
  onToggleDarkMode?: () => void;
}

const Settings: React.FC<SettingsProps> = ({ darkMode = false, onToggleDarkMode }) => {
  const { user } = useAuth();
  const { organization, users } = useCompany();
  const { hasPermission, isAdmin } = usePermissions();
  
  const [activeTab, setActiveTab] = useState('general');
  const [saving, setSaving] = useState(false);
  const [showAddUser, setShowAddUser] = useState(false);

  const [generalSettings, setGeneralSettings] = useState({
    companyName: organization?.name || 'ZWIFTLLY',
    logo: '',
    darkMode: darkMode
  });

  const [apiSettings, setApiSettings] = useState({
    geminiApiKey: '',
    openWeatherApiKey: '********************************'
  });

  const [superAdminSettings, setSuperAdminSettings] = useState({
    emailPasswordLoginEnabled: true,
    maintenanceMode: false,
    debugMode: false
  });

  const [newUser, setNewUser] = useState({
    email: '',
    role: 'AGENT'
  });

  const handleSaveCompanySettings = async () => {
    setSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Company settings saved:', generalSettings);
    } catch (error) {
      console.error('Failed to save company settings:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleAddUser = async () => {
    if (!newUser.email) return;

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log('User added:', newUser);
      setNewUser({ email: '', role: 'AGENT' });
      setShowAddUser(false);
    } catch (error) {
      console.error('Failed to add user:', error);
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'AGENT':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const renderGeneralTab = () => (
    <div className="space-y-8">
      {/* General Settings */}
      <div className="bg-card border border-border rounded-lg p-6">
        <h3 className="text-lg font-semibold text-foreground mb-6">General Settings</h3>

        <div className="space-y-6">
          {/* Company Logo */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Company Logo
            </label>
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center border border-border overflow-hidden">
                {generalSettings.logo ? (
                  <img src={generalSettings.logo} alt="Company Logo" className="w-full h-full object-cover" />
                ) : (
                  <Building className="w-8 h-8 text-muted-foreground" />
                )}
              </div>
              <button className="flex items-center space-x-2 px-4 py-2 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors">
                <Upload className="w-4 h-4" />
                <span>Upload Logo</span>
              </button>
            </div>
          </div>

          {/* Company Name */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Company Name
            </label>
            <input
              type="text"
              value={generalSettings.companyName}
              onChange={(e) => setGeneralSettings(prev => ({ ...prev, companyName: e.target.value }))}
              className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          {/* Dark Mode Toggle */}
          <div className="flex items-center justify-between">
            <div>
              <label className="block text-sm font-medium text-foreground mb-1">
                Dark Mode
              </label>
              <p className="text-sm text-muted-foreground">Toggle between light and dark themes</p>
            </div>
            <button
              onClick={onToggleDarkMode}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                darkMode ? 'bg-primary' : 'bg-muted'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  darkMode ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          {/* Save Button */}
          <button
            onClick={() => {/* Save general settings */}}
            disabled={saving}
            className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 transition-colors"
          >
            <Save className="w-4 h-4" />
            <span>{saving ? 'Saving Settings...' : 'Save Settings'}</span>
          </button>
        </div>
      </div>
    </div>
  );

  const renderApiTab = () => (
    <div className="space-y-8">
      {/* API Configuration */}
      <div className="bg-card border border-border rounded-lg p-6">
        <h3 className="text-lg font-semibold text-foreground mb-6">API Configuration</h3>

        <div className="space-y-6">
          {/* Google Gemini AI API */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              <div className="flex items-center space-x-2">
                <Bot className="w-4 h-4" />
                <span>Google Gemini AI API Key</span>
              </div>
            </label>
            <input
              type="password"
              placeholder="Enter your Gemini AI API key..."
              value={apiSettings.geminiApiKey}
              onChange={(e) => setApiSettings(prev => ({ ...prev, geminiApiKey: e.target.value }))}
              className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
            <p className="text-xs text-muted-foreground mt-1">
              This API key will be used for the AI chat functionality throughout the application.
            </p>
          </div>

          {/* OpenWeather API */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              OpenWeather API Key
            </label>
            <input
              type="text"
              value={apiSettings.openWeatherApiKey}
              onChange={(e) => setApiSettings(prev => ({ ...prev, openWeatherApiKey: e.target.value }))}
              className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              readOnly
            />
            <p className="text-xs text-muted-foreground mt-1">
              Used for weather data in the dashboard. This is pre-configured.
            </p>
          </div>

          {/* Save Button */}
          <button
            onClick={() => {/* Save API settings */}}
            disabled={saving}
            className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 transition-colors"
          >
            <Save className="w-4 h-4" />
            <span>{saving ? 'Saving API Settings...' : 'Save API Settings'}</span>
          </button>
        </div>
      </div>
    </div>
  );

  const renderUserManagementTab = () => (
    <ProtectedRoute permission="manage_users">
      <div className="space-y-8">
        {/* User Management */}
        <div className="bg-card border border-border rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-foreground">User Management</h3>
            </div>
            <button
              onClick={() => setShowAddUser(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Add User</span>
            </button>
          </div>

          {/* Add User Form */}
          {showAddUser && (
            <div className="mb-6 p-4 bg-muted rounded-lg border border-border">
              <h4 className="text-sm font-medium text-foreground mb-4">Add New User</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <input
                  type="email"
                  placeholder="Email address"
                  value={newUser.email}
                  onChange={(e) => setNewUser(prev => ({ ...prev, email: e.target.value }))}
                  className="px-3 py-2 bg-background border border-border rounded-lg text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
                <select
                  value={newUser.role}
                  onChange={(e) => setNewUser(prev => ({ ...prev, role: e.target.value }))}
                  className="px-3 py-2 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="AGENT">Agent</option>
                  <option value="ADMIN">Admin</option>
                </select>
                <div className="flex space-x-2">
                  <button
                    onClick={handleAddUser}
                    className="flex-1 px-3 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                  >
                    Add User
                  </button>
                  <button
                    onClick={() => setShowAddUser(false)}
                    className="px-3 py-2 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Users List */}
          <div className="space-y-3">
            {users.map((user) => (
              <div key={user.id} className="flex items-center justify-between p-4 bg-muted rounded-lg border border-border">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium text-foreground">{user.full_name || user.email}</p>
                    <p className="text-sm text-muted-foreground">{user.email}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className={`px-2.5 py-1 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                    {user.role}
                  </span>
                  <div className="flex items-center space-x-1">
                    <button className="p-1 text-muted-foreground hover:text-foreground transition-colors">
                      <Edit className="w-4 h-4" />
                    </button>
                    <button className="p-1 text-muted-foreground hover:text-destructive transition-colors">
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );

  const renderSuperAdminTab = () => (
    <ProtectedRoute permission="admin_access">
      <div className="space-y-8">
        {/* Super Admin Settings */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="text-lg font-semibold text-foreground mb-6">Super Admin Settings</h3>

          <div className="space-y-6">
            {/* Email/Password Login Toggle */}
            <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
              <div>
                <div className="flex items-center space-x-2 mb-1">
                  <Mail className="w-4 h-4 text-primary" />
                  <label className="text-sm font-medium text-foreground">
                    Email + Password Authentication
                  </label>
                </div>
                <p className="text-sm text-muted-foreground">
                  Allow users to sign up and login using email and password
                </p>
              </div>
              <button
                onClick={() => setSuperAdminSettings(prev => ({
                  ...prev,
                  emailPasswordLoginEnabled: !prev.emailPasswordLoginEnabled
                }))}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  superAdminSettings.emailPasswordLoginEnabled ? 'bg-primary' : 'bg-muted-foreground'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    superAdminSettings.emailPasswordLoginEnabled ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            {/* Maintenance Mode */}
            <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
              <div>
                <div className="flex items-center space-x-2 mb-1">
                  <Lock className="w-4 h-4 text-orange-500" />
                  <label className="text-sm font-medium text-foreground">
                    Maintenance Mode
                  </label>
                </div>
                <p className="text-sm text-muted-foreground">
                  Temporarily disable access for all users except super admins
                </p>
              </div>
              <button
                onClick={() => setSuperAdminSettings(prev => ({
                  ...prev,
                  maintenanceMode: !prev.maintenanceMode
                }))}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  superAdminSettings.maintenanceMode ? 'bg-orange-500' : 'bg-muted-foreground'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    superAdminSettings.maintenanceMode ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            {/* Debug Mode */}
            <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
              <div>
                <div className="flex items-center space-x-2 mb-1">
                  <Shield className="w-4 h-4 text-blue-500" />
                  <label className="text-sm font-medium text-foreground">
                    Debug Mode
                  </label>
                </div>
                <p className="text-sm text-muted-foreground">
                  Enable detailed logging and debugging information
                </p>
              </div>
              <button
                onClick={() => setSuperAdminSettings(prev => ({
                  ...prev,
                  debugMode: !prev.debugMode
                }))}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  superAdminSettings.debugMode ? 'bg-blue-500' : 'bg-muted-foreground'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    superAdminSettings.debugMode ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            {/* Save Button */}
            <button
              onClick={() => {/* Save super admin settings */}}
              disabled={saving}
              className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors"
            >
              <Save className="w-4 h-4" />
              <span>{saving ? 'Saving Super Admin Settings...' : 'Save Super Admin Settings'}</span>
            </button>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );



  const tabs = [
    { id: 'general', label: 'General', icon: Sliders },
    { id: 'api', label: 'API', icon: Key },
    { id: 'users', label: 'User Management', icon: Users },
    ...(user?.role === 'SUPER_ADMIN' ? [{ id: 'super-admin', label: 'Super Admin', icon: Shield }] : [])
  ];

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground">Settings</h1>
        </div>

        {/* Horizontal Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-1 bg-muted p-1 rounded-lg">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors font-medium ${
                    activeTab === tab.id
                      ? 'bg-background text-foreground shadow-sm'
                      : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Main Content */}
        <div>
          {activeTab === 'general' && renderGeneralTab()}
          {activeTab === 'api' && renderApiTab()}
          {activeTab === 'users' && renderUserManagementTab()}
          {activeTab === 'super-admin' && renderSuperAdminTab()}
        </div>
      </div>
    </div>
  );
};

export default Settings;
