import React, { useState } from 'react';
import {
  Settings as SettingsIcon,
  User,
  Users,
  Save,
  Upload,
  Plus,
  Edit,
  Trash2,
  Building
} from 'lucide-react';
import { useAuth } from '../AuthContext';
import { useCompany } from '../contexts/CompanyContext';
import { usePermissions } from './ProtectedRoute';
import ProtectedRoute from './ProtectedRoute';

interface SettingsProps {
  darkMode?: boolean;
  onToggleDarkMode?: () => void;
}

const Settings: React.FC<SettingsProps> = ({ darkMode = false, onToggleDarkMode }) => {
  const { user } = useAuth();
  const { organization, users } = useCompany();
  const { hasPermission, isAdmin } = usePermissions();
  
  const [activeTab, setActiveTab] = useState('company');
  const [saving, setSaving] = useState(false);
  const [showAddUser, setShowAddUser] = useState(false);

  const [companySettings, setCompanySettings] = useState({
    name: organization?.name || 'ZWIFTLLY',
    logo: ''
  });

  const [newUser, setNewUser] = useState({
    email: '',
    role: 'AGENT'
  });

  const handleSaveCompanySettings = async () => {
    setSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Company settings saved:', companySettings);
    } catch (error) {
      console.error('Failed to save company settings:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleAddUser = async () => {
    if (!newUser.email) return;

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log('User added:', newUser);
      setNewUser({ email: '', role: 'AGENT' });
      setShowAddUser(false);
    } catch (error) {
      console.error('Failed to add user:', error);
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'AGENT':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const renderCompanyTab = () => (
    <div className="space-y-8">
      {/* Company Settings */}
      <div className="bg-card border border-border rounded-lg p-6">
        <h3 className="text-lg font-semibold text-foreground mb-6">Company Settings</h3>
        <p className="text-muted-foreground mb-6">Update company information</p>

        <div className="space-y-6">
          {/* Company Logo */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Company Logo
            </label>
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center border border-border overflow-hidden">
                {companySettings.logo ? (
                  <img src={companySettings.logo} alt="Company Logo" className="w-full h-full object-cover" />
                ) : (
                  <Building className="w-8 h-8 text-muted-foreground" />
                )}
              </div>
              <button className="flex items-center space-x-2 px-4 py-2 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors">
                <Upload className="w-4 h-4" />
                <span>Upload Logo</span>
              </button>
            </div>
          </div>

          {/* Company Name */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Company Name
            </label>
            <input
              type="text"
              value={companySettings.name}
              onChange={(e) => setCompanySettings(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          {/* Save Button */}
          <button
            onClick={handleSaveCompanySettings}
            disabled={saving}
            className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 transition-colors"
          >
            <Save className="w-4 h-4" />
            <span>{saving ? 'Saving Company Settings...' : 'Save Company Settings'}</span>
          </button>
        </div>
      </div>
    </div>
  );

  const renderUserManagementTab = () => (
    <ProtectedRoute permission="manage_users">
      <div className="space-y-8">
        {/* User Management */}
        <div className="bg-card border border-border rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-foreground">User Management</h3>
              <p className="text-muted-foreground">Manage user accounts and permissions</p>
            </div>
            <button
              onClick={() => setShowAddUser(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Add User</span>
            </button>
          </div>

          {/* Add User Form */}
          {showAddUser && (
            <div className="mb-6 p-4 bg-muted rounded-lg border border-border">
              <h4 className="text-sm font-medium text-foreground mb-4">Add New User</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <input
                  type="email"
                  placeholder="Email address"
                  value={newUser.email}
                  onChange={(e) => setNewUser(prev => ({ ...prev, email: e.target.value }))}
                  className="px-3 py-2 bg-background border border-border rounded-lg text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
                <select
                  value={newUser.role}
                  onChange={(e) => setNewUser(prev => ({ ...prev, role: e.target.value }))}
                  className="px-3 py-2 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="AGENT">Agent</option>
                  <option value="ADMIN">Admin</option>
                </select>
                <div className="flex space-x-2">
                  <button
                    onClick={handleAddUser}
                    className="flex-1 px-3 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                  >
                    Add User
                  </button>
                  <button
                    onClick={() => setShowAddUser(false)}
                    className="px-3 py-2 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Users List */}
          <div className="space-y-3">
            {users.map((user) => (
              <div key={user.id} className="flex items-center justify-between p-4 bg-muted rounded-lg border border-border">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium text-foreground">{user.full_name || user.email}</p>
                    <p className="text-sm text-muted-foreground">{user.email}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className={`px-2.5 py-1 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                    {user.role}
                  </span>
                  <div className="flex items-center space-x-1">
                    <button className="p-1 text-muted-foreground hover:text-foreground transition-colors">
                      <Edit className="w-4 h-4" />
                    </button>
                    <button className="p-1 text-muted-foreground hover:text-destructive transition-colors">
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );



  const tabs = [
    { id: 'company', label: 'Company Settings', icon: Building },
    { id: 'users', label: 'User Management', icon: Users }
  ];

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3">
            <SettingsIcon className="w-8 h-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold text-foreground">Settings</h1>
              <p className="text-muted-foreground">Manage system settings, users, and company information</p>
            </div>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className="lg:w-64">
            <nav className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 text-left rounded-lg transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-medium">{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {activeTab === 'company' && renderCompanyTab()}
            {activeTab === 'users' && renderUserManagementTab()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
