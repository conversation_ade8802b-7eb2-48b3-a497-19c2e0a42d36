import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Bot, Send, X, Minimize2, Maximize2, <PERSON>rk<PERSON> } from 'lucide-react';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
}

const AIAssistant: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Hello! I\'m your ZWIFTLLY AI Assistant. I can help you with knowledge base searches, team schedules, performance insights, and more. How can I assist you today?',
      sender: 'ai',
      timestamp: new Date()
    }
  ]);

  const handleSendMessage = async () => {
    if (message.trim()) {
      const userMessage: Message = {
        id: Date.now().toString(),
        text: message,
        sender: 'user',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, userMessage]);
      const currentMessage = message;
      setMessage('');

      // Try Google Gemini API first, fallback to mock response
      try {
        const geminiApiKey = localStorage.getItem('gemini_api_key');
        if (geminiApiKey) {
          const response = await callGeminiAPI(currentMessage, geminiApiKey);
          const aiResponse: Message = {
            id: (Date.now() + 1).toString(),
            text: response,
            sender: 'ai',
            timestamp: new Date()
          };
          setMessages(prev => [...prev, aiResponse]);
        } else {
          // Fallback to mock response
          setTimeout(() => {
            const aiResponse: Message = {
              id: (Date.now() + 1).toString(),
              text: getAIResponse(currentMessage),
              sender: 'ai',
              timestamp: new Date()
            };
            setMessages(prev => [...prev, aiResponse]);
          }, 1000);
        }
      } catch (error) {
        // Fallback to mock response on error
        setTimeout(() => {
          const aiResponse: Message = {
            id: (Date.now() + 1).toString(),
            text: getAIResponse(currentMessage),
            sender: 'ai',
            timestamp: new Date()
          };
          setMessages(prev => [...prev, aiResponse]);
        }, 1000);
      }
    }
  };

  const callGeminiAPI = async (message: string, apiKey: string): Promise<string> => {
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: `You are ZWIFTLLY AI Assistant, a helpful assistant for a team management system. The user is asking: "${message}". Please provide a helpful, concise response related to team management, productivity, or workplace assistance. Keep responses under 200 words.`
          }]
        }]
      })
    });

    if (!response.ok) {
      throw new Error('Gemini API request failed');
    }

    const data = await response.json();
    return data.candidates[0]?.content?.parts[0]?.text || 'I apologize, but I encountered an issue processing your request. Please try again.';
  };

  const getAIResponse = (userMessage: string): string => {
    const lowerMessage = userMessage.toLowerCase();
    
    if (lowerMessage.includes('schedule') || lowerMessage.includes('time')) {
      return 'I can help you with team schedules! You can view the Live Adherence Board, check weekly schedules, or edit team member schedules. Would you like me to guide you to the Team Schedules page?';
    }
    
    if (lowerMessage.includes('knowledge') || lowerMessage.includes('document') || lowerMessage.includes('policy')) {
      return 'I can help you search the Knowledge Base! We have documents on Tenants, Guidelines, Policies, and Processes. What specific information are you looking for?';
    }
    
    if (lowerMessage.includes('performance') || lowerMessage.includes('metric') || lowerMessage.includes('analytics')) {
      return 'I can provide insights on team performance! Current metrics show 94.2% quality score and 87.5% audit completion. Would you like detailed performance analytics?';
    }
    
    if (lowerMessage.includes('task') || lowerMessage.includes('project')) {
      return 'I can help with task management! You can create, assign, and track tasks on the Task Board. Would you like me to help you create a new task?';
    }
    
    if (lowerMessage.includes('user') || lowerMessage.includes('setting') || lowerMessage.includes('admin')) {
      return 'For user management and settings, you can access the Settings page where admins can add users, manage permissions, and update company information.';
    }
    
    return 'I understand you\'re asking about "' + userMessage + '". I can help with schedules, knowledge base searches, performance metrics, task management, and system settings. Could you be more specific about what you need help with?';
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  if (!isOpen) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        {/* Floating attention bubbles */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className="absolute w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-70 animate-float-bubble"
              style={{
                left: `${20 + i * 15}%`,
                top: `${10 + i * 20}%`,
                animationDelay: `${i * 0.8}s`,
                animationDuration: `${2 + i * 0.5}s`
              }}
            />
          ))}
        </div>

        {/* Pulsing ring effect */}
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 opacity-30 animate-ping"></div>
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 opacity-20 animate-pulse"></div>

        <Button
          onClick={() => setIsOpen(true)}
          className="relative w-16 h-16 rounded-full gradient-ai-assistant hover:opacity-90 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 group animate-bounce-gentle"
          title="AI Assistant"
        >
          <div className="relative">
            <Bot className="w-8 h-8 text-white group-hover:scale-110 transition-transform duration-300 animate-wiggle" />
            <Sparkles className="w-4 h-4 text-yellow-300 absolute -top-1 -right-1 animate-pulse" />
            {/* Glowing dot indicator */}
            <div className="absolute -top-2 -right-2 w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg"></div>
          </div>
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Card className={`bg-card border border-border/50 shadow-2xl transition-all duration-300 ${
        isMinimized ? 'w-80 h-16' : 'w-96 h-[500px]'
      }`}>
        {/* Header */}
        <CardHeader className="p-4 border-b border-border/50 bg-gradient-to-r from-blue-600/10 to-purple-600/10">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center">
                <Bot className="w-6 h-6 text-white" />
              </div>
              <div>
                <span className="text-foreground font-bold">ZWIFTLLY AI Assistant</span>
                <div className="text-xs text-green-600 dark:text-green-400 font-medium">Online</div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMinimized(!isMinimized)}
                className="w-8 h-8 p-0"
              >
                {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="w-8 h-8 p-0"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </CardTitle>
        </CardHeader>

        {!isMinimized && (
          <>
            {/* Messages */}
            <CardContent className="p-0 h-[360px] overflow-y-auto">
              <div className="p-4 space-y-4">
                {messages.map((msg) => (
                  <div
                    key={msg.id}
                    className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] p-3 rounded-lg ${
                        msg.sender === 'user'
                          ? 'bg-blue-600 text-white'
                          : 'bg-muted text-foreground'
                      }`}
                    >
                      <p className="text-sm">{msg.text}</p>
                      <p className={`text-xs mt-1 ${
                        msg.sender === 'user' ? 'text-blue-100' : 'text-muted-foreground'
                      }`}>
                        {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>

            {/* Input */}
            <div className="p-4 border-t border-border/50">
              <div className="flex items-center space-x-2">
                <Input
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask me anything about ZWIFTLLY..."
                  className="flex-1 bg-background border-border/50"
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!message.trim()}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </>
        )}
      </Card>
    </div>
  );
};

export default AIAssistant;
