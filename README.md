# ZWIFTLLY Team Management System

A comprehensive, production-ready team management platform built with modern web technologies, featuring real-time collaboration, attendance tracking, task management, and advanced analytics.

## 🚀 Features

### **Core Functionality**
- **🔐 Authentication & Authorization**: Google OAuth integration with role-based access control
- **👥 User Management**: Complete user lifecycle management with organizational structure
- **⏰ Attendance & Payroll**: Advanced time tracking with automated payroll calculations
- **📋 Task Management**: Comprehensive project and task tracking with team collaboration
- **📢 Announcements**: Real-time communication system with categorized announcements
- **📊 Analytics Dashboard**: Performance metrics and team insights
- **🌤️ Weather Integration**: Location-based weather information
- **🤖 AI Assistant**: Integrated AI support for productivity enhancement

### **Advanced Features**
- **🔄 Real-time Updates**: Live synchronization across all connected clients
- **📱 Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **🌙 Dark/Light Mode**: Adaptive theming with user preferences
- **🔍 Advanced Search**: Powerful search and filtering capabilities
- **📈 Performance Monitoring**: Built-in analytics and monitoring
- **🔒 Enterprise Security**: Comprehensive security measures and data protection
- **📤 Export Capabilities**: Data export in multiple formats
- **🔔 Smart Notifications**: Intelligent notification system with real-time alerts

## 🏗️ Architecture

### **Frontend (React + TypeScript)**
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with custom design system
- **State Management**: React Context with custom hooks
- **Real-time**: Socket.IO client integration
- **Authentication**: Google OAuth with JWT tokens
- **Build Tool**: Vite for fast development and optimized builds

### **Backend (Node.js + Express)**
- **Runtime**: Node.js 18+ with Express.js
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT-based with Google OAuth verification
- **Real-time**: Socket.IO for live updates
- **API**: RESTful APIs with comprehensive validation
- **Security**: Rate limiting, CORS, input validation, and security headers

### **Database Schema**
- **Users & Organizations**: Multi-tenant architecture
- **Attendance & Payroll**: Comprehensive time tracking and payroll calculations
- **Tasks & Teams**: Project management with team collaboration
- **Announcements & Comments**: Communication system
- **Audit Logs**: Complete activity tracking
- **Notifications**: Real-time notification system

## 🚀 Quick Start

### **Prerequisites**
- Node.js 18+ and npm
- PostgreSQL 15+
- Google OAuth credentials
- OpenWeatherMap API key (optional)

### **Installation**

1. **Clone the repository**
```bash
git clone https://github.com/your-org/zwiftlly-team-management-system.git
cd zwiftlly-team-management-system
```

2. **Backend Setup**
```bash
cd zwiftlly-backend
npm install
cp .env.example .env
# Configure your environment variables in .env
npx prisma migrate deploy
npx prisma generate
npm run dev
```

3. **Frontend Setup**
```bash
cd zwiftlly-frontend
npm install
cp .env.example .env
# Configure your environment variables in .env
npm run dev
```

4. **Access the Application**
- Frontend: http://localhost:5173
- Backend API: http://localhost:3001
- API Documentation: http://localhost:3001/api

## 🔧 Configuration

### **Environment Variables**

**Backend (.env)**
```env
DATABASE_URL="postgresql://user:password@localhost:5432/zwiftlly_db"
JWT_SECRET="your-super-secret-jwt-key"
GOOGLE_CLIENT_ID="your-google-oauth-client-id"
CORS_ORIGIN="http://localhost:5173"
ALLOWED_EMAILS="<EMAIL>"
WEATHER_API_KEY="your-openweathermap-api-key"
```

**Frontend (.env)**
```env
VITE_GOOGLE_CLIENT_ID="your-google-oauth-client-id"
VITE_API_URL="http://localhost:3001/api"
VITE_ALLOWED_EMAILS="<EMAIL>"
```

## 📚 API Documentation

### **Authentication Endpoints**
- `POST /api/auth/google` - Google OAuth login
- `GET /api/auth/me` - Get current user
- `POST /api/auth/logout` - Logout user
- `GET /api/auth/permissions` - Get user permissions

### **User Management**
- `GET /api/users` - List users (admin only)
- `GET /api/users/:id` - Get user details
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user (admin only)

### **Attendance & Payroll**
- `GET /api/attendance` - Get attendance records
- `POST /api/attendance/clock-action` - Record clock action
- `GET /api/attendance/summary` - Get attendance summary
- `GET /api/attendance/recent-actions` - Get recent clock actions

### **Task Management**
- `GET /api/tasks` - List tasks
- `POST /api/tasks` - Create task
- `PUT /api/tasks/:id` - Update task
- `DELETE /api/tasks/:id` - Delete task

### **Announcements**
- `GET /api/announcements` - List announcements
- `POST /api/announcements` - Create announcement
- `PUT /api/announcements/:id` - Update announcement
- `POST /api/announcements/:id/comments` - Add comment

## 🚀 Deployment

### **Production Deployment**

**Using Docker Compose**
```bash
cd zwiftlly-backend
docker-compose up -d
```

**Using Railway (Backend)**
```bash
# Install Railway CLI
npm install -g @railway/cli

# Deploy to Railway
railway login
railway link
railway up
```

**Using Vercel (Frontend)**
```bash
# Install Vercel CLI
npm install -g vercel

# Deploy to Vercel
cd zwiftlly-frontend
vercel --prod
```

### **Environment Setup**
1. Set up PostgreSQL database (Railway, Supabase, or AWS RDS)
2. Configure Google OAuth credentials
3. Set up environment variables in deployment platform
4. Run database migrations
5. Deploy applications

## 🧪 Testing

### **Backend Tests**
```bash
cd zwiftlly-backend
npm test                # Run all tests
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Run tests with coverage
```

### **Frontend Tests**
```bash
cd zwiftlly-frontend
npm test                # Run all tests
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Run tests with coverage
```

## 🔒 Security

### **Security Features**
- JWT-based authentication with secure token handling
- Role-based access control (RBAC)
- Input validation and sanitization
- Rate limiting and DDoS protection
- CORS configuration
- Security headers (HSTS, CSP, etc.)
- SQL injection prevention with Prisma ORM
- XSS protection
- Audit logging for all actions

### **Security Best Practices**
- Regular security updates
- Environment variable protection
- Secure cookie handling
- HTTPS enforcement in production
- Database connection encryption
- API rate limiting
- Input validation on all endpoints

## 📊 Monitoring & Analytics

### **Built-in Monitoring**
- Health check endpoints
- Performance metrics
- Error tracking and logging
- User activity analytics
- System resource monitoring

### **Logging**
- Structured logging with Winston
- Request/response logging
- Error tracking
- Audit trail for all user actions
- Performance monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- 📧 Email: <EMAIL>
- 📖 Documentation: [docs.zwiftlly.com](https://docs.zwiftlly.com)
- 🐛 Issues: [GitHub Issues](https://github.com/your-org/zwiftlly-team-management-system/issues)

## 🙏 Acknowledgments

- React and the React community
- Prisma for excellent database tooling
- Tailwind CSS for the design system
- Socket.IO for real-time functionality
- All contributors and supporters

---

**Built with ❤️ by the ZWIFTLLY Team**
