import React, { useState } from 'react'
import { useAuth } from '../AuthContext'
import { Building, Shield, Users, Globe, Eye, EyeOff, Mail, Lock, AlertCircle } from 'lucide-react'

const Login: React.FC = () => {
  const [authMethod, setAuthMethod] = useState<'google' | 'email'>('google')
  const [isSignUp, setIsSignUp] = useState(false)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [name, setName] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { signInWithGoogle, signUpWithGoogle, signInWithEmail, signUpWithEmail, isLoading } = useAuth()

  const handleGoogleAuth = async () => {
    setIsSubmitting(true)
    setError('')

    try {
      const result = isSignUp ? await signUpWithGoogle() : await signInWithGoogle()
      
      if (!result.success) {
        setError(result.message)
        setIsSubmitting(false)
      }
    } catch (error: any) {
      setError(error.message || 'Authentication failed')
      setIsSubmitting(false)
    }
  }

  const handleEmailAuth = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError('')

    try {
      const result = isSignUp 
        ? await signUpWithEmail(email, password, name)
        : await signInWithEmail(email, password)
      
      if (!result.success) {
        setError(result.message)
        setIsSubmitting(false)
      } else if (result.message.includes('check your email')) {
        setError(result.message) // This is actually a success message
      }
    } catch (error: any) {
      setError(error.message || 'Authentication failed')
      setIsSubmitting(false)
    }
  }

  const isFormDisabled = isLoading || isSubmitting

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Logo and Title */}
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
            <Building className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-foreground">ZWIFTLLY</h1>
            <p className="text-muted-foreground">Team Management System</p>
          </div>
        </div>

        {/* Login Card */}
        <div className="bg-card/80 backdrop-blur-sm border border-border/50 shadow-xl rounded-lg">
          <div className="p-6 space-y-1 text-center border-b border-border/50">
            <h2 className="text-2xl font-bold text-foreground">
              {isSignUp ? 'Join ZWIFTLLY' : 'Welcome Back'}
            </h2>
            <p className="text-sm text-muted-foreground">
              {isSignUp
                ? 'Create your account with authorized organization email'
                : 'Sign in with your organization account'
              }
            </p>
          </div>
          
          <div className="p-6 space-y-6">
            {/* Security Features */}
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="space-y-2">
                <Shield className="w-6 h-6 mx-auto text-blue-600" />
                <p className="text-xs text-muted-foreground">Secure Auth</p>
              </div>
              <div className="space-y-2">
                <Users className="w-6 h-6 mx-auto text-purple-600" />
                <p className="text-xs text-muted-foreground">Organization Access</p>
              </div>
              <div className="space-y-2">
                <Globe className="w-6 h-6 mx-auto text-green-600" />
                <p className="text-xs text-muted-foreground">Domain Verified</p>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className={`border rounded-lg p-3 flex items-center gap-2 ${
                error.includes('successful') || error.includes('check your email')
                  ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                  : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
              }`}>
                <AlertCircle className={`w-4 h-4 flex-shrink-0 ${
                  error.includes('successful') || error.includes('check your email')
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-red-600 dark:text-red-400'
                }`} />
                <p className={`text-sm ${
                  error.includes('successful') || error.includes('check your email')
                    ? 'text-green-700 dark:text-green-300'
                    : 'text-red-700 dark:text-red-300'
                }`}>{error}</p>
              </div>
            )}

            {/* Domain Restrictions Info */}
            {isSignUp && (
              <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <Globe className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">Authorized Domains Only</h4>
                    <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                      Only users with netic.ai email addresses can sign up.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Authentication Method Switcher */}
            <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
              <button
                onClick={() => setAuthMethod('google')}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                  authMethod === 'google'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
                }`}
              >
                Google OAuth
              </button>
              <button
                onClick={() => setAuthMethod('email')}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                  authMethod === 'email'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
                }`}
              >
                Email & Password
              </button>
            </div>

            {/* Google OAuth Method */}
            {authMethod === 'google' && (
              <div className="space-y-4">
                <button
                  onClick={handleGoogleAuth}
                  disabled={isFormDisabled}
                  className="w-full bg-white hover:bg-gray-50 text-gray-900 font-medium py-3 px-6 rounded-lg border border-gray-300 transition-all duration-200 flex items-center justify-center space-x-3 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isFormDisabled ? (
                    <div className="w-5 h-5 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
                  ) : (
                    <svg className="w-5 h-5" viewBox="0 0 24 24">
                      <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                      <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                      <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                      <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                  )}
                  <span>{isSignUp ? 'Sign up with Google' : 'Sign in with Google'}</span>
                </button>

                {/* Switch Sign Up/In */}
                <div className="text-center">
                  <button
                    onClick={() => setIsSignUp(!isSignUp)}
                    className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
                  >
                    {isSignUp ? 'Already have an account? Sign in' : 'Need an account? Sign up'}
                  </button>
                </div>
              </div>
            )}

            {/* Email/Password Method */}
            {authMethod === 'email' && (
              <form onSubmit={handleEmailAuth} className="space-y-4">
                {isSignUp && (
                  <div className="space-y-2">
                    <label htmlFor="name" className="text-sm font-medium text-foreground">Full Name</label>
                    <div className="relative">
                      <Users className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <input
                        id="name"
                        type="text"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        placeholder="Enter your full name"
                        className="w-full pl-10 pr-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                        required
                      />
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-medium text-foreground">Email</label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your organization email"
                      className="w-full pl-10 pr-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label htmlFor="password" className="text-sm font-medium text-foreground">Password</label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder={isSignUp ? "Create a strong password" : "Enter your password"}
                      className="w-full pl-10 pr-10 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-3 text-muted-foreground hover:text-foreground"
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={isFormDisabled}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isFormDisabled ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>{isSignUp ? 'Creating Account...' : 'Signing In...'}</span>
                    </div>
                  ) : (
                    <span>{isSignUp ? 'Create Account' : 'Sign In'}</span>
                  )}
                </button>

                {/* Switch Sign Up/In */}
                <div className="text-center">
                  <button
                    type="button"
                    onClick={() => setIsSignUp(!isSignUp)}
                    className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
                  >
                    {isSignUp ? 'Already have an account? Sign in' : 'Need an account? Sign up'}
                  </button>
                </div>
              </form>
            )}

            {/* Organization Info */}
            <div className="text-center space-y-2">
              <p className="text-xs text-muted-foreground">
                Only authorized organization domains can access this system
              </p>
              <p className="text-xs text-muted-foreground font-medium">
                Contact your administrator if you need access
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-xs text-muted-foreground">
          <p>© 2025 ZWIFTLLY Team Management System</p>
        </div>
      </div>
    </div>
  )
}

export default Login
