import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase, type Database } from '../lib/supabase';
import { useAuth } from '../AuthContext';

type Task = Database['public']['Tables']['tasks']['Row'];
type Announcement = Database['public']['Tables']['announcements']['Row'];
type Notification = Database['public']['Tables']['notifications']['Row'];
type AttendanceRecord = Database['public']['Tables']['attendance_records']['Row'];

interface DataContextType {
  // Tasks
  tasks: Task[];
  tasksLoading: boolean;
  refreshTasks: () => Promise<void>;
  
  // Announcements
  announcements: Announcement[];
  announcementsLoading: boolean;
  refreshAnnouncements: () => Promise<void>;
  
  // Notifications
  notifications: Notification[];
  unreadCount: number;
  notificationsLoading: boolean;
  refreshNotifications: () => Promise<void>;
  markNotificationAsRead: (id: string) => Promise<void>;
  
  // Attendance
  attendanceRecords: AttendanceRecord[];
  attendanceLoading: boolean;
  refreshAttendance: () => Promise<void>;
  
  // Global refresh
  refreshAllData: () => Promise<void>;
  
  // Error handling
  error: string | null;
  clearError: () => void;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export const useData = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

interface DataProviderProps {
  children: React.ReactNode;
}

export const DataProvider: React.FC<DataProviderProps> = ({ children }) => {
  const { user } = useAuth();
  
  // State
  const [tasks, setTasks] = useState<Task[]>([]);
  const [tasksLoading, setTasksLoading] = useState(false);
  
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [announcementsLoading, setAnnouncementsLoading] = useState(false);
  
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [notificationsLoading, setNotificationsLoading] = useState(false);
  
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [attendanceLoading, setAttendanceLoading] = useState(false);
  
  const [error, setError] = useState<string | null>(null);

  // Computed values
  const unreadCount = notifications.filter(n => !n.is_read && !n.is_archived).length;

  const clearError = () => setError(null);

  // Tasks functions
  const refreshTasks = async () => {
    if (!user) return;
    
    setTasksLoading(true);
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select(`
          *,
          assignee:users!tasks_assignee_id_fkey(id, full_name, email),
          creator:users!tasks_created_by_fkey(id, full_name, email)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setTasks(data || []);
    } catch (err) {
      console.error('Error fetching tasks:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch tasks');
    } finally {
      setTasksLoading(false);
    }
  };

  // Announcements functions
  const refreshAnnouncements = async () => {
    if (!user) return;
    
    setAnnouncementsLoading(true);
    try {
      const { data, error } = await supabase
        .from('announcements')
        .select(`
          *,
          author:users!announcements_author_id_fkey(id, full_name, email, avatar_url)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setAnnouncements(data || []);
    } catch (err) {
      console.error('Error fetching announcements:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch announcements');
    } finally {
      setAnnouncementsLoading(false);
    }
  };

  // Notifications functions
  const refreshNotifications = async () => {
    if (!user) return;
    
    setNotificationsLoading(true);
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setNotifications(data || []);
    } catch (err) {
      console.error('Error fetching notifications:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch notifications');
    } finally {
      setNotificationsLoading(false);
    }
  };

  const markNotificationAsRead = async (id: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ 
          is_read: true, 
          read_at: new Date().toISOString() 
        })
        .eq('id', id);

      if (error) throw error;

      // Update local state
      setNotifications(prev => 
        prev.map(n => 
          n.id === id 
            ? { ...n, is_read: true, read_at: new Date().toISOString() }
            : n
        )
      );
    } catch (err) {
      console.error('Error marking notification as read:', err);
      setError(err instanceof Error ? err.message : 'Failed to mark notification as read');
    }
  };

  // Attendance functions
  const refreshAttendance = async () => {
    if (!user) return;
    
    setAttendanceLoading(true);
    try {
      const { data, error } = await supabase
        .from('attendance_records')
        .select(`
          *,
          user:users!attendance_records_user_id_fkey(id, full_name, email)
        `)
        .eq('user_id', user.id)
        .order('date', { ascending: false });

      if (error) throw error;
      setAttendanceRecords(data || []);
    } catch (err) {
      console.error('Error fetching attendance:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch attendance records');
    } finally {
      setAttendanceLoading(false);
    }
  };

  // Global refresh
  const refreshAllData = async () => {
    await Promise.all([
      refreshTasks(),
      refreshAnnouncements(),
      refreshNotifications(),
      refreshAttendance()
    ]);
  };

  // Initial data load
  useEffect(() => {
    if (user) {
      refreshAllData();
    }
  }, [user]);

  // Set up real-time subscriptions
  useEffect(() => {
    if (!user) return;

    const subscriptions = [
      // Tasks subscription
      supabase
        .channel('tasks-changes')
        .on('postgres_changes', { event: '*', schema: 'public', table: 'tasks' }, () => {
          refreshTasks();
        })
        .subscribe(),

      // Announcements subscription
      supabase
        .channel('announcements-changes')
        .on('postgres_changes', { event: '*', schema: 'public', table: 'announcements' }, () => {
          refreshAnnouncements();
        })
        .subscribe(),

      // Notifications subscription
      supabase
        .channel('notifications-changes')
        .on('postgres_changes', 
          { 
            event: '*', 
            schema: 'public', 
            table: 'notifications',
            filter: `user_id=eq.${user.id}`
          }, 
          () => {
            refreshNotifications();
          }
        )
        .subscribe(),

      // Attendance subscription
      supabase
        .channel('attendance-changes')
        .on('postgres_changes', 
          { 
            event: '*', 
            schema: 'public', 
            table: 'attendance_records',
            filter: `user_id=eq.${user.id}`
          }, 
          () => {
            refreshAttendance();
          }
        )
        .subscribe()
    ];

    return () => {
      subscriptions.forEach(sub => sub.unsubscribe());
    };
  }, [user]);

  const value: DataContextType = {
    tasks,
    tasksLoading,
    refreshTasks,
    announcements,
    announcementsLoading,
    refreshAnnouncements,
    notifications,
    unreadCount,
    notificationsLoading,
    refreshNotifications,
    markNotificationAsRead,
    attendanceRecords,
    attendanceLoading,
    refreshAttendance,
    refreshAllData,
    error,
    clearError
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};
