import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/contexts/SupabaseAuthContext';
import { useCompany } from '@/contexts/CompanyContext';
import { AlertCircle, Building, Shield, Users, Globe, Eye, EyeOff, Mail, Lock } from 'lucide-react';

const Login: React.FC = () => {
  const [error, setError] = useState('');
  const [isSigningIn, setIsSigningIn] = useState(false);
  const [isSigningUp, setIsSigningUp] = useState(false);
  const [showSignUp, setShowSignUp] = useState(false);
  const [userEmail, setUserEmail] = useState('');
  const [authMethod, setAuthMethod] = useState<'google' | 'email'>('google');

  // Email/Password form fields
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const { googleLogin, googleSignUp, emailLogin, emailSignUp, checkUserExists, isLoading } = useAuth();
  const { companySettings } = useCompany();

  // Authorized domains
  const AUTHORIZED_DOMAINS = ['gmail.com', 'netic.ai', 'zwiftlly.com'];

  // Check if user exists when component mounts
  React.useEffect(() => {
    const checkExistingUser = async () => {
      // Get email from URL params if redirected from OAuth
      const urlParams = new URLSearchParams(window.location.search);
      const email = urlParams.get('email');

      if (email) {
        setUserEmail(email);
        const exists = await checkUserExists(email);
        setShowSignUp(!exists);
      } else {
        // Default to showing sign-in for existing users
        setShowSignUp(false);
      }
    };

    checkExistingUser();
  }, [checkUserExists]);

  // Debug logging
  console.log('🔍 Login component rendered');
  console.log('🔍 googleLogin function available:', !!googleLogin);
  console.log('🔍 isLoading:', isLoading);
  console.log('🔍 isSigningIn:', isSigningIn);

  const validateDomain = (email: string): boolean => {
    const domain = email.split('@')[1];
    return AUTHORIZED_DOMAINS.includes(domain);
  };

  const handleGoogleLogin = async () => {
    console.log('🎯 Sign In button clicked');
    setIsSigningIn(true);
    setError('');

    try {
      const result = await googleLogin();

      if (!result.success) {
        console.log('❌ Google OAuth failed:', result.message);
        setError(result.message);
        setIsSigningIn(false);
      } else {
        console.log('✅ Google OAuth sign-in initiated - redirecting...');
      }
    } catch (error) {
      console.error('💥 Google OAuth error:', error);
      setError(`Login failed: ${error?.message || 'Unknown error'}. Please try again.`);
      setIsSigningIn(false);
    }
  };

  const handleGoogleSignUp = async () => {
    console.log('🎯 Sign Up button clicked');
    setIsSigningUp(true);
    setError('');

    try {
      const result = await googleSignUp();

      if (!result.success) {
        console.log('❌ Google OAuth failed:', result.message);
        setError(result.message);
        setIsSigningUp(false);
      } else {
        console.log('✅ Google OAuth sign-up initiated - redirecting...');
      }
    } catch (error) {
      console.error('💥 Google OAuth error:', error);
      setError(`Sign-up failed: ${error?.message || 'Unknown error'}. Please try again.`);
      setIsSigningUp(false);
    }
  };

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSigningIn(true);
    setError('');

    try {
      const result = await emailLogin(email, password);

      if (!result.success) {
        setError(result.message);
        setIsSigningIn(false);
      } else {
        console.log('✅ Email login successful');
      }
    } catch (error) {
      console.error('💥 Email login error:', error);
      setError(`Login failed: ${error?.message || 'Unknown error'}. Please try again.`);
      setIsSigningIn(false);
    }
  };

  const handleEmailSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSigningUp(true);
    setError('');

    if (!name.trim()) {
      setError('Please enter your full name');
      setIsSigningUp(false);
      return;
    }

    try {
      const result = await emailSignUp(email, password, name);

      if (!result.success) {
        setError(result.message);
        setIsSigningUp(false);
      } else {
        setError(''); // Clear any previous errors
        console.log('✅ Email sign-up successful');
        // Show success message
        setError('Account created successfully! Please check your email for verification.');
      }
    } catch (error) {
      console.error('💥 Email sign-up error:', error);
      setError(`Sign-up failed: ${error?.message || 'Unknown error'}. Please try again.`);
      setIsSigningUp(false);
    }
  };



  return (
    <div className="min-h-screen gradient-login-bg flex items-center justify-center p-4 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/20 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-accent/20 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-muted/30 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>

        {/* Floating particles */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute w-2 h-2 bg-primary/30 rounded-full animate-float"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${3 + Math.random() * 2}s`
              }}
            />
          ))}
        </div>
      </div>

      <div className="w-full max-w-md space-y-6 relative z-10">
        {/* Logo and Title */}
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 gradient-primary rounded-full flex items-center justify-center shadow-lg animate-pulse overflow-hidden border-2 border-border/20">
            {companySettings.logo ? (
              <img src={companySettings.logo} alt="Company Logo" className="w-full h-full object-cover" />
            ) : (
              <Building className="w-8 h-8 text-white" />
            )}
          </div>
          <div>
            <h1 className="text-3xl font-bold text-foreground">{companySettings.name}</h1>
            <p className="text-muted-foreground">Team Management System</p>
          </div>
        </div>

        {/* Login Card */}
        <Card className="bg-card/80 backdrop-blur-sm border border-border/50 shadow-xl">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl font-bold text-foreground">
              {showSignUp ? 'Join ZWIFTLLY' : 'Welcome Back'}
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              {showSignUp
                ? 'Sign up with your authorized organization account'
                : 'Sign in with your organization account'
              }
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Security Features */}
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="space-y-2">
                <Shield className="w-6 h-6 mx-auto text-blue-600" />
                <p className="text-xs text-muted-foreground">Secure OAuth</p>
              </div>
              <div className="space-y-2">
                <Users className="w-6 h-6 mx-auto text-purple-600" />
                <p className="text-xs text-muted-foreground">Organization Access</p>
              </div>
              <div className="space-y-2">
                <Globe className="w-6 h-6 mx-auto text-green-600" />
                <p className="text-xs text-muted-foreground">Domain Verified</p>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="flex items-center space-x-2 text-red-600 dark:text-red-400 text-sm bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
                <AlertCircle className="h-4 w-4 flex-shrink-0" />
                <span>{error}</span>
              </div>
            )}



            {/* Domain Restrictions Info */}
            {showSignUp && (
              <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <Globe className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">Authorized Domains Only</h4>
                    <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                      Only users with netic.ai email addresses can sign up.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Authentication Method Switcher */}
            <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
              <button
                onClick={() => setAuthMethod('google')}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                  authMethod === 'google'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
                }`}
              >
                Google OAuth
              </button>
              <button
                onClick={() => setAuthMethod('email')}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                  authMethod === 'email'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
                }`}
              >
                Email & Password
              </button>
            </div>

            {/* Email/Password Authentication */}
            {authMethod === 'email' && (
              <div className="w-full space-y-4">
                {showSignUp ? (
                  // Email Sign Up Form
                  <form onSubmit={handleEmailSignUp} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="signup-name" className="text-foreground font-medium">Full Name</Label>
                      <div className="relative">
                        <Users className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="signup-name"
                          type="text"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          placeholder="Enter your full name"
                          className="pl-10"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="signup-email" className="text-foreground font-medium">Email</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="signup-email"
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          placeholder="Enter your organization email"
                          className="pl-10"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="signup-password" className="text-foreground font-medium">Password</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="signup-password"
                          type={showPassword ? 'text' : 'password'}
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          placeholder="Create a strong password"
                          className="pl-10 pr-10"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-3 text-muted-foreground hover:text-foreground"
                        >
                          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                    </div>

                    <Button
                      type="submit"
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium"
                      disabled={isLoading || isSigningUp}
                    >
                      {isSigningUp ? 'Creating Account...' : 'Create Account'}
                    </Button>

                    {/* Switch to Sign In */}
                    <div className="text-center">
                      <button
                        type="button"
                        onClick={() => setShowSignUp(false)}
                        className="text-sm text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 transition-colors"
                      >
                        Already have an account? Sign in
                      </button>
                    </div>
                  </form>
                ) : (
                  // Email Sign In Form
                  <form onSubmit={handleEmailLogin} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="signin-email" className="text-foreground font-medium">Email</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="signin-email"
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          placeholder="Enter your email"
                          className="pl-10"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="signin-password" className="text-foreground font-medium">Password</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="signin-password"
                          type={showPassword ? 'text' : 'password'}
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          placeholder="Enter your password"
                          className="pl-10 pr-10"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-3 text-muted-foreground hover:text-foreground"
                        >
                          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                    </div>

                    <Button
                      type="submit"
                      className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-medium"
                      disabled={isLoading || isSigningIn}
                    >
                      {isSigningIn ? 'Signing In...' : 'Sign In'}
                    </Button>

                    {/* Switch to Sign Up */}
                    <div className="text-center">
                      <button
                        type="button"
                        onClick={() => setShowSignUp(true)}
                        className="text-sm text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 transition-colors"
                      >
                        Need an account? Sign up
                      </button>
                    </div>
                  </form>
                )}
              </div>
            )}

            {/* Google Sign-In/Sign-Up Buttons */}
            {authMethod === 'google' && (
              <div className="w-full space-y-3">
              {showSignUp ? (
                // Sign Up Button
                <>
                  {isSigningUp || isLoading ? (
                    <div className="w-full h-12 bg-gradient-to-r from-green-600 to-blue-600 rounded-md flex items-center justify-center">
                      <div className="flex items-center space-x-2 text-white">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>Creating account...</span>
                      </div>
                    </div>
                  ) : (
                    <button
                      onClick={handleGoogleSignUp}
                      disabled={isLoading}
                      className="w-full bg-white hover:bg-gray-50 text-gray-900 font-medium py-3 px-6 rounded-lg border border-gray-300 transition-all duration-200 flex items-center justify-center space-x-3 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <svg className="w-5 h-5" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                      </svg>
                      <span>Sign up with Google</span>
                    </button>
                  )}

                  {/* Switch to Sign In */}
                  <div className="text-center">
                    <button
                      onClick={() => setShowSignUp(false)}
                      className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
                    >
                      Already have an account? Sign in
                    </button>
                  </div>
                </>
              ) : (
                // Sign In Button
                <>
                  {isSigningIn || isLoading ? (
                    <div className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-md flex items-center justify-center">
                      <div className="flex items-center space-x-2 text-white">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>Signing in...</span>
                      </div>
                    </div>
                  ) : (
                    <button
                      onClick={handleGoogleLogin}
                      disabled={isLoading}
                      className="w-full bg-white hover:bg-gray-50 text-gray-900 font-medium py-3 px-6 rounded-lg border border-gray-300 transition-all duration-200 flex items-center justify-center space-x-3 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <svg className="w-5 h-5" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                      </svg>
                      <span>Sign in with Google</span>
                    </button>
                  )}

                  {/* Switch to Sign Up */}
                  <div className="text-center">
                    <button
                      onClick={() => setShowSignUp(true)}
                      className="text-sm text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 transition-colors"
                    >
                      Need an account? Sign up
                    </button>
                  </div>
                </>
              )}
              </div>
            )}

            {/* Organization Info */}
            <div className="text-center space-y-2">
              <p className="text-xs text-muted-foreground">
                Only authorized organization domains can access this system
              </p>
              <p className="text-xs text-muted-foreground font-medium">
                Contact your administrator if you need access
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center text-xs text-muted-foreground">
          <p>© 2025 ZWIFTLLY Team Management System</p>
        </div>
      </div>
    </div>
  );
};

export default Login;
