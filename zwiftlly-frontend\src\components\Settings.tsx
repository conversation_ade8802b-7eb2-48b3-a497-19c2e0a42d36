import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Settings as SettingsIcon, Users, Building, Plus, Edit3, Trash2, Upload, Image, Key, AlertCircle, CheckCircle, Shield, User, Eye, Globe, Mail, Crown, Bot, Database, RefreshCw, BookOpen, AlertTriangle } from 'lucide-react';
import { useAuth } from '@/contexts/SupabaseAuthContext';
import { useCompany } from '@/contexts/CompanyContext';
import CustomDialog from '@/components/ui/custom-dialog';

interface User {
  id: string;
  email: string;
  name: string;
  picture?: string;
  role: 'super_admin' | 'admin' | 'manager' | 'agent' | 'viewer';
  organization: string;
  firstName?: string;
  lastName?: string;
}

interface Organization {
  id: string;
  domain: string;
  name: string;
  defaultRole: User['role'];
  adminEmails: string[];
}

const Settings: React.FC = () => {
  const {
    user: currentUser,
    hasPermission,
    getAllUsers,
    updateUser,
    deleteUser,
    getAllOrganizations,
    addOrganization,
    updateOrganization,
    deleteOrganization
  } = useAuth();

  const { companySettings, updateCompanySettings } = useCompany();

  // State management
  const [isEditUserOpen, setIsEditUserOpen] = useState(false);
  const [isAddOrgOpen, setIsAddOrgOpen] = useState(false);
  const [isEditOrgOpen, setIsEditOrgOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [deleteDialog, setDeleteDialog] = useState<{ isOpen: boolean; type: 'user' | 'org'; id: string; name: string }>({
    isOpen: false,
    type: 'user',
    id: '',
    name: ''
  });

  // New state for roles & permissions
  const [selectedUserForRole, setSelectedUserForRole] = useState<User | null>(null);
  const [showAddPermissionDialog, setShowAddPermissionDialog] = useState(false);

  // New state for API settings and mock data
  const [geminiApiKey, setGeminiApiKey] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  // Form states
  const [editUser, setEditUser] = useState<{
    email: string;
    firstName: string;
    lastName: string;
    role: 'super_admin' | 'admin' | 'manager' | 'agent' | 'viewer';
  }>({
    email: '',
    firstName: '',
    lastName: '',
    role: 'agent'
  });

  const [newOrg, setNewOrg] = useState<{
    domain: string;
    name: string;
    defaultRole: 'super_admin' | 'admin' | 'manager' | 'agent' | 'viewer';
    adminEmails: string;
  }>({
    domain: '',
    name: '',
    defaultRole: 'agent',
    adminEmails: ''
  });

  const [editOrg, setEditOrg] = useState<{
    domain: string;
    name: string;
    defaultRole: 'super_admin' | 'admin' | 'manager' | 'agent' | 'viewer';
    adminEmails: string;
  }>({
    domain: '',
    name: '',
    defaultRole: 'agent',
    adminEmails: ''
  });

  // Get data from auth context
  const [users, setUsers] = useState<User[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);

  useEffect(() => {
    const loadData = async () => {
      const usersData = await getAllUsers();
      const orgsData = await getAllOrganizations();
      setUsers(usersData);
      setOrganizations(orgsData);
    };
    loadData();

    // Load Gemini API key from localStorage
    const savedApiKey = localStorage.getItem('gemini_api_key');
    if (savedApiKey) {
      setGeminiApiKey(savedApiKey);
    }
  }, [getAllUsers, getAllOrganizations]);

  // Clear message after 5 seconds
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  // Handler functions
  const handleAddOrganization = async () => {
    if (!newOrg.domain.trim() || !newOrg.name.trim()) {
      setMessage({ type: 'error', text: 'Please fill in domain and name.' });
      return;
    }

    const adminEmails = newOrg.adminEmails
      .split(',')
      .map(email => email.trim())
      .filter(email => email.length > 0);

    const result = await addOrganization({
      domain: newOrg.domain.trim(),
      name: newOrg.name.trim(),
      default_role: newOrg.defaultRole as any,
      admin_emails: adminEmails
    });

    if (result.success) {
      const orgsData = await getAllOrganizations();
      setOrganizations(orgsData);
      setNewOrg({
        domain: '',
        name: '',
        defaultRole: 'agent',
        adminEmails: ''
      });
      setIsAddOrgOpen(false);
      setMessage({ type: 'success', text: result.message });
    } else {
      setMessage({ type: 'error', text: result.message });
    }
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setEditUser({
      email: user.email,
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      role: user.role
    });
    setIsEditUserOpen(true);
  };

  const handleEditOrganization = (org: Organization) => {
    setSelectedOrg(org);
    setEditOrg({
      domain: org.domain,
      name: org.name,
      defaultRole: org.defaultRole,
      adminEmails: org.adminEmails.join(', ')
    });
    setIsEditOrgOpen(true);
  };

  const handleUpdateUser = async () => {
    if (!selectedUser) return;

    const result = await updateUser(selectedUser.id, {
      email: editUser.email.trim(),
      firstName: editUser.firstName.trim(),
      lastName: editUser.lastName.trim(),
      role: editUser.role
    });

    if (result.success) {
      setUsers(getAllUsers());
      setIsEditUserOpen(false);
      setSelectedUser(null);
      setMessage({ type: 'success', text: result.message });
    } else {
      setMessage({ type: 'error', text: result.message });
    }
  };

  const handleUpdateOrganization = async () => {
    if (!selectedOrg) return;

    const adminEmails = editOrg.adminEmails
      .split(',')
      .map(email => email.trim())
      .filter(email => email.length > 0);

    const result = await updateOrganization(selectedOrg.id, {
      name: editOrg.name.trim(),
      defaultRole: editOrg.defaultRole,
      adminEmails
    });

    if (result.success) {
      setOrganizations(getAllOrganizations());
      setIsEditOrgOpen(false);
      setSelectedOrg(null);
      setMessage({ type: 'success', text: result.message });
    } else {
      setMessage({ type: 'error', text: result.message });
    }
  };

  const handleDeleteUser = (userId: string) => {
    const user = users.find(u => u.id === userId);
    if (user) {
      setDeleteDialog({
        isOpen: true,
        type: 'user',
        id: userId,
        name: user.name
      });
    }
  };

  const confirmDeleteUser = async () => {
    const result = await deleteUser(deleteDialog.id);

    if (result.success) {
      setUsers(getAllUsers());
      setMessage({ type: 'success', text: result.message });
    } else {
      setMessage({ type: 'error', text: result.message });
    }

    setDeleteDialog({ isOpen: false, type: 'user', id: '', name: '' });
  };

  const handleDeleteOrganization = (orgId: string) => {
    const org = organizations.find(o => o.id === orgId);
    if (org) {
      setDeleteDialog({
        isOpen: true,
        type: 'org',
        id: orgId,
        name: org.name
      });
    }
  };

  const confirmDeleteOrganization = async () => {
    const result = await deleteOrganization(deleteDialog.id);

    if (result.success) {
      setOrganizations(getAllOrganizations());
      setMessage({ type: 'success', text: result.message });
    } else {
      setMessage({ type: 'error', text: result.message });
    }

    setDeleteDialog({ isOpen: false, type: 'org', id: '', name: '' });
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        updateCompanySettings({ logo: e.target?.result as string });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSaveCompanySettings = () => {
    setMessage({ type: 'success', text: 'Company settings saved successfully!' });
  };

  const handleSaveGeminiApiKey = () => {
    localStorage.setItem('gemini_api_key', geminiApiKey);
    setMessage({ type: 'success', text: 'Google Gemini API key saved successfully!' });
  };

  const handleDeleteAllMockData = () => {
    localStorage.removeItem('zwiftlly_google_users');
    localStorage.removeItem('zwiftlly_organizations');
    setUsers([]);
    setOrganizations([]);
    setMessage({ type: 'success', text: 'All mock data deleted successfully!' });
    setShowDeleteConfirm(null);
  };

  const handleSimulateAllMockData = () => {
    // Simulate mock data creation
    const mockUsers = [
      { id: '1', name: 'John Doe', email: '<EMAIL>', role: 'agent' as const, organization: 'ZWIFTLLY' },
      { id: '2', name: 'Jane Smith', email: '<EMAIL>', role: 'manager' as const, organization: 'ZWIFTLLY' },
      { id: '3', name: 'Bob Johnson', email: '<EMAIL>', role: 'admin' as const, organization: 'ZWIFTLLY' }
    ];

    localStorage.setItem('zwiftlly_google_users', JSON.stringify(mockUsers.reduce((acc, user) => {
      acc[user.email] = user;
      return acc;
    }, {} as Record<string, any>)));

    setUsers(getAllUsers());
    setMessage({ type: 'success', text: 'Mock data simulated successfully!' });
    setShowDeleteConfirm(null);
  };

  const handleDeleteUserData = () => {
    if (selectedUser) {
      deleteUser(selectedUser.id);
      setUsers(getAllUsers());
      setSelectedUser(null);
      setMessage({ type: 'success', text: 'User data deleted successfully!' });
    }
    setShowDeleteConfirm(null);
  };

  const handleDeleteKnowledgeBase = () => {
    // Simulate knowledge base deletion
    localStorage.removeItem('zwiftlly_knowledge_base');
    setMessage({ type: 'success', text: 'Knowledge base data deleted successfully!' });
    setShowDeleteConfirm(null);
  };

  const getPermissionsForRole = (role: string): string[] => {
    const permissions = {
      super_admin: [
        'Full System Control',
        'Manage All Users',
        'Manage All Organizations',
        'System Configuration',
        'Database Access',
        'Security Settings',
        'Audit Logs',
        'Backup & Recovery'
      ],
      admin: [
        'Manage Users',
        'Manage Organizations',
        'Access All Features',
        'System Settings',
        'View Reports',
        'Manage Roles'
      ],
      manager: [
        'Manage Team Members',
        'View Team Performance',
        'Assign Tasks',
        'Access Reports',
        'Approve Requests',
        'Schedule Management'
      ],
      agent: [
        'View Dashboard',
        'Manage Own Tasks',
        'Update Profile',
        'Access Knowledge Base',
        'Submit Requests',
        'View Team Status'
      ],
      viewer: [
        'View Dashboard',
        'View Team Status',
        'Read Knowledge Base',
        'Limited Access'
      ]
    };

    return permissions[role as keyof typeof permissions] || [];
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'manager': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'agent': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'viewer': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  // Permission check
  if (!hasPermission('manage_users') && !hasPermission('all')) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="text-6xl">🔒</div>
          <h2 className="text-2xl font-bold text-foreground">Access Denied</h2>
          <p className="text-muted-foreground">
            You don't have permission to access the Settings page.
          </p>
          <p className="text-sm text-muted-foreground">
            Current role: <span className="font-medium capitalize">{currentUser?.role}</span>
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8 animate-fade-in-up animate-delay-0">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Settings
          </h1>
          <p className="text-muted-foreground">
            Manage system settings, users, and company information
          </p>
        </div>

        {/* Message Display */}
        {message && (
          <div className={`mb-6 p-4 rounded-lg border flex items-center space-x-2 ${
            message.type === 'success'
              ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-200'
              : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200'
          }`}>
            {message.type === 'success' ? (
              <CheckCircle className="w-5 h-5 flex-shrink-0" />
            ) : (
              <AlertCircle className="w-5 h-5 flex-shrink-0" />
            )}
            <span>{message.text}</span>
          </div>
        )}

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Company Settings */}
          <Card className="bg-card rounded-xl shadow-lg border border-border/50 animate-slide-in-right animate-delay-150">
            <CardHeader className="p-6 border-b border-border/50 bg-gradient-to-r from-background to-muted/20">
              <CardTitle className="text-xl flex items-center space-x-3">
                <div className="p-3 bg-muted/20 rounded-xl shadow-sm border border-border/30">
                  <Building className="w-6 h-6 text-foreground" />
                </div>
                <div>
                  <span className="text-foreground font-bold text-xl">Company Settings</span>
                  <div className="text-sm text-muted-foreground font-medium">Update company information</div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-6">
              {/* Company Logo */}
              <div className="space-y-4">
                <Label className="text-foreground font-semibold">Company Logo</Label>
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-muted rounded-xl flex items-center justify-center border border-border/50 overflow-hidden">
                    {companySettings.logo ? (
                      <img src={companySettings.logo} alt="Company Logo" className="w-full h-full object-cover" />
                    ) : (
                      <Image className="w-8 h-8 text-muted-foreground" />
                    )}
                  </div>
                  <div>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="hidden"
                      id="logo-upload"
                    />
                    <Label htmlFor="logo-upload" className="cursor-pointer">
                      <Button variant="outline" className="border-border/50" asChild>
                        <span>
                          <Upload className="w-4 h-4 mr-2" />
                          Upload Logo
                        </span>
                      </Button>
                    </Label>
                  </div>
                </div>
              </div>

              {/* Company Name */}
              <div className="space-y-2">
                <Label htmlFor="companyName" className="text-foreground font-semibold">Company Name</Label>
                <Input
                  id="companyName"
                  value={companySettings.name}
                  onChange={(e) => updateCompanySettings({ name: e.target.value })}
                  placeholder="Enter company name"
                  className="bg-background border-border/50"
                />
              </div>

              <Button
                onClick={handleSaveCompanySettings}
                className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
              >
                Save Company Settings
              </Button>
            </CardContent>
          </Card>

          {/* Organization Management */}
          <Card className="bg-card rounded-xl shadow-lg border border-border/50 animate-slide-in-right animate-delay-200">
            <CardHeader className="p-6 border-b border-border/50 bg-gradient-to-r from-background to-muted/20">
              <CardTitle className="text-xl flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-3 bg-muted/20 rounded-xl shadow-sm border border-border/30">
                    <Globe className="w-6 h-6 text-foreground" />
                  </div>
                  <div>
                    <span className="text-foreground font-bold text-xl">Organizations</span>
                    <div className="text-sm text-muted-foreground font-medium">Manage allowed domains</div>
                  </div>
                </div>
                <Button
                  onClick={() => setIsAddOrgOpen(true)}
                  size="sm"
                  className="bg-primary hover:bg-primary/90 text-primary-foreground"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Domain
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="max-h-96 overflow-y-auto">
                {organizations.map((org, index) => (
                  <div key={org.id} className={`p-4 border-b border-border/50 hover:bg-muted/10 transition-colors ${
                    index === organizations.length - 1 ? 'border-b-0' : ''
                  }`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
                          <Globe className="w-5 h-5 text-foreground" />
                        </div>
                        <div>
                          <div className="font-semibold text-foreground">{org.name}</div>
                          <div className="text-sm text-muted-foreground">{org.domain}</div>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className={`text-xs px-2 py-1 rounded-full font-medium ${getRoleColor(org.defaultRole)}`}>
                              Default: {org.defaultRole.toUpperCase()}
                            </span>
                            <span className="text-xs px-2 py-1 rounded-full font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300">
                              {org.adminEmails.length} Admin{org.adminEmails.length !== 1 ? 's' : ''}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="w-8 h-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/20"
                          onClick={() => handleEditOrganization(org)}
                          title="Edit Organization"
                        >
                          <Edit3 className="w-3 h-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="w-8 h-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-100 dark:hover:bg-red-900/20"
                          onClick={() => handleDeleteOrganization(org.id)}
                          title="Delete Organization"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* User Management */}
          <Card className="bg-card rounded-xl shadow-lg border border-border/50 animate-slide-in-right animate-delay-300">
            <CardHeader className="p-6 border-b border-border/50 bg-gradient-to-r from-background to-muted/20">
              <CardTitle className="text-xl flex items-center space-x-3">
                <div className="p-3 bg-muted/20 rounded-xl shadow-sm border border-border/30">
                  <Users className="w-6 h-6 text-foreground" />
                </div>
                <div>
                  <span className="text-foreground font-bold text-xl">Google OAuth Users</span>
                  <div className="text-sm text-muted-foreground font-medium">Users who signed in via Google</div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="max-h-96 overflow-y-auto">
                {users.map((user, index) => (
                  <div key={user.id} className={`p-4 border-b border-border/50 hover:bg-muted/10 transition-colors ${
                    index === users.length - 1 ? 'border-b-0' : ''
                  }`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center overflow-hidden">
                          {user.picture ? (
                            <img src={user.picture} alt={user.name} className="w-full h-full object-cover" />
                          ) : (
                            <span className="text-sm font-bold text-foreground">
                              {user.name.charAt(0).toUpperCase()}
                            </span>
                          )}
                        </div>
                        <div>
                          <div className="font-semibold text-foreground">{user.name}</div>
                          <div className="text-sm text-muted-foreground">{user.email}</div>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className={`text-xs px-2 py-1 rounded-full font-medium ${getRoleColor(user.role)}`}>
                              {user.role.toUpperCase()}
                            </span>
                            <span className="text-xs px-2 py-1 rounded-full font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300">
                              {user.organization}
                            </span>
                            {currentUser?.id === user.id && (
                              <span className="text-xs px-2 py-1 rounded-full font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300">
                                YOU
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="w-8 h-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/20"
                          onClick={() => handleEditUser(user)}
                          title="Edit User Role"
                        >
                          <Edit3 className="w-3 h-3" />
                        </Button>
                        {currentUser?.id !== user.id && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="w-8 h-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-100 dark:hover:bg-red-900/20"
                            onClick={() => handleDeleteUser(user.id)}
                            title="Delete User"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Roles Permissions - 3 Column Layout - Full Width */}
          <Card className="bg-card rounded-xl shadow-lg border border-border/50 animate-slide-in-right animate-delay-400 col-span-full">
            <CardHeader className="p-6 border-b border-border/50 bg-gradient-to-r from-background to-muted/20">
              <CardTitle className="text-xl flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-3 bg-muted/20 rounded-xl shadow-sm border border-border/30">
                    <Shield className="w-6 h-6 text-foreground" />
                  </div>
                  <div>
                    <span className="text-foreground font-bold text-xl">Roles & Permissions</span>
                    <div className="text-sm text-muted-foreground font-medium">Manage user roles and permissions</div>
                  </div>
                </div>
                <Button
                  onClick={() => setShowAddPermissionDialog(true)}
                  className="gradient-button-primary hover:opacity-90 text-primary-foreground"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Permission
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8 min-h-[500px]">
                {/* Left Column - Users List */}
                <div className="border-r border-border/50 pr-6">
                  <h3 className="font-semibold text-foreground mb-6 text-lg">Users</h3>
                  <div className="space-y-3 max-h-[420px] overflow-y-auto">
                    {users.map((user) => (
                      <div
                        key={user.id}
                        onClick={() => setSelectedUserForRole(user)}
                        className={`p-4 rounded-xl border cursor-pointer transition-all duration-200 hover:shadow-md ${
                          selectedUserForRole?.id === user.id
                            ? 'bg-primary/10 border-primary/30 shadow-sm'
                            : 'bg-muted/20 border-border/30 hover:bg-muted/40'
                        }`}
                      >
                        <div className="flex items-center space-x-4">
                          <div className="relative">
                            {user.picture ? (
                              <img
                                src={user.picture}
                                alt={user.name}
                                className="w-12 h-12 rounded-full object-cover border-2 border-border/20"
                              />
                            ) : (
                              <div className="w-12 h-12 gradient-primary rounded-full flex items-center justify-center border-2 border-border/20">
                                <span className="text-primary-foreground text-lg font-bold">
                                  {user.name.charAt(0)}
                                </span>
                              </div>
                            )}
                            {/* Online status indicator */}
                            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-background"></div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="font-semibold text-foreground text-base truncate">{user.name}</div>
                            <div className="text-sm text-muted-foreground truncate">{user.email}</div>
                            <div className="text-xs text-muted-foreground mt-1 capitalize">
                              {user.role.replace('_', ' ')}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Middle Column - User Role */}
                <div className="border-r border-border/50 pr-6">
                  <h3 className="font-semibold text-foreground mb-6 text-lg">Role</h3>
                  {selectedUserForRole ? (
                    <div className="space-y-6">
                      <div className="p-6 bg-muted/20 rounded-xl border border-border/30">
                        <div className="flex items-center space-x-4 mb-6">
                          <div className="relative">
                            {selectedUserForRole.picture ? (
                              <img
                                src={selectedUserForRole.picture}
                                alt={selectedUserForRole.name}
                                className="w-16 h-16 rounded-full object-cover border-2 border-border/20"
                              />
                            ) : (
                              <div className="w-16 h-16 gradient-primary rounded-full flex items-center justify-center border-2 border-border/20">
                                <span className="text-primary-foreground text-xl font-bold">
                                  {selectedUserForRole.name.charAt(0)}
                                </span>
                              </div>
                            )}
                          </div>
                          <div>
                            <div className="font-bold text-foreground text-lg">{selectedUserForRole.name}</div>
                            <div className="text-sm text-muted-foreground">{selectedUserForRole.email}</div>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <div className="flex items-center space-x-3 p-3 bg-background/50 rounded-lg">
                            {selectedUserForRole.role === 'super_admin' && <Crown className="w-6 h-6 text-purple-600" />}
                            {selectedUserForRole.role === 'admin' && <Crown className="w-6 h-6 text-red-600" />}
                            {selectedUserForRole.role === 'manager' && <Users className="w-6 h-6 text-blue-600" />}
                            {selectedUserForRole.role === 'agent' && <User className="w-6 h-6 text-green-600" />}
                            {selectedUserForRole.role === 'viewer' && <Eye className="w-6 h-6 text-gray-600" />}
                            <div>
                              <div className="font-bold text-foreground text-lg capitalize">
                                {selectedUserForRole.role.replace('_', ' ')}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {selectedUserForRole.role === 'super_admin' && 'Ultimate system control'}
                                {selectedUserForRole.role === 'admin' && 'Full system access'}
                                {selectedUserForRole.role === 'manager' && 'Team management access'}
                                {selectedUserForRole.role === 'agent' && 'Standard user access'}
                                {selectedUserForRole.role === 'viewer' && 'Read-only access'}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center text-muted-foreground py-16">
                      <Shield className="w-16 h-16 mx-auto mb-4 opacity-50" />
                      <p className="text-lg">Select a user to view their role</p>
                      <p className="text-sm mt-2">Click on any user from the left panel</p>
                    </div>
                  )}
                </div>

                {/* Right Column - Permissions */}
                <div>
                  <h3 className="font-semibold text-foreground mb-6 text-lg">Permissions</h3>
                  {selectedUserForRole ? (
                    <div className="space-y-3 max-h-[420px] overflow-y-auto">
                      {getPermissionsForRole(selectedUserForRole.role).map((permission, index) => (
                        <div
                          key={index}
                          className="p-4 bg-muted/20 rounded-xl border border-border/30 hover:bg-muted/30 transition-colors"
                        >
                          <div className="flex items-center space-x-3">
                            <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                            <span className="text-foreground font-medium">{permission}</span>
                          </div>
                        </div>
                      ))}

                      {/* Permission count summary */}
                      <div className="mt-6 p-4 bg-primary/10 rounded-xl border border-primary/20">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-foreground">Total Permissions</span>
                          <span className="text-lg font-bold text-primary">
                            {getPermissionsForRole(selectedUserForRole.role).length}
                          </span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center text-muted-foreground py-16">
                      <Key className="w-16 h-16 mx-auto mb-4 opacity-50" />
                      <p className="text-lg">Select a user to view permissions</p>
                      <p className="text-sm mt-2">User permissions will appear here</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Google Gemini AI Settings */}
          <Card className="bg-card rounded-xl shadow-lg border border-border/50 animate-slide-in-right animate-delay-500">
            <CardHeader className="p-6 border-b border-border/50 bg-gradient-to-r from-background to-muted/20">
              <CardTitle className="text-xl flex items-center space-x-3">
                <div className="p-3 bg-muted/20 rounded-xl shadow-sm border border-border/30">
                  <Bot className="w-6 h-6 text-foreground" />
                </div>
                <div>
                  <span className="text-foreground font-bold text-xl">Google Gemini AI</span>
                  <div className="text-sm text-muted-foreground font-medium">Configure AI Assistant settings</div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-4">
              <div className="space-y-2">
                <Label htmlFor="geminiApiKey" className="text-foreground font-semibold">API Key</Label>
                <Input
                  id="geminiApiKey"
                  type="password"
                  value={geminiApiKey}
                  onChange={(e) => setGeminiApiKey(e.target.value)}
                  placeholder="Enter your Google Gemini API key"
                  className="bg-background border-border/50"
                />
                <p className="text-xs text-muted-foreground">
                  Get your API key from <a href="https://makersuite.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Google AI Studio</a>
                </p>
              </div>

              <Button
                onClick={handleSaveGeminiApiKey}
                className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
              >
                Save API Key
              </Button>
            </CardContent>
          </Card>

          {/* Danger Zone */}
          <Card className="bg-card rounded-xl shadow-lg border border-red-200 dark:border-red-800 animate-slide-in-right animate-delay-600">
            <CardHeader className="p-6 border-b border-red-200 dark:border-red-800 bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20">
              <CardTitle className="text-xl flex items-center space-x-3">
                <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl shadow-sm border border-red-200 dark:border-red-800">
                  <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <span className="text-red-700 dark:text-red-300 font-bold text-xl">Danger Zone</span>
                  <div className="text-sm text-red-600 dark:text-red-400 font-medium">Destructive actions - use with caution</div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  onClick={() => setShowDeleteConfirm('all_mock')}
                  variant="destructive"
                  className="w-full"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete All Mock Data
                </Button>

                <Button
                  onClick={() => setShowDeleteConfirm('simulate')}
                  variant="outline"
                  className="w-full border-border/50"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Simulate All Mock Data
                </Button>

                <Button
                  onClick={() => setShowDeleteConfirm('user_data')}
                  variant="destructive"
                  className="w-full"
                  disabled={!selectedUser}
                >
                  <User className="w-4 h-4 mr-2" />
                  Delete User Data
                </Button>

                <Button
                  onClick={() => setShowDeleteConfirm('knowledge_base')}
                  variant="destructive"
                  className="w-full"
                >
                  <BookOpen className="w-4 h-4 mr-2" />
                  Delete Knowledge Base
                </Button>
              </div>

              {selectedUser && (
                <div className="text-sm text-muted-foreground">
                  Selected user: <strong>{selectedUser.name}</strong>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Add Organization Modal */}
        <Dialog open={isAddOrgOpen} onOpenChange={setIsAddOrgOpen}>
          <DialogContent className="max-w-md bg-card border border-border/50">
            <DialogHeader>
              <DialogTitle className="text-foreground">Add Organization Domain</DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <Label htmlFor="domain" className="text-foreground">Email Domain *</Label>
                <Input
                  id="domain"
                  value={newOrg.domain}
                  onChange={(e) => setNewOrg({...newOrg, domain: e.target.value})}
                  placeholder="example.com"
                  className="bg-background border-border/50"
                />
                <p className="text-xs text-muted-foreground mt-1">Users with this email domain can sign in</p>
              </div>

              <div>
                <Label htmlFor="orgName" className="text-foreground">Organization Name *</Label>
                <Input
                  id="orgName"
                  value={newOrg.name}
                  onChange={(e) => setNewOrg({...newOrg, name: e.target.value})}
                  placeholder="Organization Name"
                  className="bg-background border-border/50"
                />
              </div>

              <div>
                <Label htmlFor="defaultRole" className="text-foreground">Default Role *</Label>
                <Select value={newOrg.defaultRole} onValueChange={(value: any) => setNewOrg({...newOrg, defaultRole: value})}>
                  <SelectTrigger className="bg-background border-border/50">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="super_admin">Super Admin - Ultimate Control</SelectItem>
                    <SelectItem value="admin">Admin - Full Access</SelectItem>
                    <SelectItem value="manager">Manager - User Management</SelectItem>
                    <SelectItem value="agent">Agent - Standard Access</SelectItem>
                    <SelectItem value="viewer">Viewer - Read Only</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">Default role for new users from this domain</p>
              </div>

              <div>
                <Label htmlFor="adminEmails" className="text-foreground">Admin Emails</Label>
                <Input
                  id="adminEmails"
                  value={newOrg.adminEmails}
                  onChange={(e) => setNewOrg({...newOrg, adminEmails: e.target.value})}
                  placeholder="<EMAIL>, <EMAIL>"
                  className="bg-background border-border/50"
                />
                <p className="text-xs text-muted-foreground mt-1">Comma-separated emails that get admin role</p>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddOrgOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddOrganization} className="bg-primary hover:bg-primary/90">
                Add Organization
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit User Modal */}
        <Dialog open={isEditUserOpen} onOpenChange={setIsEditUserOpen}>
          <DialogContent className="max-w-md bg-card border border-border/50">
            <DialogHeader>
              <DialogTitle className="text-foreground">Edit User Role</DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <div className="p-3 bg-muted/20 rounded-lg border border-border/30">
                <p className="text-sm text-foreground">
                  Editing: <span className="font-semibold">{selectedUser?.name}</span>
                </p>
                <p className="text-xs text-muted-foreground">{selectedUser?.email}</p>
              </div>

              <div>
                <Label htmlFor="editRole" className="text-foreground">Role</Label>
                <Select value={editUser.role} onValueChange={(value: any) => setEditUser({...editUser, role: value})}>
                  <SelectTrigger className="bg-background border-border/50">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="super_admin">
                      <div className="flex items-center space-x-2">
                        <Crown className="w-4 h-4 text-purple-600" />
                        <span>Super Admin - Ultimate Control</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="admin">
                      <div className="flex items-center space-x-2">
                        <Crown className="w-4 h-4 text-red-600" />
                        <span>Admin - Full Access</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="manager">
                      <div className="flex items-center space-x-2">
                        <Users className="w-4 h-4" />
                        <span>Manager - User Management</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="agent">
                      <div className="flex items-center space-x-2">
                        <User className="w-4 h-4" />
                        <span>Agent - Standard Access</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="viewer">
                      <div className="flex items-center space-x-2">
                        <Eye className="w-4 h-4" />
                        <span>Viewer - Read Only</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">
                  Note: User information is managed by Google OAuth
                </p>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditUserOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateUser} className="bg-primary hover:bg-primary/90">
                Update Role
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Organization Modal */}
        <Dialog open={isEditOrgOpen} onOpenChange={setIsEditOrgOpen}>
          <DialogContent className="max-w-md bg-card border border-border/50">
            <DialogHeader>
              <DialogTitle className="text-foreground">Edit Organization</DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <div className="p-3 bg-muted/20 rounded-lg border border-border/30">
                <p className="text-sm text-foreground">
                  Domain: <span className="font-semibold">{selectedOrg?.domain}</span>
                </p>
                <p className="text-xs text-muted-foreground">Domain cannot be changed</p>
              </div>

              <div>
                <Label htmlFor="editOrgName" className="text-foreground">Organization Name</Label>
                <Input
                  id="editOrgName"
                  value={editOrg.name}
                  onChange={(e) => setEditOrg({...editOrg, name: e.target.value})}
                  placeholder="Organization Name"
                  className="bg-background border-border/50"
                />
              </div>

              <div>
                <Label htmlFor="editDefaultRole" className="text-foreground">Default Role</Label>
                <Select value={editOrg.defaultRole} onValueChange={(value: any) => setEditOrg({...editOrg, defaultRole: value})}>
                  <SelectTrigger className="bg-background border-border/50">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="super_admin">Super Admin - Ultimate Control</SelectItem>
                    <SelectItem value="admin">Admin - Full Access</SelectItem>
                    <SelectItem value="manager">Manager - User Management</SelectItem>
                    <SelectItem value="agent">Agent - Standard Access</SelectItem>
                    <SelectItem value="viewer">Viewer - Read Only</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="editAdminEmails" className="text-foreground">Admin Emails</Label>
                <Input
                  id="editAdminEmails"
                  value={editOrg.adminEmails}
                  onChange={(e) => setEditOrg({...editOrg, adminEmails: e.target.value})}
                  placeholder="<EMAIL>, <EMAIL>"
                  className="bg-background border-border/50"
                />
                <p className="text-xs text-muted-foreground mt-1">Comma-separated emails that get admin role</p>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditOrgOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateOrganization} className="bg-primary hover:bg-primary/90">
                Update Organization
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <CustomDialog
          isOpen={deleteDialog.isOpen}
          onClose={() => setDeleteDialog({ isOpen: false, type: 'user', id: '', name: '' })}
          title={`Delete ${deleteDialog.type === 'user' ? 'User' : 'Organization'}`}
          message={`Are you sure you want to delete "${deleteDialog.name}"? ${
            deleteDialog.type === 'user'
              ? 'This action cannot be undone.'
              : 'This will prevent users from this domain from logging in.'
          }`}
          type="error"
          onConfirm={deleteDialog.type === 'user' ? confirmDeleteUser : confirmDeleteOrganization}
          confirmText="Delete"
          cancelText="Cancel"
        />

        {/* Mock Data Management Confirmation Dialog */}
        <CustomDialog
          isOpen={!!showDeleteConfirm}
          onClose={() => setShowDeleteConfirm(null)}
          title={
            showDeleteConfirm === 'all_mock' ? 'Delete All Mock Data' :
            showDeleteConfirm === 'simulate' ? 'Simulate Mock Data' :
            showDeleteConfirm === 'user_data' ? 'Delete User Data' :
            showDeleteConfirm === 'knowledge_base' ? 'Delete Knowledge Base' : ''
          }
          message={
            showDeleteConfirm === 'all_mock' ? 'This will delete all mock users and organizations. This action cannot be undone.' :
            showDeleteConfirm === 'simulate' ? 'This will create sample users and organizations for testing purposes.' :
            showDeleteConfirm === 'user_data' ? `Delete all data for user "${selectedUser?.name}"? This action cannot be undone.` :
            showDeleteConfirm === 'knowledge_base' ? 'This will delete all knowledge base articles and documents. This action cannot be undone.' : ''
          }
          type={showDeleteConfirm === 'simulate' ? 'info' : 'error'}
          onConfirm={
            showDeleteConfirm === 'all_mock' ? handleDeleteAllMockData :
            showDeleteConfirm === 'simulate' ? handleSimulateAllMockData :
            showDeleteConfirm === 'user_data' ? handleDeleteUserData :
            showDeleteConfirm === 'knowledge_base' ? handleDeleteKnowledgeBase : undefined
          }
          confirmText={showDeleteConfirm === 'simulate' ? 'Simulate' : 'Delete'}
          cancelText="Cancel"
        />

        {/* Add Permission Dialog */}
        <Dialog open={showAddPermissionDialog} onOpenChange={setShowAddPermissionDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <Plus className="w-5 h-5 text-blue-600" />
                </div>
                <span>Add Role & Permissions</span>
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="selectUser">Select User</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a user" />
                  </SelectTrigger>
                  <SelectContent>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        {user.name} ({user.email})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="selectRole">Assign Role</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="super_admin">Super Admin</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="manager">Manager</SelectItem>
                    <SelectItem value="agent">Agent</SelectItem>
                    <SelectItem value="viewer">Viewer</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/10 p-3 rounded-lg border border-blue-200 dark:border-blue-800/30">
                <p className="text-sm text-blue-800 dark:text-blue-300">
                  <strong>Note:</strong> Permissions are automatically assigned based on the selected role.
                  Super Admin has the highest level of access.
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowAddPermissionDialog(false)}>
                Cancel
              </Button>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                Assign Role
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default Settings;
