@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100;
  }
  :root {
    --background: 0 0% 100%; /* White */
    --foreground: 0 0% 15%; /* Light Black */
    --card: 0 0% 100%; /* White */
    --card-foreground: 0 0% 15%; /* Light Black */
    --popover: 0 0% 100%; /* White */
    --popover-foreground: 0 0% 15%; /* Light Black */
    --primary: 0 0% 15%; /* Light Black */
    --primary-foreground: 0 0% 100%; /* White */
    --secondary: 0 0% 96%; /* Light Grey */
    --secondary-foreground: 0 0% 15%; /* Light Black */
    --muted: 0 0% 96%; /* Light Grey */
    --muted-foreground: 0 0% 45%; /* Grey */
    --accent: 0 0% 96%; /* Light Grey */
    --accent-foreground: 0 0% 15%; /* Light Black */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 90%; /* Light Grey border */
    --input: 0 0% 90%; /* Light Grey input */
    --ring: 0 0% 15%; /* Light Black ring */
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 7%; /* Dark Grey */
    --foreground: 0 0% 95%; /* Light Grey */
    --card: 0 0% 7%; /* Dark Grey */
    --card-foreground: 0 0% 95%; /* Light Grey */
    --popover: 0 0% 7%; /* Dark Grey */
    --popover-foreground: 0 0% 95%; /* Light Grey */
    --primary: 0 0% 95%; /* Light Grey */
    --primary-foreground: 0 0% 7%; /* Dark Grey */
    --secondary: 0 0% 15%; /* Darker Grey */
    --secondary-foreground: 0 0% 95%; /* Light Grey */
    --muted: 0 0% 15%; /* Darker Grey */
    --muted-foreground: 0 0% 65%; /* Medium Grey */
    --accent: 0 0% 15%; /* Darker Grey */
    --accent-foreground: 0 0% 95%; /* Light Grey */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 20%; /* Dark Grey border */
    --input: 0 0% 20%; /* Dark Grey input */
    --ring: 0 0% 95%; /* Light Grey ring */
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700;
  }

  .sidebar-item {
    @apply flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200;
  }

  .sidebar-item.active {
    @apply bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300;
  }

  .dashboard-widget {
    @apply transition-all duration-300 ease-out;
  }

  .animate-delay-0 {
    animation-delay: 0ms;
  }

  .animate-delay-150 {
    animation-delay: 150ms;
  }

  .animate-delay-300 {
    animation-delay: 300ms;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
