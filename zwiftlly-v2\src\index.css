@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100;
  }
  :root {
    --background: 0 0% 100%; /* White */
    --foreground: 0 0% 15%; /* Light Black */
    --card: 0 0% 100%; /* White */
    --card-foreground: 0 0% 15%; /* Light Black */
    --popover: 0 0% 100%; /* White */
    --popover-foreground: 0 0% 15%; /* Light Black */
    --primary: 0 0% 15%; /* Light Black */
    --primary-foreground: 0 0% 100%; /* White */
    --secondary: 0 0% 96%; /* Light Grey */
    --secondary-foreground: 0 0% 15%; /* Light Black */
    --muted: 0 0% 96%; /* Light Grey */
    --muted-foreground: 0 0% 45%; /* Grey */
    --accent: 0 0% 96%; /* Light Grey */
    --accent-foreground: 0 0% 15%; /* Light Black */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 90%; /* Light Grey border */
    --input: 0 0% 90%; /* Light Grey input */
    --ring: 0 0% 15%; /* Light Black ring */
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 7%; /* Dark Grey */
    --foreground: 0 0% 95%; /* Light Grey */
    --card: 0 0% 7%; /* Dark Grey */
    --card-foreground: 0 0% 95%; /* Light Grey */
    --popover: 0 0% 7%; /* Dark Grey */
    --popover-foreground: 0 0% 95%; /* Light Grey */
    --primary: 0 0% 95%; /* Light Grey */
    --primary-foreground: 0 0% 7%; /* Dark Grey */
    --secondary: 0 0% 15%; /* Darker Grey */
    --secondary-foreground: 0 0% 95%; /* Light Grey */
    --muted: 0 0% 15%; /* Darker Grey */
    --muted-foreground: 0 0% 65%; /* Medium Grey */
    --accent: 0 0% 15%; /* Darker Grey */
    --accent-foreground: 0 0% 95%; /* Light Grey */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 20%; /* Dark Grey border */
    --input: 0 0% 20%; /* Dark Grey input */
    --ring: 0 0% 95%; /* Light Grey ring */
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    @apply bg-card text-card-foreground rounded-lg shadow-sm border border-border;
  }

  .sidebar-item {
    @apply flex items-center px-4 py-3 text-muted-foreground hover:bg-accent hover:text-accent-foreground rounded-lg transition-colors duration-200;
  }

  .sidebar-item.active {
    @apply bg-accent text-accent-foreground;
  }

  .dashboard-widget {
    @apply transition-all duration-300 ease-out;
  }

  .animate-delay-0 {
    animation-delay: 0ms;
  }

  .animate-delay-150 {
    animation-delay: 150ms;
  }

  .animate-delay-300 {
    animation-delay: 300ms;
  }

  /* Theme-consistent button styles */
  .theme-button-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md;
  }

  .theme-button-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md;
  }

  /* Animation utilities */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animate-fade-in {
    animation: fadeIn 0.6s ease-out forwards;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out forwards;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.6s ease-out forwards;
  }

  /* Background animations for login page */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: float 6s ease-in-out infinite;
    animation-delay: 2s;
  }

  .animate-grid-move {
    animation: gridMove 20s linear infinite;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Keyframe animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

/* Animation delay utilities */
.animation-delay-500 { animation-delay: 0.5s; }
.animation-delay-1000 { animation-delay: 1s; }
.animation-delay-1500 { animation-delay: 1.5s; }
.animation-delay-2000 { animation-delay: 2s; }
.animation-delay-2500 { animation-delay: 2.5s; }
.animation-delay-3000 { animation-delay: 3s; }
.animation-delay-3500 { animation-delay: 3.5s; }
