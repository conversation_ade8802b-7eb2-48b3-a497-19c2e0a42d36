import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Clock, Coffee, Wrench, Users, LogOut, User } from 'lucide-react';
import { useAuth } from '@/contexts/SupabaseAuthContext';

interface ClockSystemDropdownProps {
  onClockAction: (action: string) => void;
  currentStatus?: string;
  clockInTime?: string;
  onProfileClick?: () => void;
}

const ClockSystemDropdown: React.FC<ClockSystemDropdownProps> = ({
  onClockAction,
  currentStatus = 'offline',
  clockInTime = '8:00 AM',
  onProfileClick
}) => {
  const { user, logout } = useAuth();
  const [profileData, setProfileData] = useState<{
    firstName: string;
    lastName: string;
    companyRole: string;
  } | null>(null);

  // Load saved profile data
  useEffect(() => {
    if (user) {
      const savedProfile = localStorage.getItem(`profile_data_${user.id}`);
      if (savedProfile) {
        try {
          const parsed = JSON.parse(savedProfile);
          setProfileData({
            firstName: parsed.firstName || user.firstName || '',
            lastName: parsed.lastName || user.lastName || '',
            companyRole: parsed.companyRole || ''
          });
        } catch (error) {
          console.error('Error parsing saved profile:', error);
        }
      }
    }
  }, [user]);

  // Get display name and role
  const getDisplayName = () => {
    if (profileData && profileData.firstName && profileData.lastName) {
      return `${profileData.firstName} ${profileData.lastName}`;
    }
    return user?.name || 'User';
  };

  const getDisplayRole = () => {
    if (profileData && profileData.companyRole) {
      return profileData.companyRole;
    }
    return user?.role?.replace('_', ' ') || 'Team Member';
  };
  const clockActions = [
    {
      id: 'clock-in',
      label: 'Clock In',
      icon: Clock,
      color: 'bg-slate-700 hover:bg-slate-600 border-l-4 border-l-emerald-500',
      textColor: 'text-white'
    },
    {
      id: 'break',
      label: 'Break',
      icon: Coffee,
      color: 'bg-slate-700 hover:bg-slate-600 border-l-4 border-l-amber-500',
      textColor: 'text-white'
    },
    {
      id: 'technical',
      label: 'Technical Issue',
      icon: Wrench,
      color: 'bg-slate-700 hover:bg-slate-600 border-l-4 border-l-blue-500',
      textColor: 'text-white'
    },
    {
      id: 'meeting',
      label: 'Meeting',
      icon: Users,
      color: 'bg-slate-700 hover:bg-slate-600 border-l-4 border-l-purple-500',
      textColor: 'text-white'
    },
    {
      id: 'clock-out',
      label: 'Clock Out',
      icon: LogOut,
      color: 'bg-slate-700 hover:bg-slate-600 border-l-4 border-l-rose-500',
      textColor: 'text-white'
    }
  ];

  const handleAction = (actionId: string) => {
    onClockAction(actionId);
  };

  return (
    <div className="w-72 bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-lg shadow-lg p-6">
      {/* Profile Section */}
      <div className="pb-6 border-b border-slate-200 dark:border-slate-700">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-lg font-semibold text-slate-900 dark:text-white">{getDisplayName()}</div>
            <div className="text-sm text-slate-600 dark:text-slate-400 mt-1 capitalize">{getDisplayRole()}</div>
          </div>
          <Button
            onClick={onProfileClick}
            variant="outline"
            size="lg"
            className="w-16 h-16 p-0 rounded-full border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors"
            title="View Profile"
          >
            <User className="w-8 h-8 text-slate-600 dark:text-slate-400" />
          </Button>
        </div>
      </div>

      {/* Clock Actions */}
      <div className="pt-6 pb-6 space-y-2 border-b border-slate-200 dark:border-slate-700">
        {clockActions.map((action) => {
          const IconComponent = action.icon;
          return (
            <Button
              key={action.id}
              onClick={() => handleAction(action.id)}
              className={`w-full ${action.color} ${action.textColor} flex items-center justify-start space-x-3 h-11 rounded-md transition-all duration-200 hover:translate-x-1 shadow-sm border-0 font-medium text-sm`}
            >
              <IconComponent className="w-4 h-4 ml-2" />
              <span>{action.label}</span>
            </Button>
          );
        })}
      </div>

      {/* Quick Stats */}
      <div className="py-6 space-y-4 border-b border-slate-200 dark:border-slate-700">
        <div className="text-sm font-semibold text-slate-900 dark:text-white">Today's Summary</div>
        <div className="space-y-3 text-sm">
          <div className="flex justify-between items-center">
            <span className="text-slate-600 dark:text-slate-400">Hours Worked:</span>
            <span className="font-semibold text-slate-900 dark:text-white">6h 45m</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-slate-600 dark:text-slate-400">Overtime:</span>
            <span className="font-semibold text-slate-900 dark:text-white">45m</span>
          </div>
        </div>
      </div>

      {/* User Info */}
      <div className="pt-4 pb-4 border-b border-slate-200 dark:border-slate-700">
        <div className="text-sm font-semibold text-slate-900 dark:text-white mb-2">Logged in as:</div>
        <div className="text-sm text-slate-600 dark:text-slate-400">
          <div className="font-medium">{user?.firstName} {user?.lastName}</div>
          <div className="text-xs">{user?.email}</div>
          <div className="text-xs capitalize mt-1">
            <span className="px-2 py-1 bg-slate-100 dark:bg-slate-800 rounded-full">
              {user?.role}
            </span>
          </div>
        </div>
      </div>

      {/* Logout Button */}
      <div className="pt-6">
        <Button
          onClick={logout}
          variant="outline"
          className="w-full text-slate-600 dark:text-slate-400 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-white transition-all duration-200 h-10 rounded-md font-medium text-sm"
        >
          <LogOut className="w-4 h-4 mr-2" />
          Logout
        </Button>
      </div>
    </div>
  );
};

export default ClockSystemDropdown;
