// Weather API service using OpenWeatherMap
const WEATHER_API_KEY = '********************************';
const WEATHER_BASE_URL = 'https://api.openweathermap.org/data/2.5/weather';

export interface WeatherData {
  city: string;
  displayName: string;
  timezone: string;
  temperature: number;
  description: string;
  icon: string;
  emoji: string;
  time: string;
}

// Time Zone configurations with coordinates
const CITIES = [
  {
    name: 'San Francisco',
    displayName: 'Pacific Time',
    lat: 37.7749,
    lon: -122.4194,
    timezone: 'America/Los_Angeles',
    locale: 'en-US'
  },
  {
    name: 'Denver',
    displayName: 'Mountain Time',
    lat: 39.7392,
    lon: -104.9903,
    timezone: 'America/Denver',
    locale: 'en-US'
  },
  {
    name: 'Chicago',
    displayName: 'Central Time',
    lat: 41.8781,
    lon: -87.6298,
    timezone: 'America/Chicago',
    locale: 'en-US'
  },
  {
    name: 'New York',
    displayName: 'Eastern Time',
    lat: 40.7128,
    lon: -74.0060,
    timezone: 'America/New_York',
    locale: 'en-US'
  },
  {
    name: 'Manila',
    displayName: 'Philippines Time',
    lat: 14.5995,
    lon: 120.9842,
    timezone: 'Asia/Manila',
    locale: 'en-PH'
  }
];

// Weather condition to emoji mapping
const getWeatherEmoji = (weatherMain: string, icon: string): string => {
  const isDay = icon.includes('d');
  
  switch (weatherMain.toLowerCase()) {
    case 'clear':
      return isDay ? '☀️' : '🌙';
    case 'clouds':
      return isDay ? '⛅' : '☁️';
    case 'rain':
      return '🌧️';
    case 'drizzle':
      return '🌦️';
    case 'thunderstorm':
      return '⛈️';
    case 'snow':
      return '❄️';
    case 'mist':
    case 'fog':
      return '🌫️';
    default:
      return isDay ? '🌤️' : '🌙';
  }
};

// Fetch weather data for a specific city
const fetchCityWeather = async (city: typeof CITIES[0]): Promise<WeatherData> => {
  if (!WEATHER_API_KEY) {
    // Return mock data if no API key
    return {
      city: city.name,
      displayName: city.displayName,
      timezone: city.timezone,
      temperature: Math.floor(Math.random() * 30) + 10,
      description: 'Clear',
      icon: '01d',
      emoji: '☀️',
      time: new Date().toLocaleTimeString(city.locale, {
        timeZone: city.timezone,
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  }

  try {
    const response = await fetch(
      `${WEATHER_BASE_URL}?lat=${city.lat}&lon=${city.lon}&appid=${WEATHER_API_KEY}&units=imperial`
    );

    if (!response.ok) {
      throw new Error(`Weather API error: ${response.status}`);
    }

    const data = await response.json();
    
    return {
      city: city.name,
      displayName: city.displayName,
      timezone: city.timezone,
      temperature: Math.round(data.main.temp),
      description: data.weather[0].main,
      icon: data.weather[0].icon,
      emoji: getWeatherEmoji(data.weather[0].main, data.weather[0].icon),
      time: new Date().toLocaleTimeString(city.locale, {
        timeZone: city.timezone,
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  } catch (error) {
    console.error(`Error fetching weather for ${city.name}:`, error);
    
    // Return fallback data
    return {
      city: city.name,
      displayName: city.displayName,
      timezone: city.timezone,
      temperature: Math.floor(Math.random() * 30) + 10,
      description: 'Clear',
      icon: '01d',
      emoji: '☀️',
      time: new Date().toLocaleTimeString(city.locale, {
        timeZone: city.timezone,
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  }
};

// Fetch weather data for all cities
export const fetchAllWeatherData = async (): Promise<WeatherData[]> => {
  try {
    const weatherPromises = CITIES.map(city => fetchCityWeather(city));
    const weatherData = await Promise.all(weatherPromises);
    return weatherData;
  } catch (error) {
    console.error('Error fetching weather data:', error);
    
    // Return mock data as fallback
    return CITIES.map(city => ({
      city: city.name,
      displayName: city.displayName,
      timezone: city.timezone,
      temperature: Math.floor(Math.random() * 30) + 10,
      description: 'Clear',
      icon: '01d',
      emoji: '☀️',
      time: new Date().toLocaleTimeString(city.locale, {
        timeZone: city.timezone,
        hour: '2-digit',
        minute: '2-digit'
      })
    }));
  }
};

// Get background color based on weather condition
export const getWeatherBackgroundColor = (description: string): string => {
  switch (description.toLowerCase()) {
    case 'clear':
      return 'bg-blue-50 dark:bg-blue-900/20';
    case 'clouds':
      return 'bg-gray-50 dark:bg-gray-900/20';
    case 'rain':
    case 'drizzle':
      return 'bg-blue-100 dark:bg-blue-900/30';
    case 'thunderstorm':
      return 'bg-purple-50 dark:bg-purple-900/20';
    case 'snow':
      return 'bg-cyan-50 dark:bg-cyan-900/20';
    case 'mist':
    case 'fog':
      return 'bg-gray-100 dark:bg-gray-900/30';
    default:
      return 'bg-blue-50 dark:bg-blue-900/20';
  }
};
