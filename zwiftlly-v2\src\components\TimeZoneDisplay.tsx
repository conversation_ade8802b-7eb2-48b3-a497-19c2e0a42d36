import React, { useState, useEffect } from 'react';
import { Globe, Clock, MapPin } from 'lucide-react';

interface TimeZone {
  id: string;
  name: string;
  city: string;
  timezone: string;
  offset: string;
  flag?: string;
}

interface TimeZoneDisplayProps {
  darkMode?: boolean;
  compact?: boolean;
  showMultiple?: boolean;
}

const TimeZoneDisplay: React.FC<TimeZoneDisplayProps> = ({ 
  darkMode = false, 
  compact = false,
  showMultiple = true 
}) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [selectedTimezones] = useState<TimeZone[]>([
    {
      id: 'manila',
      name: 'Manila',
      city: 'Manila, Philippines',
      timezone: 'Asia/Manila',
      offset: 'UTC+8',
      flag: '🇵🇭'
    },
    {
      id: 'new-york',
      name: 'New York',
      city: 'New York, USA',
      timezone: 'America/New_York',
      offset: 'UTC-5',
      flag: '🇺🇸'
    },
    {
      id: 'london',
      name: 'London',
      city: 'London, UK',
      timezone: 'Europe/London',
      offset: 'UTC+0',
      flag: '🇬🇧'
    },
    {
      id: 'tokyo',
      name: 'Tokyo',
      city: 'Tokyo, Japan',
      timezone: 'Asia/Tokyo',
      offset: 'UTC+9',
      flag: '🇯🇵'
    }
  ]);

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTimeForTimezone = (timezone: string) => {
    try {
      return new Intl.DateTimeFormat('en-US', {
        timeZone: timezone,
        hour: '2-digit',
        minute: '2-digit',
        second: compact ? undefined : '2-digit',
        hour12: true
      }).format(currentTime);
    } catch (error) {
      console.error(`Error formatting time for timezone ${timezone}:`, error);
      return '--:--';
    }
  };

  const formatDateForTimezone = (timezone: string) => {
    try {
      return new Intl.DateTimeFormat('en-US', {
        timeZone: timezone,
        weekday: 'short',
        month: 'short',
        day: 'numeric'
      }).format(currentTime);
    } catch (error) {
      console.error(`Error formatting date for timezone ${timezone}:`, error);
      return '--';
    }
  };

  const getTimeStatus = (timezone: string) => {
    try {
      const hour = new Date().toLocaleString('en-US', {
        timeZone: timezone,
        hour: 'numeric',
        hour12: false
      });
      
      const hourNum = parseInt(hour);
      
      if (hourNum >= 6 && hourNum < 12) return { status: 'morning', color: 'text-yellow-500' };
      if (hourNum >= 12 && hourNum < 18) return { status: 'afternoon', color: 'text-orange-500' };
      if (hourNum >= 18 && hourNum < 22) return { status: 'evening', color: 'text-purple-500' };
      return { status: 'night', color: 'text-blue-500' };
    } catch (error) {
      return { status: 'unknown', color: darkMode ? 'text-gray-400' : 'text-gray-500' };
    }
  };

  if (compact) {
    const primaryTimezone = selectedTimezones[0];
    return (
      <div className="flex items-center space-x-2">
        <Globe className={`w-4 h-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
        <div className="flex items-center space-x-1">
          <span className="text-lg">{primaryTimezone.flag}</span>
          <span className={`text-sm font-mono ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            {formatTimeForTimezone(primaryTimezone.timezone)}
          </span>
        </div>
      </div>
    );
  }

  if (!showMultiple) {
    const primaryTimezone = selectedTimezones[0];
    const timeStatus = getTimeStatus(primaryTimezone.timezone);
    
    return (
      <div className={`rounded-lg border p-4 ${
        darkMode 
          ? 'bg-gray-800 border-gray-700' 
          : 'bg-white border-gray-200'
      }`}>
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <MapPin className={`w-4 h-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
            <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              {primaryTimezone.city}
            </span>
          </div>
          <span className="text-lg">{primaryTimezone.flag}</span>
        </div>
        
        <div className="space-y-1">
          <div className={`text-2xl font-mono font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            {formatTimeForTimezone(primaryTimezone.timezone)}
          </div>
          <div className="flex items-center justify-between">
            <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {formatDateForTimezone(primaryTimezone.timezone)}
            </span>
            <span className={`text-xs px-2 py-1 rounded-full ${timeStatus.color} bg-opacity-20`}>
              {timeStatus.status}
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`rounded-lg border ${
      darkMode 
        ? 'bg-gray-800 border-gray-700' 
        : 'bg-white border-gray-200'
    }`}>
      {/* Header */}
      <div className={`p-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="flex items-center space-x-2">
          <Globe className={`w-5 h-5 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
          <h3 className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            World Clock
          </h3>
        </div>
      </div>

      {/* Timezone List */}
      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {selectedTimezones.map((tz, index) => {
          const timeStatus = getTimeStatus(tz.timezone);
          
          return (
            <div key={tz.id} className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="text-xl">{tz.flag}</span>
                  <div>
                    <div className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {tz.name}
                    </div>
                    <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {tz.offset}
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className={`text-lg font-mono font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    {formatTimeForTimezone(tz.timezone)}
                  </div>
                  <div className="flex items-center justify-end space-x-2">
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {formatDateForTimezone(tz.timezone)}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded-full ${timeStatus.color} bg-opacity-20`}>
                      {timeStatus.status}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Footer */}
      <div className={`p-3 border-t text-center ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <span className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>
          Updated every second
        </span>
      </div>
    </div>
  );
};

export default TimeZoneDisplay;
