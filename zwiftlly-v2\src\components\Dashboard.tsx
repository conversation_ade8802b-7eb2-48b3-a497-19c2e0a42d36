import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Phone, MessageSquare, Users, Calendar, CheckSquare, TrendingUp } from 'lucide-react';

const Dashboard: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [volumeWatcherMode, setVolumeWatcherMode] = useState<'calls' | 'sms'>('calls');

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-1 sm:gap-2 h-full">
      {/* Left Column - Main Content */}
      <div className="lg:col-span-2 space-y-1 sm:space-y-2">
        {/* Weather & Time Zones - Always on top */}
        <div className="dashboard-widget animate-fade-in-up animate-delay-0">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm sm:text-base">Weather & Time Zones</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                <div className="text-center p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-xs text-muted-foreground">New York</div>
                  <div className="text-sm font-medium">2:30 PM</div>
                  <div className="text-xs">☀️ 72°F</div>
                </div>
                <div className="text-center p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-xs text-muted-foreground">London</div>
                  <div className="text-sm font-medium">7:30 PM</div>
                  <div className="text-xs">🌤️ 18°C</div>
                </div>
                <div className="text-center p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <div className="text-xs text-muted-foreground">Tokyo</div>
                  <div className="text-sm font-medium">3:30 AM</div>
                  <div className="text-xs">🌙 22°C</div>
                </div>
                <div className="text-center p-2 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                  <div className="text-xs text-muted-foreground">Sydney</div>
                  <div className="text-sm font-medium">5:30 AM</div>
                  <div className="text-xs">🌅 16°C</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Team Status - Below weather */}
        <div className="dashboard-widget animate-fade-in-up animate-delay-150">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm sm:text-base">Team Status</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                {[
                  { name: 'Louise Chen', status: 'online', time: '8:00 AM', activity: 'Customer Support' },
                  { name: 'John Smith', status: 'break', time: '8:15 AM', activity: 'Break (15 min)' },
                  { name: 'Sarah Wilson', status: 'online', time: '8:30 AM', activity: 'Quality Assurance' },
                  { name: 'Mike Johnson', status: 'meeting', time: '9:00 AM', activity: 'Team Meeting' },
                  { name: 'Emma Davis', status: 'offline', time: '7:45 AM', activity: 'Not Started' }
                ].map((member, index) => (
                  <div key={index} className="flex items-center justify-between p-2 rounded-lg bg-muted/50">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        member.status === 'online' ? 'bg-green-500' :
                        member.status === 'break' ? 'bg-yellow-500' :
                        member.status === 'meeting' ? 'bg-blue-500' :
                        'bg-gray-400'
                      }`}></div>
                      <div>
                        <div className="text-sm font-medium">{member.name}</div>
                        <div className="text-xs text-muted-foreground">{member.activity}</div>
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground">{member.time}</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Right Column - Dashboard Widgets */}
      <div className="lg:col-span-1 space-y-1 sm:space-y-2">
        {/* Quick Stats */}
        <div className="dashboard-widget animate-slide-in-right animate-delay-300">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm sm:text-base">Who's in?</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-3 gap-1">
                {/* Team Member Tiles */}
                <div className="p-1.5 rounded-md bg-green-100 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-center">
                  <div className="text-xs font-medium text-green-800 dark:text-green-400 truncate">Louise</div>
                </div>
                <div className="p-1.5 rounded-md bg-yellow-100 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 text-center">
                  <div className="text-xs font-medium text-yellow-800 dark:text-yellow-400 truncate">John</div>
                </div>
                <div className="p-1.5 rounded-md bg-blue-100 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 text-center">
                  <div className="text-xs font-medium text-blue-800 dark:text-blue-400 truncate">Sarah</div>
                </div>
                <div className="p-1.5 rounded-md bg-purple-100 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 text-center">
                  <div className="text-xs font-medium text-purple-800 dark:text-purple-400 truncate">Mike</div>
                </div>
                <div className="p-1.5 rounded-md bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-center">
                  <div className="text-xs font-medium text-red-800 dark:text-red-400 truncate">Emma</div>
                </div>
                <div className="p-1.5 rounded-md bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-center">
                  <div className="text-xs font-medium text-gray-600 dark:text-gray-400 truncate">+3</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Volume Watcher */}
        <div className="dashboard-widget animate-slide-in-right animate-delay-450">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm sm:text-base flex items-center justify-between">
                Volume Watcher
                <div className="flex bg-muted rounded-md p-0.5">
                  <Button
                    variant={volumeWatcherMode === 'calls' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setVolumeWatcherMode('calls')}
                    className="h-6 px-2 text-xs"
                  >
                    <Phone className="h-3 w-3 mr-1" />
                    Calls
                  </Button>
                  <Button
                    variant={volumeWatcherMode === 'sms' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setVolumeWatcherMode('sms')}
                    className="h-6 px-2 text-xs"
                  >
                    <MessageSquare className="h-3 w-3 mr-1" />
                    SMS
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                {[
                  { name: 'Louise Chen', count: 47, trend: '+12%' },
                  { name: 'John Smith', count: 43, trend: '+8%' },
                  { name: 'Sarah Wilson', count: 39, trend: '+15%' },
                  { name: 'Mike Johnson', count: 35, trend: '+5%' },
                  { name: 'Emma Davis', count: 32, trend: '+10%' }
                ].map((member, index) => (
                  <div key={index} className="flex items-center justify-between text-xs">
                    <span className="font-medium text-foreground truncate">{member.name}</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-muted-foreground">{member.count}</span>
                      <span className="text-green-600 dark:text-green-400 font-medium">{member.trend}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;