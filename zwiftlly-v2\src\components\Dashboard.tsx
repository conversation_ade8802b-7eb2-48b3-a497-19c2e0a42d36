import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Phone, MessageSquare } from 'lucide-react';
import { useAuth } from '../AuthContext';
import { userApi, taskApi, announcementApi, volumeApi } from '../lib/api';
import { supabase } from '../lib/supabase';
import { fetchAllWeatherData, getWeatherBackgroundColor, type WeatherData } from '../lib/weather';

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [volumeWatcherMode, setVolumeWatcherMode] = useState<'calls' | 'sms'>('calls');
  const [teamMembers, setTeamMembers] = useState<any[]>([]);
  const [volumeStats, setVolumeStats] = useState<any[]>([]);
  const [weatherData, setWeatherData] = useState<WeatherData[]>([]);
  const [dashboardStats, setDashboardStats] = useState({
    totalUsers: 0,
    activeTasks: 0,
    recentAnnouncements: 0,
    onlineUsers: 0
  });

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Refresh weather data every 10 minutes
  useEffect(() => {
    const weatherTimer = setInterval(async () => {
      try {
        const weather = await fetchAllWeatherData();
        setWeatherData(weather);
      } catch (error) {
        console.error('Error refreshing weather data:', error);
      }
    }, 10 * 60 * 1000); // 10 minutes

    return () => clearInterval(weatherTimer);
  }, []);

  // Load dashboard data
  useEffect(() => {
    const loadDashboardData = async () => {
      if (!user) return;

      try {
        const [users, tasks, announcements, volumeData, weather] = await Promise.all([
          userApi.getOrganizationUsers(),
          taskApi.getTasks(),
          announcementApi.getAnnouncements(),
          volumeApi.getUserVolumeStats(),
          fetchAllWeatherData()
        ]);

        setTeamMembers(users.slice(0, 8)); // Show first 8 users
        setVolumeStats(volumeData.slice(0, 5)); // Show top 5 performers
        setWeatherData(weather);
        setDashboardStats({
          totalUsers: users.length,
          activeTasks: tasks.filter(task => task.status !== 'DONE').length,
          recentAnnouncements: announcements.filter(ann => {
            const created = new Date(ann.created_at);
            const today = new Date();
            return created.toDateString() === today.toDateString();
          }).length,
          onlineUsers: users.filter(u => u.is_active).length
        });
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      }
    };

    loadDashboardData();
  }, [user]);

  // Set up real-time subscriptions
  useEffect(() => {
    if (!user) return;

    // Subscribe to user changes
    const usersSubscription = supabase
      .channel('dashboard-users')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'users'
        },
        () => {
          // Reload user data when changes occur
          userApi.getOrganizationUsers().then(users => {
            setTeamMembers(users.slice(0, 8));
            setDashboardStats(prev => ({
              ...prev,
              totalUsers: users.length,
              onlineUsers: users.filter(u => u.is_active).length
            }));
          });
        }
      )
      .subscribe();

    // Subscribe to task changes
    const tasksSubscription = supabase
      .channel('dashboard-tasks')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tasks'
        },
        () => {
          // Reload task data when changes occur
          taskApi.getTasks().then(tasks => {
            setDashboardStats(prev => ({
              ...prev,
              activeTasks: tasks.filter(task => task.status !== 'DONE').length
            }));
          });
        }
      )
      .subscribe();

    // Subscribe to announcement changes
    const announcementsSubscription = supabase
      .channel('dashboard-announcements')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'announcements'
        },
        () => {
          // Reload announcement data when changes occur
          announcementApi.getAnnouncements().then(announcements => {
            setDashboardStats(prev => ({
              ...prev,
              recentAnnouncements: announcements.filter(ann => {
                const created = new Date(ann.created_at);
                const today = new Date();
                return created.toDateString() === today.toDateString();
              }).length
            }));
          });
        }
      )
      .subscribe();

    // Subscribe to volume tracking changes
    const volumeSubscription = supabase
      .channel('dashboard-volume')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'volume_tracking'
        },
        () => {
          // Reload volume data when changes occur
          volumeApi.getUserVolumeStats().then(volumeData => {
            setVolumeStats(volumeData.slice(0, 5));
          });
        }
      )
      .subscribe();

    return () => {
      usersSubscription.unsubscribe();
      tasksSubscription.unsubscribe();
      announcementsSubscription.unsubscribe();
      volumeSubscription.unsubscribe();
    };
  }, [user]);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-1 sm:gap-2 h-full">
      {/* Left Column - Main Content */}
      <div className="lg:col-span-2 space-y-1 sm:space-y-2">
        {/* Weather & Time Zones - Always on top */}
        <div className="dashboard-widget animate-fade-in-up animate-delay-0">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm sm:text-base">Weather & Time Zones</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {weatherData.length > 0 ? weatherData.map((weather, index) => (
                  <div key={weather.city} className={`text-center p-2 rounded-lg ${getWeatherBackgroundColor(weather.description)}`}>
                    <div className="text-xs text-muted-foreground">{weather.displayName}</div>
                    <div className="text-sm font-medium">{weather.time}</div>
                    <div className="text-xs">{weather.emoji} {weather.temperature}°F</div>
                  </div>
                )) : (
                  // Fallback while loading
                  <>
                    <div className="text-center p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="text-xs text-muted-foreground">Pacific Time</div>
                      <div className="text-sm font-medium">Loading...</div>
                      <div className="text-xs">☀️ --°</div>
                    </div>
                    <div className="text-center p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <div className="text-xs text-muted-foreground">Mountain Time</div>
                      <div className="text-sm font-medium">Loading...</div>
                      <div className="text-xs">🌤️ --°</div>
                    </div>
                    <div className="text-center p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <div className="text-xs text-muted-foreground">Central Time</div>
                      <div className="text-sm font-medium">Loading...</div>
                      <div className="text-xs">🌙 --°</div>
                    </div>
                    <div className="text-center p-2 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                      <div className="text-xs text-muted-foreground">Eastern Time</div>
                      <div className="text-sm font-medium">Loading...</div>
                      <div className="text-xs">🌅 --°</div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Team Status - Below weather */}
        <div className="dashboard-widget animate-fade-in-up animate-delay-150">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm sm:text-base">Team Status</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                {teamMembers.length > 0 ? teamMembers.slice(0, 5).map((member, index) => {
                  const status = member.is_active ? 'online' : 'offline';
                  const lastLogin = member.last_login ? new Date(member.last_login).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'Never';

                  return (
                    <div key={member.id} className="flex items-center justify-between p-2 rounded-lg bg-muted/50">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          status === 'online' ? 'bg-green-500' : 'bg-gray-400'
                        }`}></div>
                        <div>
                          <div className="text-sm font-medium">{member.full_name || member.email.split('@')[0]}</div>
                          <div className="text-xs text-muted-foreground">{member.role}</div>
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground">{lastLogin}</div>
                    </div>
                  );
                }) : (
                  <div className="text-center text-muted-foreground text-sm py-4">
                    Loading team members...
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Right Column - Dashboard Widgets */}
      <div className="lg:col-span-1 space-y-1 sm:space-y-2">
        {/* Quick Stats */}
        <div className="dashboard-widget animate-slide-in-right animate-delay-300">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm sm:text-base">Quick Stats</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-2 gap-2">
                <div className="text-center p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-xs text-muted-foreground">Users</div>
                  <div className="text-lg font-bold">{dashboardStats.totalUsers}</div>
                </div>
                <div className="text-center p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-xs text-muted-foreground">Tasks</div>
                  <div className="text-lg font-bold">{dashboardStats.activeTasks}</div>
                </div>
                <div className="text-center p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <div className="text-xs text-muted-foreground">News</div>
                  <div className="text-lg font-bold">{dashboardStats.recentAnnouncements}</div>
                </div>
                <div className="text-center p-2 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                  <div className="text-xs text-muted-foreground">Online</div>
                  <div className="text-lg font-bold">{dashboardStats.onlineUsers}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Who's in */}
        <div className="dashboard-widget animate-slide-in-right animate-delay-400">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm sm:text-base">Who's in?</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-3 gap-1">
                {teamMembers.slice(0, 5).map((member, index) => {
                  const colors = [
                    'bg-green-100 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400',
                    'bg-blue-100 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-400',
                    'bg-purple-100 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800 text-purple-800 dark:text-purple-400',
                    'bg-yellow-100 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-400',
                    'bg-red-100 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
                  ];

                  return (
                    <div key={member.id} className={`p-1.5 rounded-md border text-center ${colors[index % colors.length]}`}>
                      <div className="text-xs font-medium truncate">
                        {member.full_name ? member.full_name.split(' ')[0] : member.email.split('@')[0]}
                      </div>
                    </div>
                  );
                })}
                {teamMembers.length > 5 && (
                  <div className="p-1.5 rounded-md bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-center">
                    <div className="text-xs font-medium text-gray-600 dark:text-gray-400 truncate">
                      +{teamMembers.length - 5}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Volume Watcher */}
        <div className="dashboard-widget animate-slide-in-right animate-delay-500">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm sm:text-base flex items-center justify-between">
                Volume Watcher
                <div className="flex bg-muted rounded-md p-0.5">
                  <Button
                    variant={volumeWatcherMode === 'calls' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setVolumeWatcherMode('calls')}
                    className="h-6 px-2 text-xs"
                  >
                    <Phone className="h-3 w-3 mr-1" />
                    Calls
                  </Button>
                  <Button
                    variant={volumeWatcherMode === 'sms' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setVolumeWatcherMode('sms')}
                    className="h-6 px-2 text-xs"
                  >
                    <MessageSquare className="h-3 w-3 mr-1" />
                    SMS
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                {volumeStats.length > 0 ? volumeStats.map((userStat, index) => {
                  const count = volumeWatcherMode === 'calls' ? userStat.totalCalls : userStat.totalSms;
                  const userName = userStat.user?.full_name || userStat.user?.email?.split('@')[0] || 'Unknown User';

                  return (
                    <div key={userStat.user?.id || index} className="flex items-center justify-between text-xs">
                      <span className="font-medium text-foreground truncate">{userName}</span>
                      <div className="flex items-center space-x-2">
                        <span className="text-muted-foreground">{count}</span>
                        <span className="text-green-600 dark:text-green-400 font-medium">
                          {index === 0 ? '🏆' : `#${index + 1}`}
                        </span>
                      </div>
                    </div>
                  );
                }) : (
                  <div className="text-center text-muted-foreground text-sm py-4">
                    Loading volume data...
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;