import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

interface TeamMember {
  id: string;
  name: string;
  status: 'working' | 'break' | 'offline';
  currentProjects: string[];
  lastActivity: Date;
  avatar?: string;
  idleTime: number; // in minutes (unproductive time)
  nationality: string;
}

const TeamStatus: React.FC = () => {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);

  // Mock team data with multiple projects and realistic idle times (unproductive time)
  const mockTeamData: TeamMember[] = [
    {
      id: '1',
      name: '<PERSON>',
      status: 'working',
      currentProjects: ['Paschal', 'Classic', 'A1'],
      lastActivity: new Date(Date.now() - 5 * 60 * 1000),
      idleTime: 12, // 12 minutes of unproductive time today
      nationality: 'PH'
    },
    {
      id: '2',
      name: '<PERSON>',
      status: 'break',
      currentProjects: ['Vredevoogd', '<PERSON>'],
      lastActivity: new Date(Date.now() - 10 * 60 * 1000),
      idleTime: 89, // 1h 29m of unproductive time today
      nationality: 'PH'
    },
    {
      id: '3',
      name: 'Bless-Ann',
      status: 'working',
      currentProjects: ['Classic', 'Robin Aire', 'Blue Sky'],
      lastActivity: new Date(Date.now() - 2 * 60 * 1000),
      idleTime: 7, // 7 minutes of unproductive time today
      nationality: 'PH'
    },
    {
      id: '4',
      name: 'Pearl',
      status: 'offline',
      currentProjects: ['Hurlburt', 'Randazzo', 'Flame Fox & Sons'],
      lastActivity: new Date(Date.now() - 45 * 60 * 1000),
      idleTime: 156, // 2h 36m of unproductive time today
      nationality: 'PH'
    },
    {
      id: '5',
      name: 'Christine G.',
      status: 'working',
      currentProjects: ['Hoffmann', 'Mr. Sparky'],
      lastActivity: new Date(Date.now() - 1 * 60 * 1000),
      idleTime: 3, // 3 minutes of unproductive time today
      nationality: 'PH'
    },
    {
      id: '6',
      name: 'Christine D.',
      status: 'break',
      currentProjects: ['Randazzo', 'Blind & Sons', 'Freguson'],
      lastActivity: new Date(Date.now() - 15 * 60 * 1000),
      idleTime: 34, // 34 minutes of unproductive time today
      nationality: 'PH'
    },
    {
      id: '7',
      name: 'Ray',
      status: 'working',
      currentProjects: ['Flame Fox & Sons', 'A1', 'Blue Sky', 'Paschal'],
      lastActivity: new Date(Date.now() - 8 * 60 * 1000),
      idleTime: 18, // 18 minutes of unproductive time today
      nationality: 'PH'
    },
    {
      id: '8',
      name: 'Gizelle',
      status: 'working',
      currentProjects: ['A1', 'Robin Aire'],
      lastActivity: new Date(Date.now() - 3 * 60 * 1000),
      idleTime: 5, // 5 minutes of unproductive time today
      nationality: 'PH'
    },
    {
      id: '9',
      name: 'Dustin',
      status: 'break',
      currentProjects: ['Mr. Sparky', 'Blind & Sons', 'Freguson'],
      lastActivity: new Date(Date.now() - 25 * 60 * 1000),
      idleTime: 67, // 1h 7m of unproductive time today
      nationality: 'PH'
    }
  ];

  useEffect(() => {
    // Simulate API call
    const fetchTeamData = async () => {
      try {
        setLoading(true);
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 800));
        // Sort team members by idle time (highest to lowest)
        const sortedTeamData = [...mockTeamData].sort((a, b) => b.idleTime - a.idleTime);
        setTeamMembers(sortedTeamData);
      } finally {
        setLoading(false);
      }
    };

    fetchTeamData();

    // Refresh team data every 30 seconds
    const interval = setInterval(fetchTeamData, 30 * 1000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: TeamMember['status']) => {
    switch (status) {
      case 'working':
        return 'bg-green-500';
      case 'break':
        return 'bg-yellow-500';
      case 'offline':
        return 'bg-gray-400';
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusText = (status: TeamMember['status']) => {
    switch (status) {
      case 'working':
        return 'Working';
      case 'break':
        return 'On Break';
      case 'offline':
        return 'Offline';
      default:
        return 'Unknown';
    }
  };

  const getFlag = (nationality: string) => {
    switch (nationality) {
      case 'PH':
        return '🇵🇭';
      default:
        return '🏳️';
    }
  };

  const formatIdleTime = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}m`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
    }
  };

  const getTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  if (loading) {
    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Team Status</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div>
            {[...Array(6)].map((_, i) => (
              <div key={i}>
                <div className="animate-pulse flex items-center p-1.5">
                  {/* Left: Avatar and Name */}
                  <div className="flex items-center space-x-2 flex-shrink-0 w-1/3">
                    <div className="w-5 h-5 sm:w-6 sm:h-6 bg-muted-foreground/20 rounded-full"></div>
                    <div className="w-16 h-3 bg-muted-foreground/20 rounded"></div>
                  </div>
                  {/* Center: Project */}
                  <div className="flex-1 text-center px-1">
                    <div className="w-20 h-6 bg-muted-foreground/20 rounded-full mx-auto"></div>
                  </div>
                  {/* Right: Status */}
                  <div className="flex-shrink-0 w-1/4 text-right">
                    <div className="w-16 h-6 bg-muted-foreground/20 rounded-full ml-auto"></div>
                  </div>
                </div>
                {/* Separator line */}
                {i < 5 && <hr className="border-t border-muted-foreground/20 my-1" />}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get current date in San Francisco time
  const getCurrentSFDate = () => {
    const now = new Date();
    const sfTime = new Date(now.toLocaleString("en-US", {timeZone: "America/Los_Angeles"}));
    return sfTime.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm sm:text-base">Team Status</CardTitle>
          <div className="text-sm font-semibold text-muted-foreground">
            {getCurrentSFDate()}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        {/* Column Headers */}
        <div className="flex items-center p-1.5 border-b border-border mb-2">
          <div className="flex-shrink-0 w-1/4">
            <span className="text-xs font-semibold text-muted-foreground">Agent</span>
          </div>
          <div className="flex-1 text-center px-1">
            <span className="text-xs font-semibold text-muted-foreground">Auditing Today</span>
          </div>
          <div className="flex-shrink-0 w-1/6 text-center">
            <span className="text-xs font-semibold text-muted-foreground">Idle Time</span>
          </div>
          <div className="flex-shrink-0 w-1/4 text-right">
            <span className="text-xs font-semibold text-muted-foreground">Work Status</span>
          </div>
        </div>

        <div>
          {teamMembers.map((member, index) => (
            <div key={member.id}>
              <div className="flex items-center p-1.5 rounded-lg hover:bg-muted/50 transition-colors">
              {/* Left: Avatar, Flag and Name */}
              <div className="flex items-center space-x-2 flex-shrink-0 w-1/4">
                <div className="relative">
                  <Avatar className="w-5 h-5 sm:w-6 sm:h-6">
                    <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                      {getInitials(member.name)}
                    </AvatarFallback>
                  </Avatar>
                  {/* Status indicator */}
                  <div className={`absolute -bottom-0.5 -right-0.5 w-2 h-2 ${getStatusColor(member.status)} rounded-full border border-background`}></div>
                </div>
                <span className="text-xs">{getFlag(member.nationality)}</span>
                <div className="font-medium text-foreground text-xs truncate">
                  {member.name}
                </div>
              </div>

              {/* Center Left: Project Names */}
              <div className="flex-1 text-center px-1">
                <div className="flex flex-wrap justify-center gap-1">
                  {member.currentProjects.slice(0, 2).map((project, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                    >
                      {project}
                    </span>
                  ))}
                  {member.currentProjects.length > 2 && (
                    <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-900/20 dark:text-gray-400">
                      +{member.currentProjects.length - 2}
                    </span>
                  )}
                </div>
              </div>

              {/* Center Right: Idle Time */}
              <div className="flex-shrink-0 w-1/6 text-center">
                <div className="flex items-center justify-center space-x-1">
                  <span className="text-xs text-muted-foreground">
                    {formatIdleTime(member.idleTime)}
                  </span>
                  <span className="text-xs text-muted-foreground/70">
                    idle
                  </span>
                </div>
              </div>

              {/* Right: Status */}
              <div className="flex-shrink-0 w-1/4 text-right">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  member.status === 'working'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                    : member.status === 'break'
                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                }`}>
                  {getStatusText(member.status)}
                </span>
              </div>
              </div>
              {/* Horizontal separator line between members */}
              {index < teamMembers.length - 1 && (
                <hr className="border-t border-border my-1" />
              )}
            </div>
          ))}
        </div>

        <div className="mt-2 text-xs text-muted-foreground text-center">
          Updates every 30 seconds
        </div>
      </CardContent>
    </Card>
  );
};

export default TeamStatus;
