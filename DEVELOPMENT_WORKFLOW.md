# 🚀 ZWIFTLLY Development Workflow

This document outlines the **3-tier development workflow** for the ZWIFTLLY Team Management System.

## 📋 Overview

```
💻 LOCALHOST (Development)
    ↓ git push origin development
🧪 STAGING (Development Branch)
    ↓ merge to master
🚀 PRODUCTION (Master Branch)
```

## 🏗️ Environment Setup

### **1. 💻 Local Development**
- **Branch**: `development` (or feature branches)
- **URL**: `http://localhost:5176`
- **Environment**: `.env.local`
- **Purpose**: Development and testing new features

### **2. 🧪 Staging Environment**
- **Branch**: `development`
- **URL**: `https://zwiftlly-team-management-system-git-development-daddyrays-projects.vercel.app`
- **Environment**: `.env.development`
- **Purpose**: Testing before production deployment

### **3. 🚀 Production Environment**
- **Branch**: `master`
- **URL**: `https://zwiftlly-team-management-system.vercel.app`
- **Environment**: `.env.production`
- **Purpose**: Live application for end users

## 🔄 Development Workflow

### **Step 1: Local Development**
```bash
# Make sure you're on development branch
git checkout development
git pull origin development

# Start local development server
cd zwiftlly-frontend
npm run dev

# Make your changes and test locally at http://localhost:5176
```

### **Step 2: Push to Staging**
```bash
# Add and commit your changes
git add .
git commit -m "feat: your feature description"

# Push to development branch (triggers automatic staging deployment)
git push origin development
```

### **Step 3: Test on Staging**
- Visit: `https://zwiftlly-team-management-system-git-development-daddyrays-projects.vercel.app`
- Test all functionality thoroughly
- Verify the changes work as expected

### **Step 4: Deploy to Production**
```bash
# Switch to master branch
git checkout master
git pull origin master

# Merge development into master
git merge development

# Push to production (triggers automatic production deployment)
git push origin master
```

## 🛡️ Safety Rules

### **✅ DO:**
- Always test changes locally first
- Push to `development` branch for staging
- Test thoroughly on staging before production
- Use descriptive commit messages
- Create feature branches for large changes

### **❌ DON'T:**
- Never push directly to `master` without testing
- Don't skip the staging environment
- Don't deploy untested code to production
- Don't commit sensitive data or API keys

## 🔧 Quick Commands

### **Start Local Development**
```bash
cd zwiftlly-frontend
npm run dev
```

### **Build for Production**
```bash
npm run build
```

### **Deploy to Staging**
```bash
git push origin development
```

### **Deploy to Production**
```bash
git checkout master
git merge development
git push origin master
```

## 🌐 Environment URLs

| Environment | URL | Branch | Purpose |
|-------------|-----|--------|---------|
| **Local** | http://localhost:5176 | development | Development |
| **Staging** | [Staging URL](https://zwiftlly-team-management-system-git-development-daddyrays-projects.vercel.app) | development | Testing |
| **Production** | [Production URL](https://zwiftlly-team-management-system.vercel.app) | master | Live App |

## 🔍 Environment Variables

Each environment has its own configuration:
- `.env.local` - Local development
- `.env.development` - Staging environment
- `.env.production` - Production environment

## 📞 Support

If you encounter issues with the workflow:
1. Check the Vercel dashboard for deployment status
2. Verify environment variables are set correctly
3. Ensure all tests pass before merging to master
4. Contact the development team for assistance

---

**Remember**: This workflow ensures that all changes are tested before going live, preventing production issues and maintaining application stability.
