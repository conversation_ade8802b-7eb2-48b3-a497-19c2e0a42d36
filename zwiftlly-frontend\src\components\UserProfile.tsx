import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { User, Camera, Save, ArrowLeft, Shield, Globe, Mail, FileEdit } from 'lucide-react';
import { useAuth } from '@/contexts/SupabaseAuthContext';
import ProfileUpdatePopup from './ProfileUpdatePopup';
import ChangeRequestDialog from './ChangeRequestDialog';
import CustomDialog from '@/components/ui/custom-dialog';

const UserProfile: React.FC<{ onBack?: () => void }> = ({ onBack }) => {
  const { user } = useAuth();
  const [selectedAvatar, setSelectedAvatar] = useState('');
  const [customAvatar, setCustomAvatar] = useState('');
  const [profile, setProfile] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    hireDate: '',
    companyRole: '',
    nickname: ''
  });

  // New state for profile management
  const [hasUpdatedProfile, setHasUpdatedProfile] = useState(false);
  const [showWelcomePopup, setShowWelcomePopup] = useState(false);
  const [showChangeRequest, setShowChangeRequest] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);

  // Initialize profile with Google user data
  useEffect(() => {
    if (user) {
      // Try to load saved profile data first
      const savedProfile = localStorage.getItem(`profile_data_${user.id}`);
      if (savedProfile) {
        try {
          const parsed = JSON.parse(savedProfile);
          setProfile({
            firstName: parsed.firstName || user.firstName || '',
            lastName: parsed.lastName || user.lastName || '',
            email: user.email,
            phone: parsed.phone || '',
            hireDate: parsed.hireDate || '',
            companyRole: parsed.companyRole || '',
            nickname: parsed.nickname || ''
          });
          if (parsed.avatar) {
            setCustomAvatar(parsed.avatar);
          }
        } catch (error) {
          console.error('Error parsing saved profile:', error);
          // Fall back to Google data
          setProfile({
            firstName: user.firstName || '',
            lastName: user.lastName || '',
            email: user.email,
            phone: '',
            hireDate: '',
            companyRole: '',
            nickname: ''
          });
        }
      } else {
        // Use Google data as default
        setProfile({
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          email: user.email,
          phone: '',
          hireDate: '',
          companyRole: '',
          nickname: ''
        });
      }

      // Set Google profile picture if available and no custom avatar
      if (user.picture && !customAvatar) {
        setCustomAvatar(user.picture);
      }

      // Check if this is a first-time user (simulate with localStorage)
      const hasSeenWelcome = localStorage.getItem(`profile_welcome_${user.id}`);
      const hasUpdated = localStorage.getItem(`profile_updated_${user.id}`);

      if (!hasSeenWelcome) {
        setShowWelcomePopup(true);
      }

      if (hasUpdated) {
        setHasUpdatedProfile(true);
      }
    }
  }, [user]);

  // 20 default avatar options
  const defaultAvatars = [
    '👨‍💼', '👩‍💼', '👨‍💻', '👩‍💻', '👨‍🔧', '👩‍🔧', '👨‍🎓', '👩‍🎓', '👨‍⚕️', '👩‍⚕️',
    '👨‍🏫', '👩‍🏫', '👨‍🎨', '👩‍🎨', '👨‍🚀', '👩‍🚀', '👨‍🔬', '👩‍🔬', '👨‍💼', '👩‍💼'
  ];

  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setCustomAvatar(e.target?.result as string);
        setSelectedAvatar('');
      };
      reader.readAsDataURL(file);
    }
  };

  const calculateYearsOfService = () => {
    if (!profile.hireDate) return null;

    const hireDate = new Date(profile.hireDate);
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - hireDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    const years = Math.floor(diffDays / 365);
    const months = Math.floor((diffDays % 365) / 30);

    if (years === 0 && months === 0) {
      return 'Less than a month';
    } else if (years === 0) {
      return `${months} month${months !== 1 ? 's' : ''}`;
    } else if (months === 0) {
      return `${years} year${years !== 1 ? 's' : ''}`;
    } else {
      return `${years} year${years !== 1 ? 's' : ''}, ${months} month${months !== 1 ? 's' : ''}`;
    }
  };

  const handleSave = () => {
    console.log('Saving profile:', {
      ...profile,
      avatar: customAvatar || selectedAvatar
    });

    // Save profile data to localStorage
    if (user) {
      localStorage.setItem(`profile_data_${user.id}`, JSON.stringify({
        ...profile,
        avatar: customAvatar || selectedAvatar
      }));
      localStorage.setItem(`profile_updated_${user.id}`, 'true');
      setHasUpdatedProfile(true);
    }

    // Here you would typically save to your backend
    setShowSuccessDialog(true);
  };

  const handleWelcomeSkip = () => {
    if (user) {
      localStorage.setItem(`profile_welcome_${user.id}`, 'true');
    }
    setShowWelcomePopup(false);
  };

  const handleWelcomeUpdate = () => {
    if (user) {
      localStorage.setItem(`profile_welcome_${user.id}`, 'true');
    }
    setShowWelcomePopup(false);
    // Focus on first input or scroll to form
  };

  const handleRequestChange = () => {
    setShowChangeRequest(true);
  };

  const handleSubmitChangeRequest = (fields: string[]) => {
    console.log('Change request submitted for fields:', fields);
    // Here you would send the request to admins
    // For now, just show success
    setShowSuccessDialog(true);
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8 animate-fade-in-up animate-delay-0">
          <div className="flex items-center space-x-4 mb-4">
            {onBack && (
              <Button
                variant="outline"
                onClick={onBack}
                className="border-border/50"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
            )}
            <div>
              <h1 className="text-3xl font-bold text-foreground">
                User Profile
              </h1>
              <p className="text-muted-foreground">
                Manage your personal information and profile settings
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
          {/* Profile Picture Section */}
          <Card className="bg-card rounded-xl shadow-lg border border-border/50 animate-slide-in-right animate-delay-150">
            <CardHeader className="p-6 border-b border-border/50 bg-gradient-to-r from-background to-muted/20">
              <CardTitle className="text-xl flex items-center space-x-3">
                <div className="p-3 bg-muted/20 rounded-xl shadow-sm border border-border/30">
                  <Camera className="w-6 h-6 text-foreground" />
                </div>
                <div>
                  <span className="text-foreground font-bold text-xl">Profile Picture</span>
                  <div className="text-sm text-muted-foreground font-medium">Choose your avatar</div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-6">
              {/* Current Avatar Display */}
              <div className="flex justify-center">
                <div className="w-32 h-32 rounded-full bg-muted border-4 border-border/50 flex items-center justify-center overflow-hidden">
                  {customAvatar ? (
                    <img src={customAvatar} alt="Profile" className="w-full h-full object-cover" />
                  ) : selectedAvatar ? (
                    <span className="text-6xl">{selectedAvatar}</span>
                  ) : (
                    <User className="w-16 h-16 text-muted-foreground" />
                  )}
                </div>
              </div>

              {/* Upload Custom Photo */}
              <div className="space-y-2">
                <Label className="text-foreground font-semibold">Upload Custom Photo</Label>
                <div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleAvatarUpload}
                    className="hidden"
                    id="avatar-upload"
                  />
                  <Label htmlFor="avatar-upload" className="cursor-pointer">
                    <Button variant="outline" className="w-full border-border/50" asChild>
                      <span>
                        <Camera className="w-4 h-4 mr-2" />
                        Choose Photo
                      </span>
                    </Button>
                  </Label>
                </div>
              </div>

              {/* Default Avatars */}
              <div className="space-y-2">
                <Label className="text-foreground font-semibold">Or Choose Default Avatar</Label>
                <div className="grid grid-cols-5 gap-2">
                  {defaultAvatars.map((avatar, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        setSelectedAvatar(avatar);
                        setCustomAvatar('');
                      }}
                      className={`w-12 h-12 rounded-full border-2 flex items-center justify-center text-2xl hover:scale-110 transition-all duration-200 ${
                        selectedAvatar === avatar
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-border/50 hover:border-blue-300'
                      }`}
                    >
                      {avatar}
                    </button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Google Account Info */}
          <Card className="bg-card rounded-xl shadow-lg border border-border/50 animate-slide-in-right animate-delay-200">
            <CardHeader className="p-6 border-b border-border/50 bg-gradient-to-r from-background to-muted/20">
              <CardTitle className="text-xl flex items-center space-x-3">
                <div className="p-3 bg-muted/20 rounded-xl shadow-sm border border-border/30">
                  <Globe className="w-6 h-6 text-foreground" />
                </div>
                <div>
                  <span className="text-foreground font-bold text-xl">Google Account</span>
                  <div className="text-sm text-muted-foreground font-medium">OAuth information</div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-3 p-3 bg-muted/10 rounded-lg">
                  <Mail className="w-5 h-5 text-blue-600" />
                  <div>
                    <div className="text-sm font-medium text-foreground">Email</div>
                    <div className="text-xs text-muted-foreground">{user?.email}</div>
                  </div>
                </div>

                <div className="flex items-center space-x-3 p-3 bg-muted/10 rounded-lg">
                  <Shield className="w-5 h-5 text-green-600" />
                  <div>
                    <div className="text-sm font-medium text-foreground">Role</div>
                    <div className="text-xs text-muted-foreground capitalize">{user?.role}</div>
                  </div>
                </div>

                <div className="flex items-center space-x-3 p-3 bg-muted/10 rounded-lg">
                  <Globe className="w-5 h-5 text-purple-600" />
                  <div>
                    <div className="text-sm font-medium text-foreground">Organization</div>
                    <div className="text-xs text-muted-foreground">{user?.organization}</div>
                  </div>
                </div>
              </div>

              <div className="pt-2 border-t border-border/50">
                <p className="text-xs text-muted-foreground text-center">
                  Account managed by Google OAuth
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Profile Information */}
          <div className="xl:col-span-2">
            <Card className="bg-card rounded-xl shadow-lg border border-border/50 animate-slide-in-right animate-delay-300">
              <CardHeader className="p-6 border-b border-border/50 bg-gradient-to-r from-background to-muted/20">
                <CardTitle className="text-xl flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-3 bg-muted/20 rounded-xl shadow-sm border border-border/30">
                      <User className="w-6 h-6 text-foreground" />
                    </div>
                    <div>
                      <span className="text-foreground font-bold text-xl">Personal Information</span>
                      <div className="text-sm text-muted-foreground font-medium">Update your profile details</div>
                    </div>
                  </div>
                  {calculateYearsOfService() && (
                    <div className="text-right">
                      <div className="text-sm font-semibold text-foreground">Time with Company</div>
                      <div className="text-xs text-muted-foreground">{calculateYearsOfService()}</div>
                    </div>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-6">
                {/* Name Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName" className="text-foreground font-semibold">First Name</Label>
                    <Input
                      id="firstName"
                      value={profile.firstName}
                      onChange={(e) => setProfile({...profile, firstName: e.target.value})}
                      placeholder="Enter your first name"
                      className="bg-background border-border/50"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName" className="text-foreground font-semibold">Last Name</Label>
                    <Input
                      id="lastName"
                      value={profile.lastName}
                      onChange={(e) => setProfile({...profile, lastName: e.target.value})}
                      placeholder="Enter your last name"
                      className="bg-background border-border/50"
                    />
                  </div>
                </div>

                {/* Nickname Field */}
                <div className="space-y-2">
                  <Label htmlFor="nickname" className="text-foreground font-semibold">Nickname</Label>
                  <Input
                    id="nickname"
                    value={profile.nickname}
                    onChange={(e) => setProfile({...profile, nickname: e.target.value})}
                    placeholder="Enter your preferred nickname"
                    className="bg-background border-border/50"
                  />
                  <p className="text-xs text-muted-foreground">This is how other team members will see you</p>
                </div>

                {/* Contact Information */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-foreground font-semibold">Work Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={profile.email}
                      disabled
                      className="bg-muted border-border/50 text-muted-foreground"
                    />
                    <p className="text-xs text-muted-foreground">Email is managed by Google OAuth and cannot be changed</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="text-foreground font-semibold">Phone Number</Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={profile.phone}
                      onChange={(e) => setProfile({...profile, phone: e.target.value})}
                      placeholder="Enter your phone number"
                      className="bg-background border-border/50"
                    />
                  </div>
                </div>

                {/* Employment Information */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="companyRole" className="text-foreground font-semibold">Company Role</Label>
                    <Input
                      id="companyRole"
                      value={profile.companyRole}
                      onChange={(e) => setProfile({...profile, companyRole: e.target.value})}
                      placeholder="Enter your role in the company"
                      className="bg-background border-border/50"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="hireDate" className="text-foreground font-semibold">Hire Date</Label>
                    <Input
                      id="hireDate"
                      type="date"
                      value={profile.hireDate}
                      onChange={(e) => setProfile({...profile, hireDate: e.target.value})}
                      className="bg-background border-border/50"
                    />
                  </div>
                </div>

                {/* Save Button */}
                <div className="pt-4">
                  {hasUpdatedProfile ? (
                    <Button
                      onClick={handleRequestChange}
                      className="w-full bg-orange-600 hover:bg-orange-700 text-white"
                    >
                      <FileEdit className="w-4 h-4 mr-2" />
                      Request Change
                    </Button>
                  ) : (
                    <Button
                      onClick={handleSave}
                      className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
                    >
                      <Save className="w-4 h-4 mr-2" />
                      Save Profile
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Welcome Popup */}
      <ProfileUpdatePopup
        isOpen={showWelcomePopup}
        onClose={handleWelcomeSkip}
        onUpdateProfile={handleWelcomeUpdate}
        onSkip={handleWelcomeSkip}
      />

      {/* Change Request Dialog */}
      <ChangeRequestDialog
        isOpen={showChangeRequest}
        onClose={() => setShowChangeRequest(false)}
        onSubmitRequest={handleSubmitChangeRequest}
      />

      {/* Success Dialog */}
      <CustomDialog
        isOpen={showSuccessDialog}
        onClose={() => setShowSuccessDialog(false)}
        title="Success!"
        message={hasUpdatedProfile ? "Your change request has been submitted to administrators for approval." : "Your profile has been saved successfully!"}
        type="success"
        confirmText="OK"
        showCancel={false}
      />
    </div>
  );
};

export default UserProfile;
