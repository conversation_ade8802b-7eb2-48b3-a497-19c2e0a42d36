import React, { useState, useRef, useEffect } from 'react';
import { Button } from './ui/button';
import { Avatar, AvatarFallback } from './ui/avatar';
import { Moon, Sun, Bell, User, LogOut } from 'lucide-react';
import { useAuth } from '../AuthContext';

interface TopNavigationProps {
  darkMode: boolean;
  onToggleDarkMode: () => void;
  onProfileClick?: () => void;
}

const TopNavigation: React.FC<TopNavigationProps> = ({ darkMode, onToggleDarkMode, onProfileClick }) => {
  const [profileMenuOpen, setProfileMenuOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const profileMenuRef = useRef<HTMLDivElement>(null);
  const notificationsRef = useRef<HTMLDivElement>(null);
  const { user, signOut } = useAuth();

  const handleClockAction = (action: string) => {
    console.log(`Clock action: ${action}`);
    setProfileMenuOpen(false);
  };

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (profileMenuRef.current && !profileMenuRef.current.contains(event.target as Node)) {
        setProfileMenuOpen(false);
      }
      if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
        setNotificationsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSignOut = async () => {
    await signOut();
    setProfileMenuOpen(false);
  };

  return (
    <header className="bg-card border-b border-border px-3 sm:px-4 py-1.5 sm:py-2">
      <div className="flex items-center justify-end">
        {/* Right side */}
        <div className="flex items-center space-x-2 sm:space-x-3 mr-2 sm:mr-3">
          {/* Dark mode toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleDarkMode}
            className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:scale-110 transition-all duration-200 hover:bg-primary/10"
            title={darkMode ? "Switch to Light Mode" : "Switch to Dark Mode"}
          >
            {darkMode ? <Sun className="h-3 w-3 sm:h-4 sm:w-4 transition-transform duration-300 hover:rotate-180" /> : <Moon className="h-3 w-3 sm:h-4 sm:w-4 transition-transform duration-300 hover:rotate-12" />}
          </Button>

          {/* Notifications */}
          <div className="relative" ref={notificationsRef}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setNotificationsOpen(!notificationsOpen)}
              className="relative h-7 w-7 sm:h-8 sm:w-8 p-0 hover:scale-110 transition-all duration-200 hover:bg-primary/10"
              title="Notifications"
            >
              <Bell className="h-3 w-3 sm:h-4 sm:w-4 hover:rotate-12 transition-transform duration-200" />
              <span className="absolute -top-0.5 -right-0.5 sm:-top-1 sm:-right-1 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-red-500 rounded-full animate-pulse"></span>
            </Button>

            {/* Notifications Dropdown */}
            {notificationsOpen && (
              <div className="absolute right-0 mt-2 w-80 bg-card border border-border rounded-lg shadow-lg z-50">
                <div className="p-4 border-b border-border">
                  <h3 className="font-semibold text-foreground">Notifications</h3>
                </div>
                <div className="p-4">
                  <p className="text-sm text-muted-foreground">No new notifications</p>
                </div>
              </div>
            )}
          </div>

          {/* User profile */}
          <div className="relative" ref={profileMenuRef}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setProfileMenuOpen(!profileMenuOpen)}
              className="h-8 w-8 p-0 rounded-full hover:scale-110 transition-all duration-200 hover:bg-primary/10"
              title="User Profile"
            >
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-primary text-primary-foreground text-sm font-bold">
                  {user?.name?.charAt(0)?.toUpperCase() || user?.email?.charAt(0)?.toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>
            </Button>

            {/* Profile Dropdown */}
            {profileMenuOpen && (
              <div className="absolute right-0 mt-2 w-64 bg-card border border-border rounded-lg shadow-lg z-50">
                <div className="p-4 border-b border-border">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-10 w-10">
                      <AvatarFallback className="bg-primary text-primary-foreground">
                        {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium text-foreground">{user?.name}</p>
                      <p className="text-sm text-muted-foreground">{user?.email}</p>
                    </div>
                  </div>
                </div>
                <div className="p-2">
                  <Button
                    variant="ghost"
                    onClick={onProfileClick}
                    className="w-full justify-start"
                  >
                    <User className="h-4 w-4 mr-2" />
                    View Profile
                  </Button>
                  <Button
                    variant="ghost"
                    onClick={handleSignOut}
                    className="w-full justify-start text-destructive hover:text-destructive"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default TopNavigation;
