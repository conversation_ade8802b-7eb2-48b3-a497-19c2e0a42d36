# Google OAuth Setup Guide for ZWIFTLLY

This guide will help you set up Google OAuth authentication for the ZWIFTLLY Team Management System.

## Prerequisites

- Google Cloud Console account
- Domain ownership (for production)
- Admin access to the ZWIFTLLY application

## Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Create Project" or select an existing project
3. Name your project (e.g., "ZWIFTLLY Team Management")
4. Note down your Project ID

## Step 2: Enable Google+ API

1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for "Google+ API" 
3. Click on it and press "Enable"
4. Also enable "Google Identity" if available

## Step 3: Configure OAuth Consent Screen

1. Go to "APIs & Services" > "OAuth consent screen"
2. Choose "External" for user type (unless you have Google Workspace)
3. Fill in the required information:
   - **App name**: ZWIFTLLY Team Management System
   - **User support email**: Your admin email
   - **Developer contact information**: Your admin email
4. Add your domain to "Authorized domains" (for production)
5. Save and continue through the scopes and test users sections

## Step 4: Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Choose "Web application" as the application type
4. Name it "ZWIFTLLY Web Client"
5. Add authorized JavaScript origins:
   - `http://localhost:5173` (for development)
   - `http://localhost:5174` (backup dev port)
   - `http://localhost:5175` (backup dev port)
   - `http://localhost:5176` (backup dev port)
   - `https://yourdomain.com` (for production)
6. Add authorized redirect URIs:
   - `http://localhost:5173` (for development)
   - `https://yourdomain.com` (for production)
7. Click "Create"
8. **Important**: Copy the Client ID and Client Secret

## Step 5: Configure Environment Variables

1. Open the `.env` file in your project root
2. Replace the placeholder values:

```env
# Google OAuth Configuration
VITE_GOOGLE_CLIENT_ID=your_actual_google_client_id_here
VITE_GOOGLE_CLIENT_SECRET=your_actual_google_client_secret_here

# Application Configuration
VITE_APP_NAME=ZWIFTLLY Team Management System
VITE_APP_URL=http://localhost:5173

# Organization Configuration (Default allowed domains)
VITE_DEFAULT_ALLOWED_DOMAINS=zwiftlly.com,yourcompany.com
```

3. Replace `your_actual_google_client_id_here` with your actual Client ID
4. Replace `yourcompany.com` with your organization's email domain

## Step 6: Configure Allowed Organizations

1. Start the application: `npm run dev`
2. Sign in with a Google account that has an email from your configured domain
3. Go to Settings > Organizations
4. Add your organization domains and configure:
   - **Domain**: Your company email domain (e.g., `yourcompany.com`)
   - **Organization Name**: Your company name
   - **Default Role**: Role for new users (usually `agent`)
   - **Admin Emails**: Comma-separated list of admin emails

## Step 7: Test the Authentication Flow

1. Open the application in your browser
2. You should see the Google Sign-In button
3. Click "Sign in with Google"
4. Complete the Google OAuth flow
5. Verify that:
   - You're logged in successfully
   - Your role is assigned correctly
   - You can access appropriate features based on your role

## Security Considerations

### For Production:

1. **Domain Verification**: Verify your domain in Google Cloud Console
2. **HTTPS Only**: Use HTTPS for all production URLs
3. **Restrict Domains**: Only add necessary domains to authorized origins
4. **Regular Audits**: Regularly review and audit user access
5. **Environment Variables**: Never commit real credentials to version control

### Organization Management:

- Only admin users can add/remove organization domains
- Users can only sign in if their email domain is in the allowed list
- Admin emails get automatic admin role assignment
- Regular users get the default role for their organization

## Troubleshooting

### Common Issues:

1. **"Error 400: redirect_uri_mismatch"**
   - Check that your redirect URIs in Google Cloud Console match your app URL
   - Ensure you're using the correct protocol (http vs https)

2. **"Error 403: access_blocked"**
   - Your app may need to be verified by Google for production use
   - Check OAuth consent screen configuration

3. **"Organization not authorized"**
   - Add your email domain to the allowed organizations in Settings
   - Verify the domain spelling is correct

4. **Google Client ID missing**
   - Check that VITE_GOOGLE_CLIENT_ID is set in your .env file
   - Restart the development server after changing .env

### Getting Help:

- Check the browser console for detailed error messages
- Verify all environment variables are set correctly
- Ensure your Google Cloud project has the necessary APIs enabled

## Production Deployment

When deploying to production:

1. Update the authorized origins and redirect URIs in Google Cloud Console
2. Set the production environment variables
3. Configure your domain in the allowed organizations
4. Test the complete authentication flow
5. Set up monitoring for authentication failures

---

**Note**: Keep your Google Client Secret secure and never expose it in client-side code. The current implementation only uses the Client ID on the frontend, which is safe for public exposure.
