import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Calendar as CalendarIcon, Clock, Save, X, Plus, Trash2 } from 'lucide-react';
import { format } from 'date-fns';

interface TeamMember {
  id: string;
  name: string;
}

interface ScheduleEntry {
  date: Date;
  startTime: string;
  endTime: string;
  isOff: boolean;
  shiftType: 'regular' | 'overtime' | 'holiday' | 'off';
}

interface ScheduleEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  member: TeamMember | null;
  onSave: (memberId: string, schedules: ScheduleEntry[]) => void;
}

const ScheduleEditModal: React.FC<ScheduleEditModalProps> = ({
  isOpen,
  onClose,
  member,
  onSave
}) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [scheduleEntries, setScheduleEntries] = useState<ScheduleEntry[]>([]);
  const [currentEntry, setCurrentEntry] = useState<Partial<ScheduleEntry>>({
    startTime: '08:00',
    endTime: '17:00',
    isOff: false,
    shiftType: 'regular'
  });

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
  };

  const addScheduleEntry = () => {
    if (selectedDate && currentEntry.startTime && currentEntry.endTime) {
      const newEntry: ScheduleEntry = {
        date: selectedDate,
        startTime: currentEntry.startTime!,
        endTime: currentEntry.endTime!,
        isOff: currentEntry.isOff || false,
        shiftType: currentEntry.shiftType || 'regular'
      };
      
      setScheduleEntries(prev => [...prev, newEntry]);
      
      // Reset form
      setCurrentEntry({
        startTime: '08:00',
        endTime: '17:00',
        isOff: false,
        shiftType: 'regular'
      });
    }
  };

  const removeScheduleEntry = (index: number) => {
    setScheduleEntries(prev => prev.filter((_, i) => i !== index));
  };

  const handleSave = () => {
    if (member) {
      onSave(member.id, scheduleEntries);
      onClose();
      setScheduleEntries([]);
    }
  };

  const handleClose = () => {
    onClose();
    setScheduleEntries([]);
    setSelectedDate(new Date());
  };

  const getShiftTypeColor = (shiftType: string) => {
    switch (shiftType) {
      case 'regular': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'overtime': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300';
      case 'holiday': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300';
      case 'off': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  if (!member) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-card border border-border/50">
        <DialogHeader className="border-b border-border/50 pb-4">
          <DialogTitle className="text-xl font-bold text-foreground flex items-center space-x-3">
            <div className="w-10 h-10 bg-muted/20 rounded-xl flex items-center justify-center">
              <CalendarIcon className="w-6 h-6 text-foreground" />
            </div>
            <div>
              <span>Edit Schedule - {member.name}</span>
              <div className="text-sm text-muted-foreground font-normal">Manage team member work schedule</div>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 py-6">
          {/* Calendar Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <CalendarIcon className="w-5 h-5 text-foreground" />
              <h3 className="text-lg font-semibold text-foreground">Select Date</h3>
            </div>
            
            <div className="bg-muted/10 rounded-xl p-4 border border-border/30">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={handleDateSelect}
                className="rounded-md border-0"
                classNames={{
                  months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
                  month: "space-y-4",
                  caption: "flex justify-center pt-1 relative items-center",
                  caption_label: "text-sm font-medium",
                  nav: "space-x-1 flex items-center",
                  nav_button: "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
                  nav_button_previous: "absolute left-1",
                  nav_button_next: "absolute right-1",
                  table: "w-full border-collapse space-y-1",
                  head_row: "flex",
                  head_cell: "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",
                  row: "flex w-full mt-2",
                  cell: "text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
                  day: "h-9 w-9 p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground rounded-md",
                  day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
                  day_today: "bg-accent text-accent-foreground",
                  day_outside: "text-muted-foreground opacity-50",
                  day_disabled: "text-muted-foreground opacity-50",
                  day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
                  day_hidden: "invisible",
                }}
              />
            </div>

            {selectedDate && (
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-700">
                <p className="text-sm text-blue-700 dark:text-blue-300 font-medium">
                  Selected: {format(selectedDate, 'EEEE, MMMM do, yyyy')}
                </p>
              </div>
            )}
          </div>

          {/* Schedule Entry Form */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-foreground" />
              <h3 className="text-lg font-semibold text-foreground">Schedule Details</h3>
            </div>

            <div className="bg-muted/10 rounded-xl p-4 border border-border/30 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startTime" className="text-sm font-medium text-foreground">Start Time</Label>
                  <Input
                    id="startTime"
                    type="time"
                    value={currentEntry.startTime}
                    onChange={(e) => setCurrentEntry(prev => ({ ...prev, startTime: e.target.value }))}
                    className="bg-background border-border/50"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endTime" className="text-sm font-medium text-foreground">End Time</Label>
                  <Input
                    id="endTime"
                    type="time"
                    value={currentEntry.endTime}
                    onChange={(e) => setCurrentEntry(prev => ({ ...prev, endTime: e.target.value }))}
                    className="bg-background border-border/50"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="shiftType" className="text-sm font-medium text-foreground">Shift Type</Label>
                <Select
                  value={currentEntry.shiftType}
                  onValueChange={(value) => setCurrentEntry(prev => ({ ...prev, shiftType: value as any }))}
                >
                  <SelectTrigger className="bg-background border-border/50">
                    <SelectValue placeholder="Select shift type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="regular">Regular Shift</SelectItem>
                    <SelectItem value="overtime">Overtime</SelectItem>
                    <SelectItem value="holiday">Holiday</SelectItem>
                    <SelectItem value="off">Day Off</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button
                onClick={addScheduleEntry}
                disabled={!selectedDate}
                className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Schedule Entry
              </Button>
            </div>
          </div>
        </div>

        {/* Schedule Entries List */}
        {scheduleEntries.length > 0 && (
          <div className="space-y-4 border-t border-border/50 pt-6">
            <h3 className="text-lg font-semibold text-foreground">Scheduled Entries</h3>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {scheduleEntries.map((entry, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-muted/10 rounded-lg border border-border/30">
                  <div className="flex items-center space-x-3">
                    <div className="text-sm font-medium text-foreground">
                      {format(entry.date, 'MMM dd, yyyy')}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {entry.startTime} - {entry.endTime}
                    </div>
                    <Badge className={getShiftTypeColor(entry.shiftType)}>
                      {entry.shiftType}
                    </Badge>
                  </div>
                  <Button
                    onClick={() => removeScheduleEntry(index)}
                    variant="ghost"
                    size="sm"
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        <DialogFooter className="border-t border-border/50 pt-4">
          <Button
            onClick={handleClose}
            variant="outline"
            className="border-border/50 hover:bg-muted/50"
          >
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={scheduleEntries.length === 0}
            className="bg-primary hover:bg-primary/90 text-primary-foreground"
          >
            <Save className="w-4 h-4 mr-2" />
            Save Schedule
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ScheduleEditModal;
