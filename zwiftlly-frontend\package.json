{"name": "zwiftlly-frontend", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "vercel-build": "npm run build"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@react-oauth/google": "^0.12.2", "@reduxjs/toolkit": "^2.8.2", "@supabase/supabase-js": "^2.50.0", "@types/node": "^24.0.3", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.18.1", "lucide-react": "^0.516.0", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "tailwind-scrollbar-hide": "^4.0.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/jest": "^30.0.0", "@types/morgan": "^1.9.10", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}