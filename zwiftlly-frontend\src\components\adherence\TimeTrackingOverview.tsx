import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TrendingUp, Users, Clock, CheckCircle, Coffee } from 'lucide-react';

const TimeTrackingOverview: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('weekly');

  return (
    <Card className="w-full bg-card rounded-xl shadow-lg border border-border/50 overflow-hidden">
      <CardHeader className="pb-4 p-6 border-b border-border/50 bg-gradient-to-r from-background to-muted/20 animate-fade-in animate-delay-0">
        <CardTitle className="text-xl flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-muted/20 rounded-xl shadow-sm border border-border/30">
              <TrendingUp className="w-6 h-6 text-foreground" />
            </div>
            <div>
              <span className="text-foreground font-bold text-xl">Time Tracking Overview</span>
              <div className="text-sm text-muted-foreground font-medium">Performance analytics and insights</div>
            </div>
          </div>
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-36 h-10 text-sm bg-muted/20 border border-border/30 rounded-xl shadow-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
              <SelectItem value="overall">Overall</SelectItem>
            </SelectContent>
          </Select>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div className="space-y-8">
          {/* Enhanced Time Metrics Grid - Full Width */}
          <div className="space-y-6 animate-fade-in animate-delay-150">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-muted/20 rounded-xl flex items-center justify-center shadow-sm">
                <Clock className="w-6 h-6 text-foreground" />
              </div>
              <h4 className="text-xl font-bold text-foreground">Time Metrics</h4>
            </div>
            <div className="grid grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="text-center p-5 bg-gradient-to-br from-card via-card to-card/95 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-border/50">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">312h 45m</div>
                <div className="text-xs text-muted-foreground font-semibold uppercase tracking-wider">Hours Worked</div>
              </div>
              <div className="text-center p-5 bg-gradient-to-br from-card via-card to-card/95 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-border/50">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">18h 30m</div>
                <div className="text-xs text-muted-foreground font-semibold uppercase tracking-wider">Overtime</div>
              </div>
              <div className="text-center p-5 bg-gradient-to-br from-card via-card to-card/95 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-border/50">
                <div className="text-2xl font-bold text-red-600 dark:text-red-400 mb-1">6h 15m</div>
                <div className="text-xs text-muted-foreground font-semibold uppercase tracking-wider">Hours Absent</div>
              </div>
              <div className="text-center p-5 bg-gradient-to-br from-card via-card to-card/95 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-border/50">
                <div className="text-2xl font-bold text-orange-600 dark:text-orange-400 mb-1">24h 12m</div>
                <div className="text-xs text-muted-foreground font-semibold uppercase tracking-wider">Total Idle Time</div>
              </div>
              <div className="text-center p-5 bg-gradient-to-br from-card via-card to-card/95 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-border/50">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1">2h 45m</div>
                <div className="text-xs text-muted-foreground font-semibold uppercase tracking-wider">Technical Issues</div>
              </div>
            </div>
          </div>

          {/* Team Summary and Adherence - Side by Side */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 animate-slide-in-right animate-delay-300">

            {/* Enhanced Team Summary - 2 columns */}
            <div className="lg:col-span-2 space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-muted/20 rounded-xl flex items-center justify-center shadow-sm">
                  <Users className="w-6 h-6 text-foreground" />
                </div>
                <h4 className="text-xl font-bold text-foreground">Team Summary</h4>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between items-center p-4 bg-gradient-to-r from-card via-card to-card/95 rounded-xl shadow-lg border border-border/50">
                  <span className="text-muted-foreground font-semibold">Active:</span>
                  <span className="font-bold text-green-600 dark:text-green-400 text-lg">7 agents</span>
                </div>
                <div className="flex justify-between items-center p-4 bg-gradient-to-r from-card via-card to-card/95 rounded-xl shadow-lg border border-border/50">
                  <span className="text-muted-foreground font-semibold">On Break:</span>
                  <span className="font-bold text-yellow-600 dark:text-yellow-400 text-lg">1 agent</span>
                </div>
                <div className="flex justify-between items-center p-4 bg-gradient-to-r from-card via-card to-card/95 rounded-xl shadow-lg border border-border/50">
                  <span className="text-muted-foreground font-semibold">Technical:</span>
                  <span className="font-bold text-red-600 dark:text-red-400 text-lg">1 agent</span>
                </div>
                <div className="flex justify-between items-center p-4 bg-gradient-to-r from-card via-card to-card/95 rounded-xl shadow-lg border border-border/50">
                  <span className="text-muted-foreground font-semibold">Offline:</span>
                  <span className="font-bold text-white dark:text-white text-lg">0 agents</span>
                </div>
              </div>
            </div>

            {/* Enhanced Adherence Score - 1 column */}
            <div className="lg:col-span-1">
              <div className="h-full p-6 bg-gradient-to-br from-card via-card to-card/95 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-border/50">
                <div className="text-center space-y-4 h-full flex flex-col justify-center">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-8 h-8 bg-muted/20 rounded-full flex items-center justify-center">
                      <CheckCircle className="w-6 h-6 text-foreground" />
                    </div>
                    <div className="text-sm text-muted-foreground font-bold uppercase tracking-wider">Team Adherence</div>
                  </div>
                  <div className="text-5xl font-bold text-green-600 dark:text-green-400">87%</div>
                  <div className="text-sm text-muted-foreground font-medium">Above target <span className="text-green-600 dark:text-green-400">(85%)</span></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TimeTrackingOverview;
