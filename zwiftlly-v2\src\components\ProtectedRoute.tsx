import React from 'react';
import { useAuth } from '../AuthContext';
import { useCompany } from '../contexts/CompanyContext';
import { Shield, AlertTriangle } from 'lucide-react';

// Define permission types based on user roles
type Permission =
  | 'view_dashboard'
  | 'view_announcements'
  | 'create_announcements'
  | 'view_tasks'
  | 'create_tasks'
  | 'manage_tasks'
  | 'view_performance'
  | 'view_calendar'
  | 'view_schedules'
  | 'manage_schedules'
  | 'view_attendance'
  | 'manage_attendance'
  | 'view_knowledge'
  | 'manage_knowledge'
  | 'view_settings'
  | 'manage_settings'
  | 'manage_users'
  | 'admin_access'
  | 'all'; // Special permission that allows everything

// Role-based permissions mapping
const ROLE_PERMISSIONS: Record<string, Permission[]> = {
  'SUPER_ADMIN': [
    'view_dashboard',
    'view_announcements',
    'create_announcements',
    'view_tasks',
    'create_tasks',
    'manage_tasks',
    'view_performance',
    'view_calendar',
    'view_schedules',
    'manage_schedules',
    'view_attendance',
    'manage_attendance',
    'view_knowledge',
    'manage_knowledge',
    'view_settings',
    'manage_settings',
    'manage_users',
    'admin_access',
    'all'
  ],
  'ADMIN': [
    'view_dashboard',
    'view_announcements',
    'create_announcements',
    'view_tasks',
    'create_tasks',
    'manage_tasks',
    'view_performance',
    'view_calendar',
    'view_schedules',
    'manage_schedules',
    'view_attendance',
    'manage_attendance',
    'view_knowledge',
    'manage_knowledge',
    'view_settings',
    'manage_settings',
    'manage_users',
    'admin_access',
    'all'
  ],
  'AGENT': [
    'view_dashboard',
    'view_announcements',
    'view_tasks',
    'create_tasks',
    'view_performance',
    'view_calendar',
    'view_schedules',
    'view_attendance',
    'view_knowledge',
    'view_settings'
  ],
  'USER': [
    'view_dashboard',
    'view_announcements',
    'view_tasks',
    'view_calendar',
    'view_schedules',
    'view_attendance',
    'view_knowledge',
    'view_settings'
  ]
};

interface ProtectedRouteProps {
  children: React.ReactNode;
  permission: Permission;
  fallback?: React.ReactNode;
  showError?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  permission, 
  fallback,
  showError = true 
}) => {
  const { user } = useAuth();
  const { users, isLoading } = useCompany();

  // Show loading state while checking permissions
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="text-center space-y-4">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg mx-auto animate-pulse">
            <Shield className="w-4 h-4 text-white" />
          </div>
          <div className="text-sm text-muted-foreground">Checking permissions...</div>
        </div>
      </div>
    );
  }

  // If no user, deny access
  if (!user) {
    if (fallback) return <>{fallback}</>;
    
    return showError ? (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="text-center space-y-4 max-w-md">
          <div className="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-xl flex items-center justify-center mx-auto">
            <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground mb-2">Authentication Required</h3>
            <p className="text-sm text-muted-foreground">
              You must be logged in to access this feature.
            </p>
          </div>
        </div>
      </div>
    ) : null;
  }

  // Find current user's profile to get role
  const currentUserProfile = users.find(u => u.id === user.id);
  const userRole = currentUserProfile?.role || 'USER';

  // Check if user has the required permission
  const userPermissions = ROLE_PERMISSIONS[userRole] || [];
  const hasPermission = userPermissions.includes(permission) || userPermissions.includes('all');

  // If user doesn't have permission, show error or fallback
  if (!hasPermission) {
    if (fallback) return <>{fallback}</>;
    
    return showError ? (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="text-center space-y-4 max-w-md">
          <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-xl flex items-center justify-center mx-auto">
            <Shield className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground mb-2">Access Restricted</h3>
            <p className="text-sm text-muted-foreground mb-4">
              You don't have permission to access this feature.
            </p>
            <div className="bg-muted/50 rounded-lg p-3">
              <p className="text-xs text-muted-foreground">
                <strong>Your Role:</strong> {userRole}
                <br />
                <strong>Required Permission:</strong> {permission}
              </p>
            </div>
          </div>
        </div>
      </div>
    ) : null;
  }

  // User has permission, render children
  return <>{children}</>;
};

// Helper hook to check permissions programmatically
export const usePermissions = () => {
  const { user } = useAuth();
  const { users } = useCompany();

  const hasPermission = (permission: Permission): boolean => {
    if (!user) return false;
    
    const currentUserProfile = users.find(u => u.id === user.id);
    const userRole = currentUserProfile?.role || 'USER';
    const userPermissions = ROLE_PERMISSIONS[userRole] || [];
    
    return userPermissions.includes(permission) || userPermissions.includes('all');
  };

  const getUserRole = (): string => {
    if (!user) return 'GUEST';
    
    const currentUserProfile = users.find(u => u.id === user.id);
    return currentUserProfile?.role || 'USER';
  };

  const isAdmin = (): boolean => {
    return hasPermission('admin_access');
  };

  const canManage = (resource: string): boolean => {
    return hasPermission(`manage_${resource}` as Permission);
  };

  return {
    hasPermission,
    getUserRole,
    isAdmin,
    canManage,
    user: user ? users.find(u => u.id === user.id) : null
  };
};

export default ProtectedRoute;
