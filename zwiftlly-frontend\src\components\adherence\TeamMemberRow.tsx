import React, { useState, useEffect } from 'react';

interface TimeBlock {
  startTime: number; // hour in 24h format
  endTime: number;
  status: 'online' | 'technical' | 'offline' | 'break' | 'meeting';
  activity?: string;
  project?: string;
}

interface TeamMemberRowProps {
  member: {
    id: string;
    name: string;
    avatar?: string;
    status: 'online' | 'technical' | 'offline' | 'break' | 'meeting';
    clockedIn: boolean;
    clockInTime?: string;
  };
  timeBlocks: TimeBlock[];
  startHour: number;
  endHour: number;
}

const TeamMemberRow: React.FC<TeamMemberRowProps> = ({
  member,
  timeBlocks,
  startHour = 8,
  endHour = 17
}) => {
  const totalHours = endHour - startHour;
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return '#22c55e';
      case 'technical': return '#3b82f6';
      case 'offline': return '#8b5cf6';
      case 'break': return '#f59e0b';
      case 'meeting': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatusPattern = (status: string) => {
    switch (status) {
      case 'online':
        return 'solid';
      case 'technical':
        return 'diagonal';
      case 'offline':
        return 'solid';
      case 'break':
        return 'dots';
      case 'meeting':
        return 'waves';
      default:
        return 'solid';
    }
  };

  const getBlockStyle = (block: TimeBlock) => {
    const startPercent = ((block.startTime - startHour) / totalHours) * 100;
    const widthPercent = ((block.endTime - block.startTime) / totalHours) * 100;
    const color = getStatusColor(block.status);
    const pattern = getStatusPattern(block.status);

    let background = color;
    
    if (pattern === 'diagonal') {
      background = `repeating-linear-gradient(
        45deg,
        ${color},
        ${color} 3px,
        transparent 3px,
        transparent 6px
      )`;
    } else if (pattern === 'dots') {
      background = `radial-gradient(circle, ${color} 1.5px, transparent 1.5px)`;
    } else if (pattern === 'waves') {
      background = `repeating-linear-gradient(
        90deg,
        ${color} 0px,
        ${color} 3px,
        transparent 3px,
        transparent 6px
      )`;
    }

    return {
      position: 'absolute' as const,
      left: `${startPercent}%`,
      width: `${widthPercent}%`,
      height: '24px',
      background,
      borderRadius: '4px',
      border: `1px solid ${color}`,
      top: '50%',
      transform: 'translateY(-50%)',
      zIndex: 1
    };
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-100 text-green-800 border-green-200';
      case 'technical': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'offline': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'break': return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'meeting': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="flex border-b border-border hover:bg-muted/20 transition-colors">
      {/* Team member info */}
      <div className="w-48 p-3 border-r border-border flex items-center space-x-3 flex-shrink-0">
        <div className="w-8 h-8 bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center">
          <span className="text-xs font-bold text-indigo-600 dark:text-indigo-400">
            {getInitials(member.name)}
          </span>
        </div>
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-foreground truncate">
            {member.name}
          </div>
        </div>
      </div>

      {/* Timeline area */}
      <div className="relative h-16 bg-muted/5 min-w-max" style={{ width: `${totalHours * 96}px` }}>
        {/* Time blocks */}
        {timeBlocks.map((block, index) => (
          <div
            key={index}
            style={getBlockStyle(block)}
            className="group cursor-pointer"
            title={`${block.status} ${block.startTime}:00 - ${block.endTime}:00${block.project ? ` | ${block.project}` : ''}${block.activity ? ` | ${block.activity}` : ''}`}
          >
            {/* Activity label inside block if there's space */}
            {block.activity && (block.endTime - block.startTime) >= 2 && (
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xs font-medium truncate px-1" style={{
                  color: block.status === 'meeting' ? '#000' : '#fff'
                }}>
                  {block.activity}
                </span>
              </div>
            )}
          </div>
        ))}

        {/* Hour grid lines */}
        {Array.from({ length: totalHours + 1 }, (_, i) => (
          <div
            key={i}
            className="absolute top-0 bottom-0 border-l border-border/30"
            style={{ left: `${(i / totalHours) * 100}%` }}
          />
        ))}

        {/* Live time indicator - red vertical line */}
        <div
          className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-20 animate-pulse"
          style={{
            left: `${((currentTime.getHours() + currentTime.getMinutes() / 60 - startHour) / totalHours) * 100}%`
          }}
        >
          <div className="absolute -top-1 -left-1 w-2 h-2 bg-red-500 rounded-full"></div>
        </div>
      </div>
    </div>
  );
};

export default TeamMemberRow;
