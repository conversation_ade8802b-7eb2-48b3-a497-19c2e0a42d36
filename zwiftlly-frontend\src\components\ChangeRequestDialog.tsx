import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { AlertTriangle, FileEdit, CheckCircle } from 'lucide-react';
import CustomDialog from '@/components/ui/custom-dialog';

interface ChangeRequestDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmitRequest: (fields: string[]) => void;
}

const ChangeRequestDialog: React.FC<ChangeRequestDialogProps> = ({
  isOpen,
  onClose,
  onSubmitRequest
}) => {
  const [selectedFields, setSelectedFields] = useState<string[]>([]);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const availableFields = [
    { id: 'firstName', label: 'First Name' },
    { id: 'lastName', label: 'Last Name' },
    { id: 'phone', label: 'Phone Number' },
    { id: 'companyRole', label: 'Company Role' },
    { id: 'hireDate', label: 'Hire Date' },
    { id: 'avatar', label: 'Profile Picture' }
  ];

  const handleFieldToggle = (fieldId: string) => {
    setSelectedFields(prev => 
      prev.includes(fieldId) 
        ? prev.filter(id => id !== fieldId)
        : [...prev, fieldId]
    );
  };

  const handleSubmit = () => {
    if (selectedFields.length === 0) return;
    setShowConfirmation(true);
  };

  const handleConfirmSubmit = () => {
    onSubmitRequest(selectedFields);

    // Simulate sending notification to admins
    console.log('📧 Notification sent to admins:', {
      type: 'profile_change_request',
      user: 'Current User',
      fields: selectedFields,
      timestamp: new Date().toISOString()
    });

    setShowConfirmation(false);
    setSelectedFields([]);
    onClose();
  };

  const handleCancel = () => {
    setSelectedFields([]);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <div 
          className="absolute inset-0 bg-black/50 backdrop-blur-sm animate-fade-in"
          onClick={handleCancel}
        />
        
        {/* Dialog */}
        <div className="relative bg-card border border-border/50 rounded-xl shadow-2xl max-w-md w-full mx-4 animate-scale-in">
          {/* Header */}
          <CardHeader className="p-6 border-b border-border/50 bg-gradient-to-r from-background to-muted/20">
            <CardTitle className="text-xl flex items-center space-x-3">
              <div className="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-xl shadow-sm border border-orange-200 dark:border-orange-800/30">
                <FileEdit className="w-6 h-6 text-orange-600" />
              </div>
              <div>
                <span className="text-foreground font-bold text-xl">Request Profile Changes</span>
                <div className="text-sm text-muted-foreground font-medium">Select fields to modify</div>
              </div>
            </CardTitle>
          </CardHeader>

          {/* Content */}
          <CardContent className="p-6 space-y-4">
            <div className="bg-yellow-50 dark:bg-yellow-900/10 border border-yellow-200 dark:border-yellow-800/30 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-yellow-800 dark:text-yellow-300">Profile Update Restriction</p>
                  <p className="text-yellow-700 dark:text-yellow-400 mt-1">
                    Since you've already updated your profile once, any further changes require admin approval.
                  </p>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium text-foreground mb-3">Select fields you want to change:</h4>
              <div className="space-y-3">
                {availableFields.map((field) => (
                  <div key={field.id} className="flex items-center space-x-3">
                    <Checkbox
                      id={field.id}
                      checked={selectedFields.includes(field.id)}
                      onCheckedChange={() => handleFieldToggle(field.id)}
                    />
                    <label 
                      htmlFor={field.id}
                      className="text-sm font-medium text-foreground cursor-pointer"
                    >
                      {field.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {selectedFields.length > 0 && (
              <div className="bg-blue-50 dark:bg-blue-900/10 border border-blue-200 dark:border-blue-800/30 rounded-lg p-3">
                <p className="text-sm text-blue-800 dark:text-blue-300">
                  <strong>Selected fields:</strong> {selectedFields.map(id => 
                    availableFields.find(f => f.id === id)?.label
                  ).join(', ')}
                </p>
              </div>
            )}
          </CardContent>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-border/50 bg-muted/20">
            <Button
              variant="outline"
              onClick={handleCancel}
              className="border-border/50"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={selectedFields.length === 0}
              className="bg-orange-600 hover:bg-orange-700 text-white"
            >
              Submit Request
            </Button>
          </div>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <CustomDialog
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        title="Confirm Change Request"
        message={`Are you sure you want to submit a change request for: ${selectedFields.map(id => 
          availableFields.find(f => f.id === id)?.label
        ).join(', ')}? This will notify administrators for approval.`}
        type="confirm"
        onConfirm={handleConfirmSubmit}
        confirmText="Yes, Submit Request"
        cancelText="Cancel"
      />
    </>
  );
};

export default ChangeRequestDialog;
