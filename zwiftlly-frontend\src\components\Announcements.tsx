import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Search, Plus, Calendar, User, Eye, Edit, Trash2, MessageCircle, Send } from 'lucide-react';
import { useAuth } from '@/contexts/SupabaseAuthContext';
import AnnouncementDraftModal from './AnnouncementDraftModal';

interface Comment {
  id: string;
  content: string;
  author: string;
  authorEmail: string;
  createdAt: string;
}

interface Announcement {
  id: string;
  title: string;
  content: string;
  author: string;
  authorEmail: string;
  createdAt: string;
  views: number;
  isLatest?: boolean;
  category: 'qa' | 'engineering' | 'management' | 'general';
  comments: Comment[];
}

const Announcements: React.FC = () => {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [filteredAnnouncements, setFilteredAnnouncements] = useState<Announcement[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'qa' | 'engineering' | 'management'>('all');
  const [expandedAnnouncement, setExpandedAnnouncement] = useState<string | null>(null);
  const [newComment, setNewComment] = useState<{ [key: string]: string }>({});
  const [isAnnouncementModalOpen, setIsAnnouncementModalOpen] = useState(false);

  // Mock announcements data
  const mockAnnouncements: Announcement[] = [
    {
      id: '1',
      title: 'New Quality Assurance Process Implementation',
      content: 'We are excited to announce the implementation of our new quality assurance process. This comprehensive system will help us maintain the highest standards of service delivery and ensure consistent quality across all our operations. The new process includes automated checks, peer reviews, and continuous monitoring to identify areas for improvement. All team members are required to complete the training module by the end of this week. Please check your email for detailed instructions and schedule your training session accordingly.',
      author: 'Sarah Johnson',
      authorEmail: '<EMAIL>',
      createdAt: 'Updated June 19, 2025 7pm',
      views: 234,
      isLatest: true,
      category: 'qa',
      comments: [
        {
          id: 'c1',
          content: 'This looks great! When will the training materials be available?',
          author: 'John Doe',
          authorEmail: '<EMAIL>',
          createdAt: 'June 19, 2025 8:30pm'
        }
      ]
    },
    {
      id: '2',
      title: 'Team Performance Review Schedule',
      content: 'The quarterly team performance reviews are scheduled for next week. Please prepare your self-assessment forms and gather any supporting documentation.',
      author: 'Mike Chen',
      authorEmail: '<EMAIL>',
      createdAt: 'Updated June 18, 2025 3pm',
      views: 156,
      category: 'management',
      comments: []
    },
    {
      id: '3',
      title: 'New Code Review Guidelines',
      content: 'Updated engineering standards and code review processes to improve code quality and development efficiency across all projects.',
      author: 'Engineering Lead',
      authorEmail: '<EMAIL>',
      createdAt: 'Updated June 17, 2025 11am',
      views: 89,
      category: 'engineering',
      comments: []
    },
    {
      id: '4',
      title: 'Automated Testing Framework Update',
      content: 'Enhanced QA testing framework with new automation tools. All QA team members should familiarize themselves with the new testing procedures.',
      author: 'QA Team Lead',
      authorEmail: '<EMAIL>',
      createdAt: 'Updated June 16, 2025 9am',
      views: 198,
      category: 'qa',
      comments: []
    },
    {
      id: '5',
      title: 'Engineering Best Practices Workshop',
      content: 'Join us for a comprehensive workshop on engineering best practices, including clean code principles and architectural patterns.',
      author: 'Senior Engineer',
      authorEmail: '<EMAIL>',
      createdAt: 'Updated June 15, 2025 2pm',
      views: 67,
      category: 'engineering',
      comments: []
    },
    {
      id: '6',
      title: 'Quarterly Business Review',
      content: 'Management team will present Q4 results and discuss strategic initiatives for the upcoming quarter.',
      author: 'Management Team',
      authorEmail: '<EMAIL>',
      createdAt: 'Updated June 14, 2025 4pm',
      views: 145,
      category: 'management',
      comments: []
    }
  ];

  useEffect(() => {
    setAnnouncements(mockAnnouncements);
    setFilteredAnnouncements(mockAnnouncements);
  }, []);

  useEffect(() => {
    // Filter announcements based on search query and category
    let filtered = announcements;

    // Filter by category first
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(announcement => announcement.category === selectedCategory);
    }

    // Then filter by search query
    if (searchQuery.trim() !== '') {
      filtered = filtered.filter(announcement =>
        announcement.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        announcement.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        announcement.author.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredAnnouncements(filtered);
  }, [searchQuery, announcements, selectedCategory]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
  };

  const latestAnnouncement = filteredAnnouncements.find(a => a.isLatest);
  const pastAnnouncements = filteredAnnouncements.filter(a => !a.isLatest);



  const truncateContent = (content: string, percentage: number) => {
    const maxLength = Math.floor(content.length * (percentage / 100));
    return content.length > maxLength ? content.substring(0, maxLength) + '...' : content;
  };

  const handleAddComment = (announcementId: string) => {
    const commentText = newComment[announcementId]?.trim();
    if (!commentText || !user) return;

    const comment: Comment = {
      id: `c${Date.now()}`,
      content: commentText,
      author: user.name,
      authorEmail: user.email,
      createdAt: new Date().toLocaleString()
    };

    setAnnouncements(prev => prev.map(announcement =>
      announcement.id === announcementId
        ? { ...announcement, comments: [...announcement.comments, comment] }
        : announcement
    ));

    // Clear the comment input
    setNewComment(prev => ({ ...prev, [announcementId]: '' }));

    // Create notification for announcement author
    const announcement = announcements.find(a => a.id === announcementId);
    if (announcement && announcement.authorEmail !== user.email) {
      // Here you would typically send a notification to the announcement author
      console.log(`Notification: ${user.name} commented on your announcement "${announcement.title}"`);
    }
  };

  const toggleComments = (announcementId: string) => {
    setExpandedAnnouncement(prev => prev === announcementId ? null : announcementId);
  };

  const handleSaveAnnouncement = (newAnnouncement: any) => {
    setAnnouncements(prev => [newAnnouncement, ...prev]);
    console.log('New announcement saved:', newAnnouncement);
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Clean Minimalistic Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-foreground mb-1">Announcements</h1>
              <p className="text-slate-600 dark:text-slate-400 text-sm">
                Stay updated with the latest news and updates
              </p>
            </div>

            {/* Category Buttons and Add Button */}
            <div className="flex items-center space-x-3">
              {/* Category Filter Buttons */}
              <div className="flex items-center space-x-2">
                <Button
                  onClick={() => setSelectedCategory('qa')}
                  variant={selectedCategory === 'qa' ? 'default' : 'outline'}
                  size="sm"
                  className="text-sm"
                >
                  QA Team
                </Button>
                <Button
                  onClick={() => setSelectedCategory('engineering')}
                  variant={selectedCategory === 'engineering' ? 'default' : 'outline'}
                  size="sm"
                  className="text-sm"
                >
                  Engineering
                </Button>
                <Button
                  onClick={() => setSelectedCategory('management')}
                  variant={selectedCategory === 'management' ? 'default' : 'outline'}
                  size="sm"
                  className="text-sm"
                >
                  Management
                </Button>
                {selectedCategory !== 'all' && (
                  <Button
                    onClick={() => setSelectedCategory('all')}
                    variant="ghost"
                    size="sm"
                    className="text-sm text-muted-foreground"
                  >
                    Clear Filter
                  </Button>
                )}
              </div>

              {/* Circular Add Button with Dark/Light Theme */}
              <Button
                onClick={() => setIsAnnouncementModalOpen(true)}
                className="group w-12 h-12 rounded-full bg-gray-900 dark:bg-white hover:bg-gray-800 dark:hover:bg-gray-100 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border-0"
                title="Add announcement"
              >
                <Plus className="w-5 h-5 text-white dark:text-gray-900 group-hover:rotate-90 transition-transform duration-300" />
              </Button>
            </div>
          </div>

          <div className="flex items-center justify-between gap-4">
            {/* Clean Search Bar */}
            <div className="flex-1 max-w-md">
              <form onSubmit={handleSearch} className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                <Input
                  type="text"
                  placeholder="Search announcements..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full bg-slate-50 dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
                />
              </form>
            </div>

            {/* Clean Count Badge */}
            <div className="px-3 py-2 bg-slate-100 dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700">
              <span className="text-sm text-slate-600 dark:text-slate-400">
                {filteredAnnouncements.length} announcement{filteredAnnouncements.length !== 1 ? 's' : ''}
              </span>
            </div>
          </div>
        </div>

        {/* Modern Minimalistic Latest Announcement */}
        {latestAnnouncement && (
          <div className="mb-8">
            <h2 className="text-xl font-bold text-foreground mb-4">Latest Announcement</h2>

            <Card className="announcement-card transition-all duration-300 hover:shadow-lg group relative overflow-hidden bg-card shadow-sm border border-slate-200/50 dark:border-slate-700/50">
              {/* Subtle hover effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/10 via-transparent to-indigo-50/5 dark:from-blue-900/3 dark:via-transparent dark:to-indigo-900/2 opacity-0 group-hover:opacity-100 transition-all duration-300" />

              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-xl font-bold text-foreground mb-3 leading-tight">
                      {latestAnnouncement.title}
                    </CardTitle>

                    {/* Clean Metadata */}
                    <div className="flex items-center gap-4 text-sm text-slate-600 dark:text-slate-400 mb-4">
                      <span className="font-medium">{latestAnnouncement.author}</span>
                      <span>•</span>
                      <span>{latestAnnouncement.createdAt}</span>
                      <span>•</span>
                      <span>{latestAnnouncement.views} views</span>
                    </div>
                  </div>

                  {/* Clean Action Buttons */}
                  <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <Button
                      onClick={() => alert(`Edit announcement: ${latestAnnouncement.title}`)}
                      variant="ghost"
                      size="sm"
                      className="w-8 h-8 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
                      title="Edit announcement"
                    >
                      <Edit className="w-3.5 h-3.5 text-slate-500 dark:text-slate-400" />
                    </Button>
                    <Button
                      onClick={() => alert(`Delete announcement: ${latestAnnouncement.title}`)}
                      variant="ghost"
                      size="sm"
                      className="w-8 h-8 rounded-lg hover:bg-rose-50 dark:hover:bg-rose-900/20 transition-colors"
                      title="Delete announcement"
                    >
                      <Trash2 className="w-3.5 h-3.5 text-slate-500 dark:text-slate-400 hover:text-rose-600 dark:hover:text-rose-400" />
                    </Button>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <p className="text-slate-600 dark:text-slate-400 leading-relaxed mb-4">
                  {truncateContent(latestAnnouncement.content, 50)}
                </p>

                {/* Comments Section */}
                <div className="border-t border-border/50 pt-4">
                  <div className="flex items-center justify-between mb-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleComments(latestAnnouncement.id)}
                      className="text-sm text-muted-foreground hover:text-foreground"
                    >
                      <MessageCircle className="w-4 h-4 mr-2" />
                      {latestAnnouncement.comments.length} comment{latestAnnouncement.comments.length !== 1 ? 's' : ''}
                    </Button>
                  </div>

                  {expandedAnnouncement === latestAnnouncement.id && (
                    <div className="space-y-3">
                      {/* Existing Comments */}
                      {latestAnnouncement.comments.map((comment) => (
                        <div key={comment.id} className="bg-muted/30 rounded-lg p-3">
                          <div className="flex items-start justify-between mb-2">
                            <span className="font-medium text-sm text-foreground">{comment.author}</span>
                            <span className="text-xs text-muted-foreground">{comment.createdAt}</span>
                          </div>
                          <p className="text-sm text-muted-foreground">{comment.content}</p>
                        </div>
                      ))}

                      {/* Add Comment */}
                      {user && (
                        <div className="flex space-x-2">
                          <Textarea
                            placeholder="Add a comment..."
                            value={newComment[latestAnnouncement.id] || ''}
                            onChange={(e) => setNewComment(prev => ({ ...prev, [latestAnnouncement.id]: e.target.value }))}
                            className="flex-1 min-h-[80px] resize-none"
                          />
                          <Button
                            onClick={() => handleAddComment(latestAnnouncement.id)}
                            disabled={!newComment[latestAnnouncement.id]?.trim()}
                            size="sm"
                            className="self-end"
                          >
                            <Send className="w-4 h-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Modern Minimalistic Past Announcements */}
        {pastAnnouncements.length > 0 && (
          <div>
            <h2 className="text-xl font-bold text-foreground mb-4">Past Announcements</h2>

            <div className="space-y-3">
              {pastAnnouncements.map((announcement, index) => (
                <Card
                  key={announcement.id}
                  className="announcement-card cursor-pointer transition-all duration-300 hover:shadow-md group relative overflow-hidden bg-card shadow-sm border border-slate-200/50 dark:border-slate-700/50"
                  style={{
                    animationDelay: `${index * 50}ms`,
                    animation: 'fadeInUp 0.4s ease-out forwards',
                    opacity: 0
                  }}
                >
                  {/* Subtle hover effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-slate-50/20 via-transparent to-gray-50/10 dark:from-slate-800/10 dark:via-transparent dark:to-gray-800/5 opacity-0 group-hover:opacity-100 transition-all duration-300" />
                  <div className="absolute left-0 top-0 w-1 h-full bg-blue-500/50 transform scale-y-0 group-hover:scale-y-100 transition-transform duration-300 origin-top" />

                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-semibold text-foreground mb-2 leading-tight">
                          {announcement.title}
                        </h3>
                        <p className="text-slate-600 dark:text-slate-400 mb-3 leading-relaxed line-clamp-2 text-sm">
                          {truncateContent(announcement.content, 20)}
                        </p>

                        {/* Clean Metadata */}
                        <div className="flex items-center gap-3 text-xs text-slate-500 dark:text-slate-400 mb-3">
                          <span>{announcement.author}</span>
                          <span>•</span>
                          <span>{announcement.createdAt}</span>
                          <span>•</span>
                          <span>{announcement.views} views</span>
                        </div>

                        {/* Comments Button */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleComments(announcement.id)}
                          className="text-xs text-muted-foreground hover:text-foreground p-0 h-auto"
                        >
                          <MessageCircle className="w-3 h-3 mr-1" />
                          {announcement.comments.length} comment{announcement.comments.length !== 1 ? 's' : ''}
                        </Button>
                      </div>

                      {/* Clean Action Buttons */}
                      <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-300">
                        <Button
                          onClick={() => alert(`Edit announcement: ${announcement.title}`)}
                          variant="ghost"
                          size="sm"
                          className="w-7 h-7 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
                          title="Edit announcement"
                        >
                          <Edit className="w-3 h-3 text-slate-500 dark:text-slate-400" />
                        </Button>
                        <Button
                          onClick={() => alert(`Delete announcement: ${announcement.title}`)}
                          variant="ghost"
                          size="sm"
                          className="w-7 h-7 rounded-lg hover:bg-rose-50 dark:hover:bg-rose-900/20 transition-colors"
                          title="Delete announcement"
                        >
                          <Trash2 className="w-3 h-3 text-slate-500 dark:text-slate-400 hover:text-rose-600 dark:hover:text-rose-400" />
                        </Button>
                      </div>
                    </div>

                    {/* Comments Section */}
                    {expandedAnnouncement === announcement.id && (
                      <div className="mt-4 pt-4 border-t border-border/50 space-y-3">
                        {/* Existing Comments */}
                        {announcement.comments.map((comment) => (
                          <div key={comment.id} className="bg-muted/30 rounded-lg p-3">
                            <div className="flex items-start justify-between mb-2">
                              <span className="font-medium text-sm text-foreground">{comment.author}</span>
                              <span className="text-xs text-muted-foreground">{comment.createdAt}</span>
                            </div>
                            <p className="text-sm text-muted-foreground">{comment.content}</p>
                          </div>
                        ))}

                        {/* Add Comment */}
                        {user && (
                          <div className="flex space-x-2">
                            <Textarea
                              placeholder="Add a comment..."
                              value={newComment[announcement.id] || ''}
                              onChange={(e) => setNewComment(prev => ({ ...prev, [announcement.id]: e.target.value }))}
                              className="flex-1 min-h-[80px] resize-none"
                            />
                            <Button
                              onClick={() => handleAddComment(announcement.id)}
                              disabled={!newComment[announcement.id]?.trim()}
                              size="sm"
                              className="self-end"
                            >
                              <Send className="w-4 h-4" />
                            </Button>
                          </div>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Clean Empty State */}
        {filteredAnnouncements.length === 0 && (
          <div className="text-center py-16">
            <div className="w-16 h-16 mx-auto mb-4 bg-slate-100 dark:bg-slate-800 rounded-full flex items-center justify-center">
              <span className="text-2xl">📢</span>
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">No announcements found</h3>
            <p className="text-slate-600 dark:text-slate-400 mb-6 max-w-md mx-auto">
              {searchQuery ? 'Try adjusting your search terms or browse all announcements' : 'No announcements are currently available. Check back later for updates!'}
            </p>
            {searchQuery && (
              <Button
                onClick={() => setSearchQuery('')}
                variant="outline"
                className="px-4 py-2 rounded-lg border-slate-300 dark:border-slate-600 hover:border-blue-400 dark:hover:border-blue-500 transition-colors"
              >
                <span className="text-slate-700 dark:text-slate-300">Clear Search</span>
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Add CSS animations */}
      <style>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .announcement-card {
          animation: fadeInUp 0.6s ease-out forwards;
        }

        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>

      {/* Announcement Draft Modal */}
      <AnnouncementDraftModal
        isOpen={isAnnouncementModalOpen}
        onClose={() => setIsAnnouncementModalOpen(false)}
        onSave={handleSaveAnnouncement}
      />
    </div>
  );
};

export default Announcements;
