import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface TimeZone {
  name: string;
  timezone: string;
  abbreviation: string;
}

const TimeZoneDisplay: React.FC = () => {
  const [times, setTimes] = useState<{ [key: string]: string }>({});
  const [format24Hour, setFormat24Hour] = useState(false);

  const timeZones: TimeZone[] = [
    { name: 'Eastern', timezone: 'America/New_York', abbreviation: 'EST/EDT' },
    { name: 'Central', timezone: 'America/Chicago', abbreviation: 'CST/CDT' },
    { name: 'Mountain', timezone: 'America/Denver', abbreviation: 'MST/MDT' },
    { name: 'Pacific', timezone: 'America/Los_Angeles', abbreviation: 'PST/PDT' },
    { name: 'Philippines', timezone: 'Asia/Manila', abbreviation: 'PHT' },
  ];

  useEffect(() => {
    const updateTimes = () => {
      const newTimes: { [key: string]: string } = {};
      
      timeZones.forEach(tz => {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
          timeZone: tz.timezone,
          hour12: !format24Hour,
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
        newTimes[tz.name] = timeString;
      });
      
      setTimes(newTimes);
    };

    updateTimes();
    const interval = setInterval(updateTimes, 1000);

    return () => clearInterval(interval);
  }, [format24Hour]);

  const getTimeZoneDate = (timezone: string) => {
    return new Date().toLocaleDateString('en-US', {
      timeZone: timezone,
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">Time Zones</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setFormat24Hour(!format24Hour)}
            className="h-7 px-2 text-xs"
          >
            {format24Hour ? '12H' : '24H'}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex space-x-3 overflow-x-auto pb-2">
          {timeZones.map((tz) => (
            <div key={tz.name} className="flex-shrink-0 w-32">
              <div className="p-2 bg-muted rounded-lg text-center">
                <div className="font-medium text-foreground text-xs mb-1">
                  {tz.name}
                </div>
                <div className="text-xs text-muted-foreground mb-1">
                  {tz.abbreviation}
                </div>
                <div className="text-sm font-mono font-bold text-foreground mb-1">
                  {times[tz.name] || '--:--:--'}
                </div>
                <div className="text-xs text-muted-foreground">
                  {getTimeZoneDate(tz.timezone)}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-3 text-xs text-muted-foreground text-center">
          Updates every second • Automatic DST
        </div>
      </CardContent>
    </Card>
  );
};

export default TimeZoneDisplay;
