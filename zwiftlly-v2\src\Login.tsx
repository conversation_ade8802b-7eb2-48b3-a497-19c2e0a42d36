import React, { useState } from 'react'
import { useAuth } from './AuthContext'

const Login: React.FC = () => {
  const [error, setError] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { signInWithGoogle, isLoading } = useAuth()

  const handleGoogleAuth = async () => {
    setIsSubmitting(true)
    setError('')

    try {
      const result = await signInWithGoogle()
      
      if (!result.success) {
        setError(result.message)
        setIsSubmitting(false)
      }
    } catch (error: any) {
      setError(error.message || 'Authentication failed')
      setIsSubmitting(false)
    }
  }

  const isFormDisabled = isLoading || isSubmitting

  return (
    <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: '#f8fafc', padding: '1rem' }}>
      <div style={{ width: '100%', maxWidth: '400px' }}>
        {/* Logo and Title */}
        <div className="text-center" style={{ marginBottom: '2rem' }}>
          <div style={{
            width: '64px',
            height: '64px',
            background: 'linear-gradient(to right, #2563eb, #7c3aed)',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 1rem',
            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
          }}>
            <span style={{ color: 'white', fontSize: '24px', fontWeight: 'bold' }}>Z</span>
          </div>
          <div>
            <h1 style={{ fontSize: '2rem', fontWeight: 'bold', margin: '0 0 0.5rem' }}>ZWIFTLLY</h1>
            <p style={{ color: '#6b7280', margin: 0 }}>Team Management System</p>
          </div>
        </div>

        {/* Login Card */}
        <div className="bg-white shadow-lg rounded-lg" style={{ overflow: 'hidden' }}>
          <div style={{ padding: '1.5rem', textAlign: 'center', borderBottom: '1px solid #e5e7eb' }}>
            <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', margin: '0 0 0.5rem' }}>Welcome Back</h2>
            <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>
              Sign in with your organization account
            </p>
          </div>
          
          <div style={{ padding: '1.5rem' }}>
            {/* Security Features */}
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '1rem', textAlign: 'center', marginBottom: '1.5rem' }}>
              <div>
                <div style={{ width: '24px', height: '24px', margin: '0 auto 0.5rem', color: '#2563eb' }}>🛡️</div>
                <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>Secure Auth</p>
              </div>
              <div>
                <div style={{ width: '24px', height: '24px', margin: '0 auto 0.5rem', color: '#7c3aed' }}>👥</div>
                <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>Organization Access</p>
              </div>
              <div>
                <div style={{ width: '24px', height: '24px', margin: '0 auto 0.5rem', color: '#059669' }}>🌐</div>
                <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>Domain Verified</p>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div style={{
                border: '1px solid #fca5a5',
                borderRadius: '0.5rem',
                padding: '0.75rem',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                backgroundColor: '#fef2f2',
                marginBottom: '1.5rem'
              }}>
                <span style={{ color: '#dc2626', fontSize: '1rem' }}>⚠️</span>
                <p style={{ fontSize: '0.875rem', color: '#dc2626', margin: 0 }}>{error}</p>
              </div>
            )}

            {/* Domain Restrictions Info */}
            <div style={{
              backgroundColor: '#eff6ff',
              border: '1px solid #bfdbfe',
              borderRadius: '0.5rem',
              padding: '1rem',
              marginBottom: '1.5rem'
            }}>
              <div style={{ display: 'flex', alignItems: 'flex-start', gap: '0.75rem' }}>
                <span style={{ color: '#2563eb', fontSize: '1.25rem' }}>🌐</span>
                <div>
                  <h4 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#1e3a8a', margin: '0 0 0.25rem' }}>
                    Authorized Domains Only
                  </h4>
                  <p style={{ fontSize: '0.75rem', color: '#1e40af', margin: 0 }}>
                    Only users with netic.ai email addresses can sign in.
                  </p>
                </div>
              </div>
            </div>

            {/* Google Sign In Button */}
            <button
              onClick={handleGoogleAuth}
              disabled={isFormDisabled}
              style={{
                width: '100%',
                backgroundColor: 'white',
                color: '#374151',
                fontWeight: '500',
                padding: '0.75rem 1.5rem',
                borderRadius: '0.5rem',
                border: '1px solid #d1d5db',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '0.75rem',
                boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
                cursor: isFormDisabled ? 'not-allowed' : 'pointer',
                opacity: isFormDisabled ? 0.5 : 1,
                transition: 'all 0.2s',
                marginBottom: '1.5rem'
              }}
              onMouseOver={(e) => {
                if (!isFormDisabled) {
                  e.currentTarget.style.backgroundColor = '#f9fafb'
                  e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }
              }}
              onMouseOut={(e) => {
                if (!isFormDisabled) {
                  e.currentTarget.style.backgroundColor = 'white'
                  e.currentTarget.style.boxShadow = '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
                }
              }}
            >
              {isFormDisabled ? (
                <div style={{
                  width: '20px',
                  height: '20px',
                  border: '2px solid #d1d5db',
                  borderTop: '2px solid #374151',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }} />
              ) : (
                <svg width="20" height="20" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
              )}
              <span>Sign in with Google</span>
            </button>

            {/* Organization Info */}
            <div className="text-center">
              <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: '0 0 0.5rem' }}>
                Only authorized organization domains can access this system
              </p>
              <p style={{ fontSize: '0.75rem', color: '#6b7280', fontWeight: '500', margin: 0 }}>
                Contact your administrator if you need access
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center" style={{ marginTop: '1.5rem' }}>
          <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>
            © 2025 ZWIFTLLY Team Management System
          </p>
        </div>
      </div>

      <style>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}

export default Login
