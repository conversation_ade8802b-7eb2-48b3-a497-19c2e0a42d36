import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Avatar, AvatarFallback } from './ui/avatar';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './ui/dialog';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Plus, MessageSquare, Heart, Share2, MoreHorizontal, Send, X, Bold, Italic, List, Link, Image, Calendar } from 'lucide-react';
import { announcementApi } from '../lib/api';
import { useAuth } from '../AuthContext';

const Announcements: React.FC = () => {
  const { user } = useAuth();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [announcements, setAnnouncements] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showDraftModal, setShowDraftModal] = useState(false);
  const [draftForm, setDraftForm] = useState<{
    title: string;
    content: string;
    category: 'QA' | 'ENGINEERING' | 'MANAGEMENT' | 'GENERAL';
    is_important: boolean;
  }>({
    title: '',
    content: '',
    category: 'GENERAL',
    is_important: false
  });

  const categories = [
    { id: 'all', label: 'All', count: 0 },
    { id: 'qa', label: 'QA', count: 0 },
    { id: 'engineering', label: 'Engineering', count: 0 },
    { id: 'management', label: 'Management', count: 0 }
  ];

  useEffect(() => {
    loadAnnouncements();
  }, []);

  const loadAnnouncements = async () => {
    try {
      setLoading(true);
      const data = await announcementApi.getAnnouncements();
      setAnnouncements(data);

      // Update category counts
      categories.forEach(cat => {
        if (cat.id === 'all') {
          cat.count = data.length;
        } else {
          cat.count = data.filter((ann: any) => ann.category?.toLowerCase() === cat.id).length;
        }
      });
    } catch (error) {
      console.error('Error loading announcements:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAnnouncement = async () => {
    if (!draftForm.title.trim() || !draftForm.content.trim()) return;

    try {
      await announcementApi.createAnnouncement({
        title: draftForm.title,
        content: draftForm.content,
        category: draftForm.category,
        is_important: draftForm.is_important
      });

      setShowDraftModal(false);
      setDraftForm({
        title: '',
        content: '',
        category: 'GENERAL',
        is_important: false
      });
      loadAnnouncements();
    } catch (error) {
      console.error('Error creating announcement:', error);
    }
  };

  // Text formatting functions
  const insertTextAtCursor = (textToInsert: string) => {
    const textarea = document.getElementById('content') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const currentContent = draftForm.content;

    const newContent = currentContent.substring(0, start) + textToInsert + currentContent.substring(end);
    setDraftForm(prev => ({ ...prev, content: newContent }));

    // Set cursor position after inserted text
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + textToInsert.length, start + textToInsert.length);
    }, 0);
  };

  const handleFormatting = (type: string) => {
    const textarea = document.getElementById('content') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = draftForm.content.substring(start, end);

    let formattedText = '';
    switch (type) {
      case 'bold':
        formattedText = selectedText ? `**${selectedText}**` : '**bold text**';
        break;
      case 'italic':
        formattedText = selectedText ? `*${selectedText}*` : '*italic text*';
        break;
      case 'list':
        formattedText = selectedText ? `• ${selectedText}` : '• List item';
        break;
      case 'link':
        formattedText = selectedText ? `[${selectedText}](url)` : '[link text](url)';
        break;
      case 'image':
        formattedText = '![image description](image-url)';
        break;
      default:
        return;
    }

    const newContent = draftForm.content.substring(0, start) + formattedText + draftForm.content.substring(end);
    setDraftForm(prev => ({ ...prev, content: newContent }));

    // Set cursor position after formatted text
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + formattedText.length, start + formattedText.length);
    }, 0);
  };

  const filteredAnnouncements = selectedCategory === 'all'
    ? announcements
    : announcements.filter(ann => ann.category?.toLowerCase() === selectedCategory);

  // Separate latest (today) and past announcements
  const today = new Date().toDateString();
  const latestAnnouncements = filteredAnnouncements.filter(ann =>
    new Date(ann.created_at).toDateString() === today
  );
  const pastAnnouncements = filteredAnnouncements.filter(ann =>
    new Date(ann.created_at).toDateString() !== today
  );

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Announcements</h1>
          <p className="text-muted-foreground">Stay updated with team announcements and important news</p>
        </div>
        <Button
          className="bg-primary hover:bg-primary/90"
          onClick={() => setShowDraftModal(true)}
        >
          <Plus className="h-4 w-4 mr-2" />
          New Announcement
        </Button>
      </div>

      {/* Category Filter */}
      <Card>
        <CardContent className="p-4">
          <div className="flex space-x-2">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
                className="relative"
              >
                {category.label}
                <span className="ml-2 px-1.5 py-0.5 text-xs bg-muted rounded-full">
                  {category.count}
                </span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Loading State */}
      {loading && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground mt-2">Loading announcements...</p>
        </div>
      )}

      {/* Latest Announcements */}
      {!loading && latestAnnouncements.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <div className="h-px bg-border flex-1"></div>
            <span className="text-sm font-medium text-muted-foreground px-3">Latest</span>
            <div className="h-px bg-border flex-1"></div>
          </div>
          {latestAnnouncements.map((announcement) => (
            <AnnouncementCard key={announcement.id} announcement={announcement} />
          ))}
        </div>
      )}

      {/* Past Announcements */}
      {!loading && pastAnnouncements.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <div className="h-px bg-border flex-1"></div>
            <span className="text-sm font-medium text-muted-foreground px-3">Past Announcements</span>
            <div className="h-px bg-border flex-1"></div>
          </div>
          {pastAnnouncements.map((announcement) => (
            <AnnouncementCard key={announcement.id} announcement={announcement} />
          ))}
        </div>
      )}

      {/* Empty State */}
      {!loading && filteredAnnouncements.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-muted-foreground">
              <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No announcements found</h3>
              <p>No announcements in this category yet.</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Announcement Drafting Modal */}
      <Dialog open={showDraftModal} onOpenChange={setShowDraftModal}>
        <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Send className="w-5 h-5 text-primary" />
              <span>Create New Announcement</span>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6 mt-4">
            {/* Title */}
            <div className="space-y-2">
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                placeholder="Enter announcement title..."
                value={draftForm.title}
                onChange={(e) => setDraftForm(prev => ({ ...prev, title: e.target.value }))}
                className="text-lg font-medium"
              />
            </div>

            {/* Category and Importance */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select value={draftForm.category} onValueChange={(value: 'QA' | 'ENGINEERING' | 'MANAGEMENT' | 'GENERAL') => setDraftForm(prev => ({ ...prev, category: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GENERAL">General</SelectItem>
                    <SelectItem value="QA">QA</SelectItem>
                    <SelectItem value="ENGINEERING">Engineering</SelectItem>
                    <SelectItem value="MANAGEMENT">Management</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="is_important">Mark as Important</Label>
                <div className="flex items-center space-x-2 pt-2">
                  <input
                    type="checkbox"
                    id="is_important"
                    checked={draftForm.is_important}
                    onChange={(e) => setDraftForm(prev => ({ ...prev, is_important: e.target.checked }))}
                    className="w-4 h-4 text-primary bg-background border-border rounded focus:ring-primary focus:ring-2"
                  />
                  <span className="text-sm text-muted-foreground">This announcement is important</span>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="space-y-2">
              <Label htmlFor="content">Content *</Label>
              <div className="border rounded-lg">
                {/* Formatting Toolbar */}
                <div className="flex items-center space-x-1 p-2 border-b bg-muted/30">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 hover:bg-primary/10"
                    onClick={() => handleFormatting('bold')}
                    title="Bold"
                  >
                    <Bold className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 hover:bg-primary/10"
                    onClick={() => handleFormatting('italic')}
                    title="Italic"
                  >
                    <Italic className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 hover:bg-primary/10"
                    onClick={() => handleFormatting('list')}
                    title="Bullet List"
                  >
                    <List className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 hover:bg-primary/10"
                    onClick={() => handleFormatting('link')}
                    title="Insert Link"
                  >
                    <Link className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 hover:bg-primary/10"
                    onClick={() => handleFormatting('image')}
                    title="Insert Image"
                  >
                    <Image className="w-4 h-4" />
                  </Button>
                </div>
                <Textarea
                  id="content"
                  placeholder="Write your announcement content here..."
                  value={draftForm.content}
                  onChange={(e) => setDraftForm(prev => ({ ...prev, content: e.target.value }))}
                  className="min-h-[200px] border-0 resize-none focus:ring-0"
                />
              </div>
            </div>



            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button variant="outline" onClick={() => setShowDraftModal(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleCreateAnnouncement}
                disabled={!draftForm.title.trim() || !draftForm.content.trim()}
                className="bg-primary hover:bg-primary/90"
              >
                <Send className="w-4 h-4 mr-2" />
                Publish Announcement
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Announcement Card Component
const AnnouncementCard: React.FC<{ announcement: any }> = ({ announcement }) => {
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 48) return 'Yesterday';
    return date.toLocaleDateString();
  };

  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${
      announcement.priority === 'urgent' ? 'border-l-4 border-l-red-500' :
      announcement.priority === 'high' ? 'border-l-4 border-l-orange-500' : ''
    }`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarFallback className="bg-primary text-primary-foreground">
                {announcement.author_name?.split(' ').map((n: string) => n[0]).join('') || 'A'}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="flex items-center space-x-2">
                <h3 className="font-semibold text-foreground">{announcement.author_name || 'Anonymous'}</h3>
                {announcement.priority !== 'normal' && (
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    announcement.priority === 'urgent'
                      ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                      : 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400'
                  }`}>
                    {announcement.priority === 'urgent' ? 'Urgent' : 'Important'}
                  </span>
                )}
              </div>
              <p className="text-sm text-muted-foreground">
                {announcement.category} • {formatTime(announcement.created_at)}
              </p>
            </div>
          </div>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <CardTitle className="text-lg mb-3">{announcement.title}</CardTitle>
        <p className="text-muted-foreground mb-4 whitespace-pre-wrap">{announcement.content}</p>

        {/* Actions */}
        <div className="flex items-center space-x-4 pt-3 border-t border-border">
          <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
            <Heart className="h-4 w-4 mr-1" />
            0
          </Button>
          <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
            <MessageSquare className="h-4 w-4 mr-1" />
            0
          </Button>
          <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
            <Share2 className="h-4 w-4 mr-1" />
            Share
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default Announcements;
