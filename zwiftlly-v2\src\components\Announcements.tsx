import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from './ui/card';
import { Button } from './ui/button';
import { Avatar, AvatarFallback } from './ui/avatar';
import { Plus, MessageSquare, Heart, Share2, MoreHorizontal } from 'lucide-react';

const Announcements: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', label: 'All', count: 12 },
    { id: 'qa', label: 'QA', count: 4 },
    { id: 'engineering', label: 'Engineering', count: 5 },
    { id: 'management', label: 'Management', count: 3 }
  ];

  const announcements = [
    {
      id: 1,
      title: 'New Quality Assurance Guidelines',
      content: 'We are implementing new QA guidelines to improve our audit quality. Please review the updated documentation in the Knowledge Base.',
      author: '<PERSON>',
      role: 'QA Manager',
      category: 'qa',
      timestamp: '2 hours ago',
      likes: 8,
      comments: 3,
      isImportant: true
    },
    {
      id: 2,
      title: 'Team Performance Review Results',
      content: 'Great job everyone! Our team exceeded targets this quarter. Individual performance reviews will be scheduled next week.',
      author: '<PERSON>',
      role: 'Team Lead',
      category: 'management',
      timestamp: '4 hours ago',
      likes: 15,
      comments: 7,
      isImportant: false
    },
    {
      id: 3,
      title: 'System Maintenance Scheduled',
      content: 'Scheduled maintenance will occur this weekend. The system will be unavailable from 2 AM to 6 AM on Saturday.',
      author: '<PERSON>',
      role: 'DevOps Engineer',
      category: 'engineering',
      timestamp: '1 day ago',
      likes: 5,
      comments: 2,
      isImportant: true
    }
  ];

  const filteredAnnouncements = selectedCategory === 'all' 
    ? announcements 
    : announcements.filter(ann => ann.category === selectedCategory);

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Announcements</h1>
          <p className="text-muted-foreground">Stay updated with team announcements and important news</p>
        </div>
        <Button className="bg-primary hover:bg-primary/90">
          <Plus className="h-4 w-4 mr-2" />
          New Announcement
        </Button>
      </div>

      {/* Category Filter */}
      <Card>
        <CardContent className="p-4">
          <div className="flex space-x-2">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
                className="relative"
              >
                {category.label}
                <span className="ml-2 px-1.5 py-0.5 text-xs bg-muted rounded-full">
                  {category.count}
                </span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Announcements List */}
      <div className="space-y-4">
        {filteredAnnouncements.map((announcement) => (
          <Card key={announcement.id} className={`transition-all duration-200 hover:shadow-md ${
            announcement.isImportant ? 'border-l-4 border-l-orange-500' : ''
          }`}>
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback className="bg-primary text-primary-foreground">
                      {announcement.author.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-foreground">{announcement.author}</h3>
                      {announcement.isImportant && (
                        <span className="px-2 py-1 text-xs bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400 rounded-full">
                          Important
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">{announcement.role} • {announcement.timestamp}</p>
                  </div>
                </div>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <CardTitle className="text-lg mb-3">{announcement.title}</CardTitle>
              <p className="text-muted-foreground mb-4">{announcement.content}</p>
              
              {/* Actions */}
              <div className="flex items-center space-x-4 pt-3 border-t border-border">
                <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
                  <Heart className="h-4 w-4 mr-1" />
                  {announcement.likes}
                </Button>
                <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
                  <MessageSquare className="h-4 w-4 mr-1" />
                  {announcement.comments}
                </Button>
                <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
                  <Share2 className="h-4 w-4 mr-1" />
                  Share
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredAnnouncements.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-muted-foreground">
              <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No announcements found</h3>
              <p>No announcements in this category yet.</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Announcements;
