import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Bell, Check, CheckCheck, Archive, Trash2, User, FileEdit, AlertTriangle, Info } from 'lucide-react';

interface Notification {
  id: string;
  type: 'profile_change_request' | 'announcement' | 'task_assignment' | 'system' | 'info';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  isArchived: boolean;
  priority: 'low' | 'medium' | 'high';
  from?: string;
}

interface NotificationsDropdownProps {
  isOpen: boolean;
  onClose: () => void;
}

const NotificationsDropdown: React.FC<NotificationsDropdownProps> = ({ isOpen, onClose }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filter, setFilter] = useState<'all' | 'unread' | 'archived'>('all');

  // Mock notifications data
  useEffect(() => {
    const mockNotifications: Notification[] = [
      {
        id: '1',
        type: 'profile_change_request',
        title: 'Profile Change Request',
        message: 'John Doe is requesting to change their personal information',
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
        isRead: false,
        isArchived: false,
        priority: 'medium',
        from: 'John Doe'
      },
      {
        id: '2',
        type: 'announcement',
        title: 'New Company Policy',
        message: 'Updated remote work guidelines have been published',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
        isRead: false,
        isArchived: false,
        priority: 'high',
        from: 'HR Department'
      },
      {
        id: '3',
        type: 'task_assignment',
        title: 'New Task Assigned',
        message: 'You have been assigned to "Q4 Performance Review"',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(), // 4 hours ago
        isRead: true,
        isArchived: false,
        priority: 'medium',
        from: 'Project Manager'
      },
      {
        id: '4',
        type: 'system',
        title: 'System Maintenance',
        message: 'Scheduled maintenance will occur tonight at 11 PM',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
        isRead: true,
        isArchived: true,
        priority: 'low',
        from: 'System Admin'
      }
    ];
    setNotifications(mockNotifications);
  }, []);

  const getIcon = (type: Notification['type']) => {
    switch (type) {
      case 'profile_change_request':
        return <FileEdit className="w-4 h-4 text-orange-600" />;
      case 'announcement':
        return <Info className="w-4 h-4 text-blue-600" />;
      case 'task_assignment':
        return <User className="w-4 h-4 text-green-600" />;
      case 'system':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      default:
        return <Bell className="w-4 h-4 text-gray-600" />;
    }
  };

  const getPriorityColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 dark:bg-red-900/20 border-red-200 dark:border-red-800';
      case 'medium':
        return 'bg-yellow-100 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800';
      case 'low':
        return 'bg-gray-100 dark:bg-gray-900/20 border-gray-200 dark:border-gray-800';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d ago`;
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'unread') return !notification.isRead && !notification.isArchived;
    if (filter === 'archived') return notification.isArchived;
    return !notification.isArchived; // 'all' shows non-archived
  });

  const unreadCount = notifications.filter(n => !n.isRead && !n.isArchived).length;

  const markAsRead = (id: string) => {
    setNotifications(prev => prev.map(n => 
      n.id === id ? { ...n, isRead: true } : n
    ));
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
  };

  const archiveNotification = (id: string) => {
    setNotifications(prev => prev.map(n => 
      n.id === id ? { ...n, isArchived: true } : n
    ));
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const deleteAll = () => {
    setNotifications(prev => prev.filter(n => n.isArchived));
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop for click-outside-to-close */}
      <div
        className="fixed inset-0 z-40"
        onClick={onClose}
      />

      <div className="absolute right-0 mt-2 w-[420px] bg-card border border-border/50 rounded-2xl shadow-2xl z-50 animate-scale-in backdrop-blur-sm overflow-hidden">
      {/* Header */}
      <CardHeader className="p-4 border-b border-border/50 bg-gradient-to-r from-background to-muted/20">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center space-x-3">
            <div className="p-2 bg-primary/10 rounded-xl">
              <Bell className="w-5 h-5 text-primary" />
            </div>
            {unreadCount > 0 && (
              <Badge variant="destructive" className="text-xs px-2 py-0.5">
                {unreadCount} new
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center space-x-1">
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={markAllAsRead}
                className="text-xs hover:bg-primary/10 px-3 py-1.5 h-auto"
                title="Mark all as read"
              >
                <CheckCheck className="w-3 h-3 mr-1" />
                Mark All Read
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={deleteAll}
              className="text-xs text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 px-3 py-1.5 h-auto"
              title="Delete all"
            >
              <Trash2 className="w-3 h-3 mr-1" />
              Clear All
            </Button>
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="flex space-x-1 mt-3 bg-muted/30 p-1 rounded-xl">
          {(['all', 'unread', 'archived'] as const).map((filterType) => (
            <Button
              key={filterType}
              variant="ghost"
              size="sm"
              onClick={() => setFilter(filterType)}
              className={`text-sm capitalize px-4 py-2 rounded-lg transition-all duration-200 flex-1 ${
                filter === filterType
                  ? 'bg-background shadow-sm border border-border/50 text-foreground font-medium'
                  : 'hover:bg-background/50 text-muted-foreground'
              }`}
            >
              {filterType}
              {filterType === 'unread' && unreadCount > 0 && (
                <Badge variant="secondary" className="ml-2 text-xs px-1.5 py-0.5 bg-primary/20 text-primary border-0">
                  {unreadCount}
                </Badge>
              )}
            </Button>
          ))}
        </div>
      </CardHeader>

      {/* Content */}
      <CardContent className="p-0 max-h-[400px] overflow-y-auto">
        {filteredNotifications.length === 0 ? (
          <div className="text-center py-12 text-muted-foreground">
            <div className="p-4 bg-muted/20 rounded-full w-fit mx-auto mb-4">
              <Bell className="w-12 h-12 opacity-50" />
            </div>
            <p className="text-lg font-medium">No notifications</p>
            <p className="text-sm mt-2">
              {filter === 'unread' && 'All caught up! 🎉'}
              {filter === 'archived' && 'No archived notifications'}
              {filter === 'all' && 'You\'re all set ✨'}
            </p>
          </div>
        ) : (
          <div className="space-y-0">
            {filteredNotifications.map((notification, index) => (
              <div
                key={notification.id}
                className={`p-4 border-b border-border/30 hover:bg-muted/30 transition-all duration-200 ${
                  !notification.isRead ? 'bg-primary/5 border-l-4 border-l-primary' : 'bg-background'
                } ${index === filteredNotifications.length - 1 ? 'border-b-0' : ''}`}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-0.5 p-2 bg-muted/30 rounded-xl">
                    {getIcon(notification.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 pr-2">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="text-sm font-semibold text-foreground">
                            {notification.title}
                          </h4>
                          {!notification.isRead && (
                            <div className="w-2 h-2 bg-primary rounded-full"></div>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground leading-relaxed mb-2">
                          {notification.message}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-muted-foreground">
                            {notification.from && `From: ${notification.from}`}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {formatTimestamp(notification.timestamp)}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1">
                        {!notification.isRead && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => markAsRead(notification.id)}
                            className="w-7 h-7 p-0 hover:bg-green-100 dark:hover:bg-green-900/20 rounded-lg"
                            title="Mark as read"
                          >
                            <Check className="w-3.5 h-3.5 text-green-600" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => archiveNotification(notification.id)}
                          className="w-7 h-7 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-lg"
                          title="Archive"
                        >
                          <Archive className="w-3.5 h-3.5 text-blue-600" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteNotification(notification.id)}
                          className="w-7 h-7 p-0 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg"
                          title="Delete"
                        >
                          <Trash2 className="w-3.5 h-3.5 text-red-600" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      </div>
    </>
  );
};

export default NotificationsDropdown;
