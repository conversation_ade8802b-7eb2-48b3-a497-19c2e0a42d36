import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Clock,
  Calendar,
  Users,
  Timer,
  CheckCircle,
  XCircle,
  Coffee,
  Play,
  Pause,
  RotateCcw,
  TrendingUp,
  ClockIcon,
  UserCheck,
  AlertTriangle,
  DollarSign,
  Calculator,
  FileText,
  Download,
  Filter,
  Search,
  Edit,
  Eye,
  MoreHorizontal,
  PlusCircle,
  MinusCircle,
  Banknote,
  CreditCard,
  Wallet,
  Receipt,
  LogIn,
  LogOut,
  RefreshCw
} from 'lucide-react';
import { useAuth } from '@/contexts/SupabaseAuthContext';
import { useData } from '@/contexts/DataContext';

import TimeTrackingOverview from './adherence/TimeTrackingOverview';
import LoadingSpinner from './ui/LoadingSpinner';
import ErrorAlert from './ui/ErrorAlert';

interface AttendanceRecord {
  id: string;
  employeeName: string;
  employeeId: string;
  date: string;
  clockIn: string;
  clockOut?: string;
  breakStart?: string;
  breakEnd?: string;
  totalHours: number;
  regularHours: number;
  overtime: number;
  doubleTime: number;
  status: 'present' | 'late' | 'absent' | 'on-break';
  hourlyRate: number;
  regularPay: number;
  overtimePay: number;
  doubleTimePay: number;
  totalPay: number;
  deductions: number;
  netPay: number;
}

interface ClockAction {
  id: string;
  employeeName: string;
  employeeId: string;
  action: 'clock-in' | 'clock-out' | 'break-start' | 'break-end';
  timestamp: string;
  location?: string;
}

interface PayrollSummary {
  totalEmployees: number;
  totalRegularHours: number;
  totalOvertimeHours: number;
  totalDoubleTimeHours: number;
  totalGrossPay: number;
  totalDeductions: number;
  totalNetPay: number;
  averageHourlyRate: number;
}

const AttendanceManagement: React.FC = () => {
  const { user } = useAuth();
  const {
    attendance,
    loading,
    errors,
    refreshAttendance,
    clockAction,
    clearErrors
  } = useData();


  const [currentTime, setCurrentTime] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedPeriod, setSelectedPeriod] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  const [selectedView, setSelectedView] = useState<'attendance' | 'payroll' | 'overtime'>('attendance');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'present' | 'late' | 'absent' | 'on-break'>('all');
  const [isClockingAction, setIsClockingAction] = useState(false);
  const [recentActions, setRecentActions] = useState<any[]>([]);

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Initialize recent actions with mock data
  useEffect(() => {
    const mockActions = [
      {
        id: '1',
        employeeName: 'John Doe',
        employeeId: 'EMP001',
        action: 'clock-in',
        timestamp: '09:00 AM',
        location: 'Office Building A'
      },
      {
        id: '2',
        employeeName: 'Jane Smith',
        employeeId: 'EMP002',
        action: 'break-start',
        timestamp: '10:30 AM',
        location: 'Office Building B'
      },
      {
        id: '3',
        employeeName: 'Mike Johnson',
        employeeId: 'EMP003',
        action: 'clock-out',
        timestamp: '05:00 PM',
        location: 'Remote'
      }
    ];
    setRecentActions(mockActions);
  }, []);

  // Handle clock actions
  const handleClockAction = async (action: string) => {
    if (!user) return;

    setIsClockingAction(true);
    try {
      const result = await clockAction({
        action,
        location: 'Office', // Could be dynamic based on user location
        notes: `${action.replace('_', ' ')} via web app`
      });

      // Refresh attendance data to show updated state
      await refreshAttendance();

      console.log('Clock action successful:', result);
    } catch (error) {
      console.error('Clock action failed:', error);
    } finally {
      setIsClockingAction(false);
    }
  };

  // Calculate payroll summary from real data
  const calculatePayrollSummary = (): PayrollSummary => {
    const records = attendance.records || [];

    return {
      totalEmployees: records.length,
      totalRegularHours: records.reduce((sum, record) => sum + (record.regularHours || 0), 0),
      totalOvertimeHours: records.reduce((sum, record) => sum + (record.overtime || 0), 0),
      totalDoubleTimeHours: records.reduce((sum, record) => sum + (record.doubleTime || 0), 0),
      totalGrossPay: records.reduce((sum, record) => sum + (record.totalPay || 0), 0),
      totalDeductions: records.reduce((sum, record) => sum + (record.deductions || 0), 0),
      totalNetPay: records.reduce((sum, record) => sum + (record.netPay || 0), 0),
      averageHourlyRate: records.length > 0
        ? records.reduce((sum, record) => sum + (record.hourlyRate || 0), 0) / records.length
        : 0
    };
  };

  // Filter records based on search and status
  const filteredRecords = (attendance.records || []).filter(record => {
    const matchesSearch = searchQuery === '' ||
      record.user?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      record.user?.email?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = filterStatus === 'all' || record.status?.toLowerCase() === filterStatus;

    return matchesSearch && matchesStatus;
  });

  // Refresh data when component mounts or date changes
  useEffect(() => {
    if (user) {
      refreshAttendance();
    }
  }, [user, selectedDate, refreshAttendance]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'late': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'absent': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      case 'on-break': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'clock-in': return <Play className="w-4 h-4 text-green-600" />;
      case 'clock-out': return <Pause className="w-4 h-4 text-red-600" />;
      case 'break-start': return <Coffee className="w-4 h-4 text-blue-600" />;
      case 'break-end': return <RotateCcw className="w-4 h-4 text-purple-600" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const formatTime = (time: Date) => {
    return time.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Get calculated payroll summary
  const payrollSummary = calculatePayrollSummary();

  // Show loading state
  if (loading.attendance) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <LoadingSpinner size="lg" text="Loading attendance data..." fullScreen />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Error Alert */}
        {errors.attendance && (
          <div className="mb-6">
            <ErrorAlert
              title="Failed to load attendance data"
              message={errors.attendance}
              onRetry={refreshAttendance}
              onDismiss={clearErrors}
            />
          </div>
        )}
        {/* Header */}
        <div className="mb-8 animate-fade-in-up animate-delay-0">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground mb-2">
                Attendance & Payroll Management
              </h1>
              <p className="text-muted-foreground">
                Comprehensive employee attendance, payroll, and overtime management system
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="text-2xl font-bold text-foreground">
                  {formatTime(currentTime)}
                </div>
                <div className="text-sm text-muted-foreground">
                  {formatDate(currentTime)}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant={selectedView === 'attendance' ? 'default' : 'outline'}
                  onClick={() => setSelectedView('attendance')}
                  size="sm"
                >
                  <UserCheck className="w-4 h-4 mr-2" />
                  Attendance
                </Button>
                <Button
                  variant={selectedView === 'payroll' ? 'default' : 'outline'}
                  onClick={() => setSelectedView('payroll')}
                  size="sm"
                >
                  <DollarSign className="w-4 h-4 mr-2" />
                  Payroll
                </Button>
                <Button
                  variant={selectedView === 'overtime' ? 'default' : 'outline'}
                  onClick={() => setSelectedView('overtime')}
                  size="sm"
                >
                  <Clock className="w-4 h-4 mr-2" />
                  Overtime
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-6 mb-8 animate-fade-in-up animate-delay-150">
          <Card className="gradient-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
                  <UserCheck className="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Present Today</p>
                  <p className="text-2xl font-bold text-foreground">{filteredRecords.filter(r => r.status === 'PRESENT').length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="gradient-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-xl">
                  <AlertTriangle className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Late Arrivals</p>
                  <p className="text-2xl font-bold text-foreground">{filteredRecords.filter(r => r.status === 'LATE').length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="gradient-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
                  <Coffee className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">On Break</p>
                  <p className="text-2xl font-bold text-foreground">{filteredRecords.filter(r => r.status === 'ON_BREAK').length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="gradient-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-xl">
                  <Clock className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Overtime Hours</p>
                  <p className="text-2xl font-bold text-foreground">{payrollSummary?.totalOvertimeHours.toFixed(1) || '0'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="gradient-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-emerald-100 dark:bg-emerald-900/30 rounded-xl">
                  <DollarSign className="w-6 h-6 text-emerald-600 dark:text-emerald-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Payroll</p>
                  <p className="text-2xl font-bold text-foreground">${payrollSummary?.totalNetPay.toFixed(0) || '0'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="gradient-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-xl">
                  <Calculator className="w-6 h-6 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Avg Rate</p>
                  <p className="text-2xl font-bold text-foreground">${payrollSummary?.averageHourlyRate.toFixed(0) || '0'}/hr</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter Controls */}
        <div className="flex items-center justify-between mb-6 animate-fade-in-up animate-delay-300">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search employees..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
            <Select value={filterStatus} onValueChange={(value: any) => setFilterStatus(value)}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="present">Present</SelectItem>
                <SelectItem value="late">Late</SelectItem>
                <SelectItem value="absent">Absent</SelectItem>
                <SelectItem value="on-break">On Break</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedPeriod} onValueChange={(value: any) => setSelectedPeriod(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <FileText className="w-4 h-4 mr-2" />
              Reports
            </Button>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Employee Records */}
          <div className="lg:col-span-3 animate-fade-in-up animate-delay-450">
            <Card className="gradient-border">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-primary/10 rounded-xl">
                      {selectedView === 'attendance' && <Calendar className="w-5 h-5 text-primary" />}
                      {selectedView === 'payroll' && <DollarSign className="w-5 h-5 text-primary" />}
                      {selectedView === 'overtime' && <Clock className="w-5 h-5 text-primary" />}
                    </div>
                    <span>
                      {selectedView === 'attendance' && "Today's Attendance"}
                      {selectedView === 'payroll' && "Payroll Overview"}
                      {selectedView === 'overtime' && "Overtime Management"}
                    </span>
                  </div>
                  <Badge variant="secondary">{filteredRecords.length} employees</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredRecords.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">No attendance records found</p>
                      <Button
                        variant="outline"
                        onClick={refreshAttendance}
                        className="mt-4"
                        disabled={loading.attendance}
                      >
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Refresh
                      </Button>
                    </div>
                  ) : (
                    filteredRecords.map((record) => (
                    <div key={record.id} className="p-4 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors border border-border/50">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                            {record.user?.name?.split(' ').map(n => n[0]).join('') || 'U'}
                          </div>
                          <div>
                            <div className="flex items-center space-x-2">
                              <p className="font-medium text-foreground">{record.user?.name || 'Unknown User'}</p>
                              <Badge variant="outline" className="text-xs">{record.user?.email}</Badge>
                            </div>

                            {selectedView === 'attendance' && (
                              <div className="flex items-center space-x-2 text-sm text-muted-foreground mt-1">
                                <span>In: {record.clockIn}</span>
                                {record.clockOut && <span>• Out: {record.clockOut}</span>}
                                <span>• {record.totalHours}h total</span>
                                {record.overtime > 0 && <span className="text-orange-600">• +{record.overtime}h OT</span>}
                              </div>
                            )}

                            {selectedView === 'payroll' && (
                              <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                                <span>Rate: ${record.hourlyRate}/hr</span>
                                <span>• Regular: ${record.regularPay}</span>
                                {record.overtimePay > 0 && <span>• OT: ${record.overtimePay}</span>}
                                <span className="text-green-600">• Net: ${record.netPay}</span>
                              </div>
                            )}

                            {selectedView === 'overtime' && (
                              <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                                <span>Regular: {record.regularHours}h</span>
                                {record.overtime > 0 && <span className="text-orange-600">• Overtime: {record.overtime}h</span>}
                                {record.doubleTime > 0 && <span className="text-red-600">• Double: {record.doubleTime}h</span>}
                                <span>• Total Pay: ${record.totalPay}</span>
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center space-x-3">
                          <Badge className={getStatusColor(record.status)}>
                            {record.status.replace('-', ' ')}
                          </Badge>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6 animate-fade-in-up animate-delay-600">
            {/* Payroll Summary */}
            {payrollSummary && (
              <Card className="gradient-border">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-3">
                    <div className="p-2 bg-primary/10 rounded-xl">
                      <Wallet className="w-5 h-5 text-primary" />
                    </div>
                    <span>Payroll Summary</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Gross Pay</span>
                      <span className="font-bold text-foreground">${payrollSummary.totalGrossPay.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Deductions</span>
                      <span className="font-medium text-red-600">-${payrollSummary.totalDeductions.toFixed(2)}</span>
                    </div>
                    <div className="border-t border-border/50 pt-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-foreground">Net Pay</span>
                        <span className="font-bold text-green-600 text-lg">${payrollSummary.totalNetPay.toFixed(2)}</span>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4 mt-4 pt-4 border-t border-border/50">
                      <div className="text-center">
                        <p className="text-xs text-muted-foreground">Regular Hours</p>
                        <p className="font-bold text-foreground">{payrollSummary.totalRegularHours}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-xs text-muted-foreground">OT Hours</p>
                        <p className="font-bold text-orange-600">{payrollSummary.totalOvertimeHours}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Recent Clock Actions */}
            <Card className="gradient-border">
              <CardHeader>
                <CardTitle className="flex items-center space-x-3">
                  <div className="p-2 bg-primary/10 rounded-xl">
                    <ClockIcon className="w-5 h-5 text-primary" />
                  </div>
                  <span>Recent Actions</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentActions.map((action) => (
                    <div key={action.id} className="flex items-start space-x-3 p-3 bg-muted/20 rounded-lg">
                      <div className="p-2 bg-background rounded-lg shadow-sm">
                        {getActionIcon(action.action)}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-foreground text-sm">{action.employeeName}</p>
                        <p className="text-xs text-muted-foreground">
                          <Badge variant="outline" className="text-xs mr-1">{action.employeeId}</Badge>
                          {action.action.replace('-', ' ')} at {action.timestamp}
                        </p>
                        {action.location && (
                          <p className="text-xs text-muted-foreground">📍 {action.location}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="gradient-border">
              <CardHeader>
                <CardTitle className="flex items-center space-x-3">
                  <div className="p-2 bg-primary/10 rounded-xl">
                    <Receipt className="w-5 h-5 text-primary" />
                  </div>
                  <span>Quick Actions</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => handleClockAction('CLOCK_IN')}
                    disabled={isClockingAction}
                  >
                    <LogIn className="w-4 h-4 mr-2" />
                    Clock In
                  </Button>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => handleClockAction('CLOCK_OUT')}
                    disabled={isClockingAction}
                  >
                    <LogOut className="w-4 h-4 mr-2" />
                    Clock Out
                  </Button>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => handleClockAction('BREAK_START')}
                    disabled={isClockingAction}
                  >
                    <Coffee className="w-4 h-4 mr-2" />
                    Start Break
                  </Button>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => handleClockAction('BREAK_END')}
                    disabled={isClockingAction}
                  >
                    <Coffee className="w-4 h-4 mr-2" />
                    End Break
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Time Tracking Overview */}
        <div className="mt-8 animate-fade-in-up animate-delay-600">
          <TimeTrackingOverview />
        </div>
      </div>
    </div>
  );
};

export default AttendanceManagement;
