import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface WeatherData {
  city: string;
  country: string;
  temperature: number;
  feelsLike: number;
  condition: string;
  description: string;
  humidity: number;
  pressure: number;
  windSpeed: number;
  windDirection: number;
  visibility: number;
  cloudiness: number;
  icon: string;
  emoji: string;
  sunrise: number;
  sunset: number;
  timezone: number;
  lastUpdated: number;
}

interface TimeZone {
  name: string;
  timezone: string;
  abbreviation: string;
}

const WeatherTimeDisplay: React.FC = () => {
  const [weatherData, setWeatherData] = useState<WeatherData[]>([]);
  const [times, setTimes] = useState<{ [key: string]: string }>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [useCelsius, setUseCelsius] = useState(false);
  const [format24Hour, setFormat24Hour] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const timeZones: TimeZone[] = [
    { name: 'Eastern', timezone: 'America/New_York', abbreviation: 'EST/EDT' },
    { name: 'Central', timezone: 'America/Chicago', abbreviation: 'CST/CDT' },
    { name: 'Mountain', timezone: 'America/Denver', abbreviation: 'MST/MDT' },
    { name: 'Pacific', timezone: 'America/Los_Angeles', abbreviation: 'PST/PDT' },
    { name: 'Philippines', timezone: 'Asia/Manila', abbreviation: 'PHT' },
  ];

  // Open-Meteo API configuration - FREE, no API key required!
  const cities = [
    { name: 'New York', lat: 40.7128, lon: -74.0060 },
    { name: 'Chicago', lat: 41.8781, lon: -87.6298 },
    { name: 'Denver', lat: 39.7392, lon: -104.9903 },
    { name: 'San Francisco', lat: 37.7749, lon: -122.4194 },
    { name: 'Manila', lat: 14.5995, lon: 120.9842 }
  ];

  // Open-Meteo weather code mappings
  const getWeatherCondition = (code: number): string => {
    if (code === 0) return 'Clear';
    if (code <= 3) return 'Partly Cloudy';
    if (code <= 48) return 'Foggy';
    if (code <= 67) return 'Rainy';
    if (code <= 77) return 'Snowy';
    if (code <= 82) return 'Showers';
    if (code <= 99) return 'Thunderstorm';
    return 'Unknown';
  };

  const getWeatherDescription = (code: number): string => {
    const descriptions: { [key: number]: string } = {
      0: 'clear sky', 1: 'mainly clear', 2: 'partly cloudy', 3: 'overcast',
      45: 'fog', 48: 'depositing rime fog', 51: 'light drizzle', 53: 'moderate drizzle',
      55: 'dense drizzle', 56: 'light freezing drizzle', 57: 'dense freezing drizzle',
      61: 'slight rain', 63: 'moderate rain', 65: 'heavy rain', 66: 'light freezing rain',
      67: 'heavy freezing rain', 71: 'slight snow', 73: 'moderate snow', 75: 'heavy snow',
      77: 'snow grains', 80: 'slight rain showers', 81: 'moderate rain showers',
      82: 'violent rain showers', 85: 'slight snow showers', 86: 'heavy snow showers',
      95: 'thunderstorm', 96: 'thunderstorm with slight hail', 99: 'thunderstorm with heavy hail'
    };
    return descriptions[code] || 'unknown';
  };

  const getWeatherIcon = (code: number): string => {
    if (code === 0) return '01d';
    if (code <= 3) return '02d';
    if (code <= 48) return '50d';
    if (code <= 67) return '10d';
    if (code <= 77) return '13d';
    if (code <= 82) return '09d';
    if (code <= 99) return '11d';
    return '01d';
  };

  const getWeatherEmojiFromCode = (code: number): string => {
    if (code === 0) return '☀️';
    if (code <= 3) return '⛅';
    if (code <= 48) return '🌫️';
    if (code <= 67) return '🌧️';
    if (code <= 77) return '❄️';
    if (code <= 82) return '🌦️';
    if (code <= 99) return '⛈️';
    return '🌤️';
  };

  const fetchCityWeather = async (cityInfo: { name: string; lat: number; lon: number }): Promise<WeatherData | null> => {
    try {
      // Make API call to Open-Meteo (FREE, no API key required!)
      const response = await fetch(
        `https://api.open-meteo.com/v1/forecast?latitude=${cityInfo.lat}&longitude=${cityInfo.lon}&current=temperature_2m,relative_humidity_2m,apparent_temperature,precipitation,weather_code,cloud_cover,pressure_msl,surface_pressure,wind_speed_10m,wind_direction_10m&temperature_unit=fahrenheit&wind_speed_unit=mph&precipitation_unit=inch&timezone=auto`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const current = data.current;

      // Transform Open-Meteo response to our WeatherData format
      return {
        city: cityInfo.name,
        country: cityInfo.name === 'Manila' ? 'PH' : 'US',
        temperature: Math.round(current.temperature_2m),
        feelsLike: Math.round(current.apparent_temperature),
        condition: getWeatherCondition(current.weather_code),
        description: getWeatherDescription(current.weather_code),
        humidity: current.relative_humidity_2m,
        pressure: Math.round(current.pressure_msl || current.surface_pressure),
        windSpeed: Math.round(current.wind_speed_10m),
        windDirection: current.wind_direction_10m,
        visibility: 10000, // Open-Meteo doesn't provide visibility
        cloudiness: current.cloud_cover,
        icon: getWeatherIcon(current.weather_code),
        emoji: getWeatherEmojiFromCode(current.weather_code),
        sunrise: 0, // Would need separate API call for sunrise/sunset
        sunset: 0,
        timezone: 0,
        lastUpdated: Date.now()
      };

    } catch (error) {
      console.error(`Error fetching weather for ${cityInfo.name}:`, error);
      // Return fallback data if API call fails
      return getFallbackWeatherData(cityInfo.name);
    }
  };

  const getFallbackWeatherData = (city: string): WeatherData => {
    const fallbackData: { [key: string]: Partial<WeatherData> } = {
      'New York': { temperature: 72, condition: 'Partly Cloudy', humidity: 65, emoji: '⛅' },
      'Chicago': { temperature: 68, condition: 'Sunny', humidity: 58, emoji: '☀️' },
      'Denver': { temperature: 75, condition: 'Clear', humidity: 45, emoji: '☀️' },
      'San Francisco': { temperature: 78, condition: 'Sunny', humidity: 70, emoji: '☀️' },
      'Manila': { temperature: 84, condition: 'Partly Cloudy', humidity: 78, emoji: '⛅' }
    };

    const data = fallbackData[city] || { temperature: 75, condition: 'Unknown', humidity: 60, emoji: '🌤️' };

    return {
      city,
      country: city === 'Manila' ? 'PH' : 'US',
      temperature: data.temperature || 75,
      feelsLike: (data.temperature || 75) + 2,
      condition: data.condition || 'Unknown',
      description: data.condition?.toLowerCase() || 'unknown',
      humidity: data.humidity || 60,
      pressure: 1013,
      windSpeed: 5,
      windDirection: 180,
      visibility: 10000,
      cloudiness: 20,
      icon: '01d',
      emoji: data.emoji || '🌤️',
      sunrise: Date.now() / 1000 - 3600,
      sunset: Date.now() / 1000 + 3600,
      timezone: 0,
      lastUpdated: Date.now()
    };
  };

  useEffect(() => {
    const fetchWeatherData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch weather for all cities using Open-Meteo API
        const weatherPromises = cities.map(cityInfo => fetchCityWeather(cityInfo));
        const results = await Promise.allSettled(weatherPromises);

        const weatherData = results
          .map((result, index) => {
            if (result.status === 'fulfilled' && result.value) {
              return result.value;
            }
            return null;
          })
          .filter((data): data is WeatherData => data !== null);

        if (weatherData.length > 0) {
          setWeatherData(weatherData);
          setLastUpdated(new Date());

          // Using Open-Meteo API - completely free!
          setError(null); // Clear any previous errors
        } else {
          throw new Error('No weather data available');
        }

      } catch (err) {
        console.error('Weather fetch error:', err);
        setError('API temporarily unavailable - using demo data');

        // Enhanced fallback data
        const fallbackData: WeatherData[] = [
          {
            city: 'New York', country: 'US', temperature: 72, feelsLike: 74, condition: 'Partly Cloudy',
            description: 'partly cloudy', humidity: 65, pressure: 1013, windSpeed: 5, windDirection: 180,
            visibility: 10000, cloudiness: 20, icon: '02d', emoji: '⛅', sunrise: 0, sunset: 0, timezone: 0, lastUpdated: Date.now()
          },
          {
            city: 'Chicago', country: 'US', temperature: 68, feelsLike: 70, condition: 'Sunny',
            description: 'clear sky', humidity: 58, pressure: 1015, windSpeed: 3, windDirection: 90,
            visibility: 10000, cloudiness: 0, icon: '01d', emoji: '☀️', sunrise: 0, sunset: 0, timezone: 0, lastUpdated: Date.now()
          },
          {
            city: 'Denver', country: 'US', temperature: 75, feelsLike: 77, condition: 'Clear',
            description: 'clear sky', humidity: 45, pressure: 1020, windSpeed: 7, windDirection: 270,
            visibility: 10000, cloudiness: 0, icon: '01d', emoji: '☀️', sunrise: 0, sunset: 0, timezone: 0, lastUpdated: Date.now()
          },
          {
            city: 'San Francisco', country: 'US', temperature: 78, feelsLike: 80, condition: 'Sunny',
            description: 'clear sky', humidity: 70, pressure: 1012, windSpeed: 8, windDirection: 315,
            visibility: 10000, cloudiness: 5, icon: '01d', emoji: '☀️', sunrise: 0, sunset: 0, timezone: 0, lastUpdated: Date.now()
          },
          {
            city: 'Manila', country: 'PH', temperature: 84, feelsLike: 88, condition: 'Partly Cloudy',
            description: 'few clouds', humidity: 78, pressure: 1008, windSpeed: 4, windDirection: 45,
            visibility: 8000, cloudiness: 25, icon: '02d', emoji: '⛅', sunrise: 0, sunset: 0, timezone: 0, lastUpdated: Date.now()
          }
        ];
        setWeatherData(fallbackData);
      } finally {
        setLoading(false);
      }
    };

    fetchWeatherData();

    // Refresh weather data every 15 minutes
    const interval = setInterval(fetchWeatherData, 15 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const updateTimes = () => {
      const newTimes: { [key: string]: string } = {};
      
      timeZones.forEach(tz => {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
          timeZone: tz.timezone,
          hour12: !format24Hour,
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
        newTimes[tz.name] = timeString;
      });
      
      setTimes(newTimes);
    };

    updateTimes();
    const interval = setInterval(updateTimes, 1000);
    return () => clearInterval(interval);
  }, [format24Hour]);

  const convertTemperature = (fahrenheit: number) => {
    return useCelsius ? Math.round((fahrenheit - 32) * 5/9) : fahrenheit;
  };

  const getTemperatureColor = (temp: number) => {
    const tempInF = useCelsius ? (temp * 9/5) + 32 : temp;
    if (tempInF >= 80) return 'text-red-600 dark:text-red-400';
    if (tempInF >= 70) return 'text-orange-600 dark:text-orange-400';
    if (tempInF >= 60) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-blue-600 dark:text-blue-400';
  };

  const getTimeZoneDate = (timezone: string) => {
    return new Date().toLocaleDateString('en-US', {
      timeZone: timezone,
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm sm:text-base">Weather & Time Zones</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3">
            {/* Weather loading grid */}
            <div className="grid grid-cols-5 gap-2 sm:gap-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse bg-muted rounded-lg p-2">
                  <div className="w-6 h-6 bg-muted-foreground/20 rounded mb-1 mx-auto"></div>
                  <div className="w-12 h-3 bg-muted-foreground/20 rounded mb-1 mx-auto"></div>
                  <div className="w-8 h-2 bg-muted-foreground/20 rounded mb-1 mx-auto"></div>
                  <div className="w-10 h-3 bg-muted-foreground/20 rounded mx-auto"></div>
                </div>
              ))}
            </div>
            {/* Time zones loading grid */}
            <div className="grid grid-cols-5 gap-2 sm:gap-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse bg-muted rounded-lg p-2">
                  <div className="w-12 h-3 bg-muted-foreground/20 rounded mb-1 mx-auto"></div>
                  <div className="w-8 h-2 bg-muted-foreground/20 rounded mb-1 mx-auto"></div>
                  <div className="w-16 h-3 bg-muted-foreground/20 rounded mx-auto"></div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm sm:text-base">Weather & Time Zones</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-center py-6">
            <div className="text-red-500 mb-2">⚠️</div>
            <p className="text-muted-foreground text-sm">{error}</p>
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
              className="mt-2 h-6 px-3 text-xs"
            >
              Try again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle className="text-sm sm:text-base">Weather & Time Zones</CardTitle>
            <div className="flex items-center space-x-1">
              {error && (
                <span className="text-xs text-orange-500" title={`Using fallback data: ${error}`}>⚠️</span>
              )}
              {!error && lastUpdated && (
                <span className="text-xs text-green-500" title={`Live data from Open-Meteo - Last updated: ${lastUpdated.toLocaleTimeString()}`}>🌐</span>
              )}
              <span className="text-xs text-blue-500" title="Powered by Open-Meteo API (Free & Open Source)">🆓</span>
            </div>
          </div>

          {/* Temperature and Time Format Toggles */}
          <div className="flex items-center space-x-4">
            {/* Temperature Toggle */}
            <div className="flex items-center space-x-2">
              <span className={`text-xs font-medium transition-colors ${!useCelsius ? 'text-foreground' : 'text-muted-foreground'}`}>°F</span>
              <button
                onClick={() => setUseCelsius(!useCelsius)}
                className={`relative inline-flex h-5 w-9 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 shadow-sm ${
                  useCelsius ? 'bg-primary' : 'bg-gray-300 dark:bg-gray-600'
                }`}
                title={`Switch to ${useCelsius ? 'Fahrenheit' : 'Celsius'}`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white shadow-sm transition-transform duration-200 ${
                    useCelsius ? 'translate-x-4' : 'translate-x-0.5'
                  }`}
                />
              </button>
              <span className={`text-xs font-medium transition-colors ${useCelsius ? 'text-foreground' : 'text-muted-foreground'}`}>°C</span>
            </div>

            {/* Time Format Toggle */}
            <div className="flex items-center space-x-2">
              <span className={`text-xs font-medium transition-colors ${!format24Hour ? 'text-foreground' : 'text-muted-foreground'}`}>12H</span>
              <button
                onClick={() => setFormat24Hour(!format24Hour)}
                className={`relative inline-flex h-5 w-9 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 shadow-sm ${
                  format24Hour ? 'bg-primary' : 'bg-gray-300 dark:bg-gray-600'
                }`}
                title={`Switch to ${format24Hour ? '12-hour' : '24-hour'} format`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white shadow-sm transition-transform duration-200 ${
                    format24Hour ? 'translate-x-4' : 'translate-x-0.5'
                  }`}
                />
              </button>
              <span className={`text-xs font-medium transition-colors ${format24Hour ? 'text-foreground' : 'text-muted-foreground'}`}>24H</span>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          {/* Combined Weather and Time Zones in a grid */}
          <div className="grid grid-cols-5 gap-1 sm:gap-2">
            {/* Weather Cards */}
            {weatherData.map((weather, index) => (
              <div key={`weather-${index}`} className={`rounded-lg p-1.5 text-center ${
                weather.city === 'Manila'
                  ? 'bg-purple-100 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800'
                  : 'bg-muted'
              }`}>
                <span className="text-lg block mb-0.5">{weather.emoji}</span>
                <div className="font-medium text-foreground text-xs mb-0.5">
                  {weather.city}
                </div>
                <div className="text-xs text-muted-foreground mb-0.5">
                  {weather.condition}
                </div>
                <div className={`text-xs font-bold ${getTemperatureColor(convertTemperature(weather.temperature))}`}>
                  {convertTemperature(weather.temperature)}°{useCelsius ? 'C' : 'F'}
                </div>
                <div className="text-xs text-muted-foreground">
                  {weather.humidity}%
                </div>
              </div>
            ))}
          </div>

          {/* Time Zones Row */}
          <div className="grid grid-cols-5 gap-1 sm:gap-2">
            {timeZones.map((tz) => (
              <div key={`timezone-${tz.name}`} className={`rounded-lg p-1.5 text-center ${
                tz.name === 'Philippines'
                  ? 'bg-purple-100 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800'
                  : 'bg-muted'
              }`}>
                <div className="font-medium text-foreground text-xs mb-0.5">
                  {tz.name}
                </div>
                <div className="text-xs text-muted-foreground mb-0.5">
                  {tz.abbreviation}
                </div>
                <div className="text-xs font-mono font-bold text-foreground mb-0.5">
                  {times[tz.name] || '--:--:--'}
                </div>
                <div className="text-xs text-muted-foreground">
                  {getTimeZoneDate(tz.timezone)}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="mt-2 text-xs text-muted-foreground text-center">
          Updates every 15 min / 1 sec
        </div>
      </CardContent>
    </Card>
  );
};

export default WeatherTimeDisplay;
