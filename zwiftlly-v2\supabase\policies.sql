-- ZWIFTLLY V2 Row Level Security Policies
-- This file contains all RLS policies for secure data access

-- Helper function to get current user's organization
CREATE OR REPLACE FUNCTION get_user_organization_id()
RETURNS UUID AS $$
BEGIN
    RETURN (SELECT organization_id FROM users WHERE id = auth.uid());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (SELECT role = 'ADMIN' FROM users WHERE id = auth.uid());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Organizations policies
CREATE POLICY "Users can view their organization" ON organizations
    FOR SELECT USING (id = get_user_organization_id());

CREATE POLICY "Admins can manage organizations" ON organizations
    FOR ALL USING (is_admin());

-- Users policies
CREATE POLICY "Users can view users in their organization" ON users
    FOR SELECT USING (organization_id = get_user_organization_id());

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (id = auth.uid());

CREATE POLICY "Admins can manage all users in their organization" ON users
    FOR ALL USING (is_admin() AND organization_id = get_user_organization_id());

-- Tasks policies
CREATE POLICY "Users can view tasks in their organization" ON tasks
    FOR SELECT USING (organization_id = get_user_organization_id());

CREATE POLICY "Users can create tasks in their organization" ON tasks
    FOR INSERT WITH CHECK (organization_id = get_user_organization_id());

CREATE POLICY "Users can update tasks assigned to them or created by them" ON tasks
    FOR UPDATE USING (
        organization_id = get_user_organization_id() AND 
        (assignee_id = auth.uid() OR created_by = auth.uid() OR is_admin())
    );

CREATE POLICY "Admins can delete tasks in their organization" ON tasks
    FOR DELETE USING (is_admin() AND organization_id = get_user_organization_id());

-- Task tags policies
CREATE POLICY "Users can view task tags for tasks in their organization" ON task_tags
    FOR SELECT USING (
        task_id IN (SELECT id FROM tasks WHERE organization_id = get_user_organization_id())
    );

CREATE POLICY "Users can manage task tags for tasks they can edit" ON task_tags
    FOR ALL USING (
        task_id IN (
            SELECT id FROM tasks 
            WHERE organization_id = get_user_organization_id() 
            AND (assignee_id = auth.uid() OR created_by = auth.uid() OR is_admin())
        )
    );

-- Announcements policies
CREATE POLICY "Users can view announcements in their organization" ON announcements
    FOR SELECT USING (organization_id = get_user_organization_id());

CREATE POLICY "Users can create announcements in their organization" ON announcements
    FOR INSERT WITH CHECK (organization_id = get_user_organization_id() AND author_id = auth.uid());

CREATE POLICY "Authors and admins can update their announcements" ON announcements
    FOR UPDATE USING (
        organization_id = get_user_organization_id() AND 
        (author_id = auth.uid() OR is_admin())
    );

CREATE POLICY "Authors and admins can delete their announcements" ON announcements
    FOR DELETE USING (
        organization_id = get_user_organization_id() AND 
        (author_id = auth.uid() OR is_admin())
    );

-- Announcement likes policies
CREATE POLICY "Users can view likes for announcements in their organization" ON announcement_likes
    FOR SELECT USING (
        announcement_id IN (SELECT id FROM announcements WHERE organization_id = get_user_organization_id())
    );

CREATE POLICY "Users can manage their own likes" ON announcement_likes
    FOR ALL USING (user_id = auth.uid());

-- Announcement comments policies
CREATE POLICY "Users can view comments for announcements in their organization" ON announcement_comments
    FOR SELECT USING (
        announcement_id IN (SELECT id FROM announcements WHERE organization_id = get_user_organization_id())
    );

CREATE POLICY "Users can create comments on announcements in their organization" ON announcement_comments
    FOR INSERT WITH CHECK (
        user_id = auth.uid() AND
        announcement_id IN (SELECT id FROM announcements WHERE organization_id = get_user_organization_id())
    );

CREATE POLICY "Users can update their own comments" ON announcement_comments
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users and admins can delete comments" ON announcement_comments
    FOR DELETE USING (user_id = auth.uid() OR is_admin());

-- Attendance records policies
CREATE POLICY "Users can view attendance records in their organization" ON attendance_records
    FOR SELECT USING (
        user_id IN (SELECT id FROM users WHERE organization_id = get_user_organization_id())
    );

CREATE POLICY "Users can create their own attendance records" ON attendance_records
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Admins can manage all attendance records in their organization" ON attendance_records
    FOR ALL USING (
        is_admin() AND 
        user_id IN (SELECT id FROM users WHERE organization_id = get_user_organization_id())
    );

-- Notifications policies
CREATE POLICY "Users can view their own notifications" ON notifications
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own notifications" ON notifications
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "System can create notifications for users" ON notifications
    FOR INSERT WITH CHECK (true); -- This will be restricted by application logic

-- Performance metrics policies
CREATE POLICY "Users can view performance metrics in their organization" ON performance_metrics
    FOR SELECT USING (
        organization_id = get_user_organization_id()
    );

CREATE POLICY "Users can view their own performance metrics" ON performance_metrics
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admins can manage performance metrics in their organization" ON performance_metrics
    FOR ALL USING (
        is_admin() AND organization_id = get_user_organization_id()
    );

-- System status policies
CREATE POLICY "Users can view system status for their organization" ON system_status
    FOR SELECT USING (organization_id = get_user_organization_id());

CREATE POLICY "Admins can manage system status for their organization" ON system_status
    FOR ALL USING (is_admin() AND organization_id = get_user_organization_id());

-- Volume tracking policies
CREATE POLICY "Users can view volume tracking in their organization" ON volume_tracking
    FOR SELECT USING (
        organization_id = get_user_organization_id()
    );

CREATE POLICY "Users can view their own volume tracking" ON volume_tracking
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admins can manage volume tracking in their organization" ON volume_tracking
    FOR ALL USING (
        is_admin() AND organization_id = get_user_organization_id()
    );
