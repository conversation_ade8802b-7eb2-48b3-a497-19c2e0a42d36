# ZWIFTLLY Team Management System - Environment Variables
# Copy this file to .env.local for local development

# Supabase Configuration (Required)
VITE_SUPABASE_URL=https://uhasayjbnnhoxmyjdtme.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Google OAuth Configuration (Required)
VITE_GOOGLE_CLIENT_ID=your_google_client_id_here

# Application Configuration
VITE_APP_NAME=ZWIFTLLY Team Management System
VITE_APP_URL=http://localhost:5173
VITE_ENVIRONMENT=development

# Organization Configuration (Default allowed domains)
VITE_DEFAULT_ALLOWED_DOMAINS=zwiftlly.com,netic.ai

# Specific allowed emails (for restricted domains like Gmail)
VITE_ALLOWED_EMAILS=<EMAIL>

# Debug mode for development
VITE_DEBUG_MODE=true

# Instructions:
# 1. Copy this file: cp .env.example .env.local
# 2. Replace the placeholder values with your actual configuration
# 3. The .env.local file is automatically ignored by git for security
