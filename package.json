{"name": "zwiftlly-team-management-system", "version": "1.0.0", "description": "Professional team management system with real-time collaboration features", "private": true, "type": "module", "scripts": {"dev": "npm run dev --workspace=zwiftlly-frontend", "build": "npm run build --workspace=zwiftlly-frontend", "preview": "npm run preview --workspace=zwiftlly-frontend", "lint": "npm run lint --workspace=zwiftlly-frontend", "type-check": "npm run type-check --workspace=zwiftlly-frontend", "test": "npm run test --workspace=zwiftlly-frontend", "clean": "npm run clean --workspace=zwiftlly-frontend", "deploy": "npm run build && npm run deploy:vercel", "deploy:vercel": "cd zwiftlly-frontend && vercel --prod", "setup": "npm install && npm run setup --workspace=zwiftlly-frontend", "db:types": "npx supabase gen types typescript --project-id uhasayjbnnhoxmyjdtme > zwiftlly-frontend/src/types/database.types.ts"}, "workspaces": ["zwiftlly-frontend"], "keywords": ["team-management", "collaboration", "real-time", "react", "typescript", "supabase", "vercel"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/DaddyRay18/ZWIFTLLY-Team-Management-System.git"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "vercel": "^32.0.0"}}