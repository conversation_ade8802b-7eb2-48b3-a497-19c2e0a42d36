import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

// Database types (based on our schema)
export interface Database {
  public: {
    Tables: {
      organizations: {
        Row: {
          id: string
          domain: string
          name: string
          default_role: 'SUPER_ADMIN' | 'ADMIN' | 'MANAGER' | 'AGENT' | 'VIEWER'
          admin_emails: string[]
          is_active: boolean
          settings: any
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          domain: string
          name: string
          default_role?: 'SUPER_ADMIN' | 'ADMIN' | 'MANAGER' | 'AGENT' | 'VIEWER'
          admin_emails?: string[]
          is_active?: boolean
          settings?: any
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          domain?: string
          name?: string
          default_role?: 'SUPER_ADMIN' | 'ADMIN' | 'MANAGER' | 'AGENT' | 'VIEWER'
          admin_emails?: string[]
          is_active?: boolean
          settings?: any
          created_at?: string
          updated_at?: string
        }
      }
      users: {
        Row: {
          id: string
          email: string
          name: string
          first_name: string | null
          last_name: string | null
          picture: string | null
          role: 'SUPER_ADMIN' | 'ADMIN' | 'MANAGER' | 'AGENT' | 'VIEWER'
          organization_id: string | null
          is_active: boolean
          last_login_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          name: string
          first_name?: string | null
          last_name?: string | null
          picture?: string | null
          role?: 'SUPER_ADMIN' | 'ADMIN' | 'MANAGER' | 'AGENT' | 'VIEWER'
          organization_id?: string | null
          is_active?: boolean
          last_login_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          first_name?: string | null
          last_name?: string | null
          picture?: string | null
          role?: 'SUPER_ADMIN' | 'ADMIN' | 'MANAGER' | 'AGENT' | 'VIEWER'
          organization_id?: string | null
          is_active?: boolean
          last_login_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      tasks: {
        Row: {
          id: string
          title: string
          description: string | null
          status: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'DONE' | 'CANCELLED'
          priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
          team_id: string
          created_by_id: string
          assignee_id: string | null
          due_date: string | null
          completed_at: string | null
          tags: string[]
          estimated_hours: number | null
          actual_hours: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          status?: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'DONE' | 'CANCELLED'
          priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
          team_id: string
          created_by_id: string
          assignee_id?: string | null
          due_date?: string | null
          completed_at?: string | null
          tags?: string[]
          estimated_hours?: number | null
          actual_hours?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          status?: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'DONE' | 'CANCELLED'
          priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
          team_id?: string
          created_by_id?: string
          assignee_id?: string | null
          due_date?: string | null
          completed_at?: string | null
          tags?: string[]
          estimated_hours?: number | null
          actual_hours?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      announcements: {
        Row: {
          id: string
          title: string
          content: string
          category: 'GENERAL' | 'QA' | 'ENGINEERING' | 'MANAGEMENT' | 'URGENT'
          author_id: string
          is_pinned: boolean
          is_published: boolean
          published_at: string | null
          expires_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          content: string
          category?: 'GENERAL' | 'QA' | 'ENGINEERING' | 'MANAGEMENT' | 'URGENT'
          author_id: string
          is_pinned?: boolean
          is_published?: boolean
          published_at?: string | null
          expires_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          content?: string
          category?: 'GENERAL' | 'QA' | 'ENGINEERING' | 'MANAGEMENT' | 'URGENT'
          author_id?: string
          is_pinned?: boolean
          is_published?: boolean
          published_at?: string | null
          expires_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      notifications: {
        Row: {
          id: string
          user_id: string
          type: 'ANNOUNCEMENT' | 'TASK_ASSIGNED' | 'TASK_COMPLETED' | 'ATTENDANCE_REMINDER' | 'SYSTEM'
          title: string
          message: string
          data: any
          is_read: boolean
          is_archived: boolean
          created_at: string
          read_at: string | null
          archived_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          type: 'ANNOUNCEMENT' | 'TASK_ASSIGNED' | 'TASK_COMPLETED' | 'ATTENDANCE_REMINDER' | 'SYSTEM'
          title: string
          message: string
          data?: any
          is_read?: boolean
          is_archived?: boolean
          created_at?: string
          read_at?: string | null
          archived_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          type?: 'ANNOUNCEMENT' | 'TASK_ASSIGNED' | 'TASK_COMPLETED' | 'ATTENDANCE_REMINDER' | 'SYSTEM'
          title?: string
          message?: string
          data?: any
          is_read?: boolean
          is_archived?: boolean
          created_at?: string
          read_at?: string | null
          archived_at?: string | null
        }
      }
      attendance_records: {
        Row: {
          id: string
          user_id: string
          date: string
          clock_in_time: string | null
          clock_out_time: string | null
          break_duration: number
          total_hours: number
          is_present: boolean
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          date: string
          clock_in_time?: string | null
          clock_out_time?: string | null
          break_duration?: number
          total_hours?: number
          is_present?: boolean
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          date?: string
          clock_in_time?: string | null
          clock_out_time?: string | null
          break_duration?: number
          total_hours?: number
          is_present?: boolean
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      clock_actions: {
        Row: {
          id: string
          user_id: string
          action_type: 'CLOCK_IN' | 'CLOCK_OUT' | 'BREAK_START' | 'BREAK_END'
          timestamp: string
          location: string | null
          ip_address: string | null
          notes: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          action_type: 'CLOCK_IN' | 'CLOCK_OUT' | 'BREAK_START' | 'BREAK_END'
          timestamp?: string
          location?: string | null
          ip_address?: string | null
          notes?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          action_type?: 'CLOCK_IN' | 'CLOCK_OUT' | 'BREAK_START' | 'BREAK_END'
          timestamp?: string
          location?: string | null
          ip_address?: string | null
          notes?: string | null
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      handle_clock_action: {
        Args: {
          p_action_type: 'CLOCK_IN' | 'CLOCK_OUT' | 'BREAK_START' | 'BREAK_END'
          p_location?: string
          p_notes?: string
        }
        Returns: any
      }
      get_user_dashboard: {
        Args: {}
        Returns: any
      }
      create_notification: {
        Args: {
          p_user_id: string
          p_type: 'ANNOUNCEMENT' | 'TASK_ASSIGNED' | 'TASK_COMPLETED' | 'ATTENDANCE_REMINDER' | 'SYSTEM'
          p_title: string
          p_message: string
          p_data?: any
        }
        Returns: string
      }
    }
    Enums: {
      user_role: 'SUPER_ADMIN' | 'ADMIN' | 'MANAGER' | 'AGENT' | 'VIEWER'
      team_role: 'LEADER' | 'MEMBER'
      task_status: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'DONE' | 'CANCELLED'
      priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
      clock_action_type: 'CLOCK_IN' | 'CLOCK_OUT' | 'BREAK_START' | 'BREAK_END'
      announcement_category: 'GENERAL' | 'QA' | 'ENGINEERING' | 'MANAGEMENT' | 'URGENT'
      notification_type: 'ANNOUNCEMENT' | 'TASK_ASSIGNED' | 'TASK_COMPLETED' | 'ATTENDANCE_REMINDER' | 'SYSTEM'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T]
