{"version": 2, "name": "zwiftlly-team-management-system", "buildCommand": "cd zwiftlly-frontend && npm run build", "outputDirectory": "zwiftlly-frontend/dist", "installCommand": "cd zwiftlly-frontend && npm install", "devCommand": "cd zwiftlly-frontend && npm run dev", "framework": "vite", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}