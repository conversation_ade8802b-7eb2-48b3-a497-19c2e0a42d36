import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const DebugAuth: React.FC = () => {
  const envVars = {
    VITE_GOOGLE_CLIENT_ID: import.meta.env.VITE_GOOGLE_CLIENT_ID,
    VITE_ALLOWED_EMAILS: import.meta.env.VITE_ALLOWED_EMAILS,
    VITE_DEFAULT_ALLOWED_DOMAINS: import.meta.env.VITE_DEFAULT_ALLOWED_DOMAINS,
    MODE: import.meta.env.MODE,
    DEV: import.meta.env.DEV,
    PROD: import.meta.env.PROD
  };

  const storedData = {
    organizations: localStorage.getItem('zwiftlly_organizations'),
    users: localStorage.getItem('zwiftlly_google_users'),
    currentUser: localStorage.getItem('zwiftlly_google_user'),
    demoMode: localStorage.getItem('demo_mode')
  };

  const clearAllData = () => {
    localStorage.removeItem('zwiftlly_organizations');
    localStorage.removeItem('zwiftlly_google_users');
    localStorage.removeItem('zwiftlly_google_user');
    localStorage.removeItem('demo_mode');
    window.location.reload();
  };

  const enableDemoMode = () => {
    localStorage.setItem('demo_mode', 'true');
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-foreground mb-2">Authentication Debug Panel</h1>
          <p className="text-muted-foreground">Debug information for troubleshooting login issues</p>
        </div>

        {/* Environment Variables */}
        <Card>
          <CardHeader>
            <CardTitle>Environment Variables</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(envVars).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                  <span className="font-medium">{key}:</span>
                  <Badge variant={value ? 'default' : 'destructive'}>
                    {value ? (key.includes('CLIENT_ID') ? `${String(value).substring(0, 20)}...` : String(value)) : 'Not Set'}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Local Storage Data */}
        <Card>
          <CardHeader>
            <CardTitle>Local Storage Data</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(storedData).map(([key, value]) => (
                <div key={key} className="p-3 bg-muted/30 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">{key}:</span>
                    <Badge variant={value ? 'default' : 'secondary'}>
                      {value ? 'Set' : 'Empty'}
                    </Badge>
                  </div>
                  {value && (
                    <pre className="text-xs bg-background p-2 rounded border overflow-auto max-h-32">
                      {JSON.stringify(JSON.parse(value), null, 2)}
                    </pre>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Expected Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>Expected <NAME_EMAIL></CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">Required Settings:</h4>
                <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
                  <li>✓ VITE_ALLOWED_EMAILS should contain: <EMAIL></li>
                  <li>✓ VITE_GOOGLE_CLIENT_ID should be set to your Google OAuth client ID</li>
                  <li>✓ User should get 'super_admin' role automatically</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Debug Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Debug Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-3">
              <Button onClick={clearAllData} variant="destructive">
                Clear All Local Storage
              </Button>
              <Button onClick={enableDemoMode} variant="secondary">
                Enable Demo Mode
              </Button>
              <Button onClick={() => window.location.reload()} variant="outline">
                Reload Page
              </Button>
              <Button onClick={() => console.log('Current state:', { envVars, storedData })} variant="outline">
                Log to Console
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Browser Console Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Browser Console Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <p className="text-sm text-muted-foreground">
                Open your browser's developer tools (F12) and check the Console tab for detailed login logs.
                Look for messages starting with 🔐, 🔍, ✅, or ❌.
              </p>
              <div className="p-3 bg-muted/30 rounded-lg">
                <h4 className="font-medium mb-2">Common Issues:</h4>
                <ul className="text-sm space-y-1">
                  <li>• <strong>JWT Decoding Error:</strong> Google OAuth token format issue</li>
                  <li>• <strong>Email Not Allowed:</strong> VITE_ALLOWED_EMAILS not configured correctly</li>
                  <li>• <strong>Network Error:</strong> Google OAuth service unavailable</li>
                  <li>• <strong>Client ID Error:</strong> Invalid or missing Google OAuth client ID</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DebugAuth;
