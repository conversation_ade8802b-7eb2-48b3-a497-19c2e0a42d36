import React, { useState, useEffect } from 'react';


// Simple Error Boundary Component
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('App Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-red-50 flex items-center justify-center p-4">
          <div className="text-center space-y-4">
            <h1 className="text-2xl font-bold text-red-800">Something went wrong</h1>
            <p className="text-red-600">Error: {this.state.error?.message}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
import Sidebar from './components/Sidebar';
import TopNavigation from './components/TopNavigation';
import Dashboard from './components/Dashboard';
import KnowledgeBase from './components/KnowledgeBase';
import DocumentsPage from './components/DocumentsPage';
import Announcements from './components/Announcements';
import AdherenceBoard from './components/adherence/AdherenceBoard';
import TeamPerformance from './components/TeamPerformance';
import TaskBoard from './components/TaskBoard';
import Settings from './components/Settings';
import UserProfile from './components/UserProfile';
import AttendanceManagement from './components/AttendanceManagement';
import AIAssistant from './components/AIAssistant';
import Login from './components/Login';
import DebugAuth from './components/DebugAuth';
import ProtectedRoute from './components/ProtectedRoute';
import { Button } from './components/ui/button';
import { Clock } from 'lucide-react';
import { AuthProvider, useAuth } from './contexts/SupabaseAuthContext';
import { CompanyProvider } from './contexts/CompanyContext';
import { DataProvider } from './contexts/DataContext';
import './App.css';

function AppContent() {
  const { user, isLoading } = useAuth();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [darkMode, setDarkMode] = useState(() => {
    // Check if user has a saved preference, otherwise use system preference
    const saved = localStorage.getItem('darkMode');
    if (saved !== null) {
      return JSON.parse(saved);
    }
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  });
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [documentsPageData, setDocumentsPageData] = useState<{
    moduleId: string;
    moduleName: string;
  } | null>(null);
  const [showUserProfile, setShowUserProfile] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  const handleNavigateToDocuments = (moduleId: string, moduleName: string) => {
    setDocumentsPageData({ moduleId, moduleName });
    setCurrentPage('documents');
  };

  const handleBackToKnowledgeBase = () => {
    setDocumentsPageData(null);
    setCurrentPage('knowledge');
  };

  useEffect(() => {
    // Apply dark mode class and save preference
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    localStorage.setItem('darkMode', JSON.stringify(darkMode));
  }, [darkMode]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg mx-auto animate-pulse">
            <span className="text-white font-bold text-xl">Z</span>
          </div>
          <div className="text-lg font-medium text-foreground">Loading ZWIFTLLY...</div>
        </div>
      </div>
    );
  }

  // Check for debug mode
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('debug') === 'auth') {
    return <DebugAuth />;
  }

  // Show login if not authenticated
  if (!user) {
    return <Login />;
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="flex h-screen">
        {/* Sidebar */}
        <Sidebar
          collapsed={sidebarCollapsed}
          onToggle={toggleSidebar}
          currentPage={currentPage}
          onPageChange={setCurrentPage}
        />

        {/* Main Content */}
        <div className={`flex-1 flex flex-col transition-all duration-300 ${sidebarCollapsed ? 'ml-12 sm:ml-14' : 'ml-48 sm:ml-56'}`}>
          {/* Top Navigation */}
          <TopNavigation
            darkMode={darkMode}
            onToggleDarkMode={toggleDarkMode}
            onProfileClick={() => setShowUserProfile(true)}
          />

          {/* Page Content */}
          <main className="flex-1 p-1 sm:p-2 overflow-auto">
            {showUserProfile ? (
              <UserProfile onBack={() => setShowUserProfile(false)} />
            ) : (
              <>
                {currentPage === 'dashboard' && (
                  <ProtectedRoute permission="view_dashboard">
                    <Dashboard />
                  </ProtectedRoute>
                )}
                {currentPage === 'announcements' && (
                  <ProtectedRoute permission="view_dashboard">
                    <Announcements />
                  </ProtectedRoute>
                )}
                {currentPage === 'calendar' && (
                  <ProtectedRoute permission="view_schedules">
                    <div className="min-h-screen bg-background">
                      <div className="container mx-auto px-4 py-8">
                        {/* Header with Animation */}
                        <div className="mb-8 animate-fade-in-up animate-delay-0">
                          <div className="flex items-center justify-between">
                            <div>
                              <h1 className="text-3xl font-bold text-foreground mb-2">
                                Team Schedules
                              </h1>
                              <p className="text-muted-foreground">
                                Manage schedules, track time, and monitor team adherence
                              </p>
                            </div>
                            <Button
                              onClick={() => setCurrentPage('attendance')}
                              className="gradient-button-primary hover:opacity-90 text-primary-foreground"
                            >
                              <Clock className="w-4 h-4 mr-2" />
                              Attendance Management
                            </Button>
                          </div>
                        </div>

                        {/* Live Adherence Board with Animation */}
                        <div className="animate-fade-in-up animate-delay-150">
                          <AdherenceBoard />
                        </div>
                      </div>
                    </div>
                  </ProtectedRoute>
                )}
                {currentPage === 'tasks' && (
                  <ProtectedRoute permission="view_tasks">
                    <TaskBoard />
                  </ProtectedRoute>
                )}
                {currentPage === 'performance' && (
                  <ProtectedRoute permission="view_performance">
                    <TeamPerformance />
                  </ProtectedRoute>
                )}
                {currentPage === 'attendance' && (
                  <ProtectedRoute permission="view_schedules">
                    <AttendanceManagement />
                  </ProtectedRoute>
                )}
                {currentPage === 'settings' && (
                  <ProtectedRoute permission="all">
                    <Settings />
                  </ProtectedRoute>
                )}
                {currentPage === 'knowledge' && (
                  <ProtectedRoute permission="view_knowledge">
                    <KnowledgeBase onNavigateToDocuments={handleNavigateToDocuments} />
                  </ProtectedRoute>
                )}
                {currentPage === 'documents' && documentsPageData && (
                  <ProtectedRoute permission="view_knowledge">
                    <DocumentsPage
                      moduleId={documentsPageData.moduleId}
                      moduleName={documentsPageData.moduleName}
                      onBack={handleBackToKnowledgeBase}
                    />
                  </ProtectedRoute>
                )}
              </>
            )}
          </main>
        </div>

        {/* AI Assistant - Always visible */}
        <AIAssistant />
      </div>
    </div>
  );
}

function App() {
  // Debug logging
  console.log('🔍 Environment check:', {
    supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
    hasSupabaseKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
    nodeEnv: import.meta.env.MODE
  });

  // Clear ALL old localStorage data that might conflict with Supabase
  React.useEffect(() => {
    // Clear old auth system data
    localStorage.removeItem('demo_mode');
    localStorage.removeItem('zwiftlly_user');
    localStorage.removeItem('zwiftlly_auth_token');
    localStorage.removeItem('auth_token');
    localStorage.removeItem('zwiftlly_google_user');
    localStorage.removeItem('zwiftlly_google_users');
    localStorage.removeItem('zwiftlly_organizations');

    console.log('🧹 Cleared all old localStorage data for clean Supabase start');
  }, []);

  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 flex items-center justify-center p-4">
        <div className="text-center space-y-6 max-w-lg">
          <div className="w-20 h-20 bg-gradient-to-r from-red-600 to-orange-600 rounded-xl flex items-center justify-center shadow-lg mx-auto">
            <span className="text-white font-bold text-2xl">⚙️</span>
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground mb-2">Supabase Configuration Required</h1>
            <div className="text-muted-foreground space-y-2">
              <p>Please configure your Supabase URL and anon key in the .env file</p>
              <p className="text-xs">URL: {supabaseUrl || 'Not set'}</p>
              <p className="text-xs">Key: {supabaseKey ? 'Set' : 'Not set'}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  console.log('🔧 App: Rendering with AuthProvider from SupabaseAuthContext');

  return (
    <ErrorBoundary>
      <CompanyProvider>
        <AuthProvider>
          <DataProvider>
            <AppContent />
          </DataProvider>
        </AuthProvider>
      </CompanyProvider>
    </ErrorBoundary>
  );
}

export default App;
