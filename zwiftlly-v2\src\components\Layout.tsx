import React, { useState, useEffect } from 'react';
import Sidebar from './Sidebar';
import TopNavigation from './TopNavigation';
import Dashboard from './Dashboard';

const Layout: React.FC = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [darkMode, setDarkMode] = useState(false);

  // Initialize dark mode from localStorage
  useEffect(() => {
    const savedDarkMode = localStorage.getItem('darkMode');
    if (savedDarkMode) {
      setDarkMode(JSON.parse(savedDarkMode));
    }
  }, []);

  // Apply dark mode to document
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    localStorage.setItem('darkMode', JSON.stringify(darkMode));
  }, [darkMode]);

  const handleToggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const handlePageChange = (page: string) => {
    setCurrentPage(page);
  };

  const handleToggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  const handleProfileClick = () => {
    setCurrentPage('profile');
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard />;
      case 'announcements':
        return <div className="p-4"><h1 className="text-2xl font-bold">Announcements</h1><p>Coming soon...</p></div>;
      case 'calendar':
        return <div className="p-4"><h1 className="text-2xl font-bold">Team Schedules</h1><p>Coming soon...</p></div>;
      case 'tasks':
        return <div className="p-4"><h1 className="text-2xl font-bold">Task Board</h1><p>Coming soon...</p></div>;
      case 'performance':
        return <div className="p-4"><h1 className="text-2xl font-bold">Team Performance</h1><p>Coming soon...</p></div>;
      case 'knowledge':
        return <div className="p-4"><h1 className="text-2xl font-bold">Knowledge Base</h1><p>Coming soon...</p></div>;
      case 'settings':
        return <div className="p-4"><h1 className="text-2xl font-bold">Settings</h1><p>Coming soon...</p></div>;
      case 'profile':
        return <div className="p-4"><h1 className="text-2xl font-bold">User Profile</h1><p>Coming soon...</p></div>;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="h-screen bg-background text-foreground overflow-hidden">
      {/* Sidebar */}
      <Sidebar
        collapsed={sidebarCollapsed}
        onToggle={handleToggleSidebar}
        currentPage={currentPage}
        onPageChange={handlePageChange}
      />

      {/* Main Content Area */}
      <div className={`transition-all duration-300 ${
        sidebarCollapsed ? 'ml-12 sm:ml-14' : 'ml-48 sm:ml-56'
      }`}>
        {/* Top Navigation */}
        <TopNavigation
          darkMode={darkMode}
          onToggleDarkMode={handleToggleDarkMode}
          onProfileClick={handleProfileClick}
        />

        {/* Page Content */}
        <main className="h-[calc(100vh-3.5rem)] overflow-auto p-1 sm:p-2">
          {renderCurrentPage()}
        </main>
      </div>
    </div>
  );
};

export default Layout;
