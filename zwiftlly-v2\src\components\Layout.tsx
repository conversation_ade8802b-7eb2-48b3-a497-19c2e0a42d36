import React, { useState } from 'react';
import Sidebar from './Sidebar';
import TopNavigation from './TopNavigation';

interface LayoutProps {
  children: React.ReactNode;
  currentPage: string;
  onPageChange: (page: string) => void;
  darkMode: boolean;
  onToggleDarkMode: () => void;
}

const Layout: React.FC<LayoutProps> = ({
  children,
  currentPage,
  onPageChange,
  darkMode,
  onToggleDarkMode
}) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const handleToggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const handleProfileClick = () => {
    onPageChange('profile');
  };

  return (
    <div className="h-screen bg-background text-foreground overflow-hidden">
      {/* Sidebar */}
      <Sidebar
        collapsed={sidebarCollapsed}
        onToggle={handleToggleSidebar}
        currentPage={currentPage}
        onPageChange={onPageChange}
        darkMode={darkMode}
      />

      {/* Main Content Area */}
      <div className={`transition-all duration-300 ${
        sidebarCollapsed ? 'ml-12 sm:ml-14' : 'ml-48 sm:ml-56'
      }`}>
        {/* Top Navigation */}
        <TopNavigation
          darkMode={darkMode}
          onToggleDarkMode={onToggleDarkMode}
          onProfileClick={handleProfileClick}
        />

        {/* Page Content */}
        <main className="h-[calc(100vh-3.5rem)] overflow-auto p-1 sm:p-2">
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;
