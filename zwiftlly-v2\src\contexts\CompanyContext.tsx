import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase, type Database } from '../lib/supabase';
import { useAuth } from '../AuthContext';

type Organization = Database['public']['Tables']['organizations']['Row'];
type User = Database['public']['Tables']['users']['Row'];

interface CompanyContextType {
  organization: Organization | null;
  users: User[];
  isLoading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;
  updateOrganization: (updates: Partial<Organization>) => Promise<boolean>;
}

const CompanyContext = createContext<CompanyContextType | undefined>(undefined);

export const useCompany = () => {
  const context = useContext(CompanyContext);
  if (context === undefined) {
    throw new Error('useCompany must be used within a CompanyProvider');
  }
  return context;
};

interface CompanyProviderProps {
  children: React.ReactNode;
}

export const CompanyProvider: React.FC<CompanyProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchOrganizationData = async () => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    try {
      setError(null);

      // Get current user's full profile to get organization_id
      const { data: userProfile, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      // If user doesn't exist in database yet, create mock data for development
      if (userError || !userProfile) {
        console.log('User not found in database, using mock data for development');

        // Create mock organization
        const mockOrg: Organization = {
          id: 'mock-org-1',
          name: 'ZWIFTLLY',
          domain: user.email.split('@')[1],
          settings: {},
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Create mock user profile with proper role from auth
        const mockUser: User = {
          id: user.id,
          email: user.email,
          full_name: user.name,
          avatar_url: user.picture || null,
          role: user.role, // Use role from auth context (SUPER_ADMIN for your email)
          organization_id: 'mock-org-1',
          settings: {},
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Add some additional mock users for testing
        const additionalMockUsers: User[] = [
          {
            id: 'mock-user-2',
            email: '<EMAIL>',
            full_name: 'Sarah Johnson',
            avatar_url: null,
            role: 'ADMIN',
            organization_id: 'mock-org-1',
            settings: {},
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 'mock-user-3',
            email: '<EMAIL>',
            full_name: 'Mike Chen',
            avatar_url: null,
            role: 'AGENT',
            organization_id: 'mock-org-1',
            settings: {},
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 'mock-user-4',
            email: '<EMAIL>',
            full_name: 'Emily Davis',
            avatar_url: null,
            role: 'USER',
            organization_id: 'mock-org-1',
            settings: {},
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ];

        setOrganization(mockOrg);
        setUsers([mockUser, ...additionalMockUsers]);
        setIsLoading(false);
        return;
      }

      if (!userProfile?.organization_id) {
        setError('User is not associated with any organization');
        setIsLoading(false);
        return;
      }

      // Get organization details
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', userProfile.organization_id)
        .single();

      if (orgError) {
        throw orgError;
      }

      setOrganization(orgData);

      // Get all users in the organization
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select('*')
        .eq('organization_id', userProfile.organization_id)
        .order('created_at', { ascending: false });

      if (usersError) {
        throw usersError;
      }

      setUsers(usersData || []);
    } catch (err) {
      console.error('Error fetching organization data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch organization data');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshData = async () => {
    setIsLoading(true);
    await fetchOrganizationData();
  };

  const updateOrganization = async (updates: Partial<Organization>): Promise<boolean> => {
    if (!organization) return false;

    try {
      const { error } = await supabase
        .from('organizations')
        .update(updates)
        .eq('id', organization.id);

      if (error) {
        throw error;
      }

      // Update local state
      setOrganization(prev => prev ? { ...prev, ...updates } : null);
      return true;
    } catch (err) {
      console.error('Error updating organization:', err);
      setError(err instanceof Error ? err.message : 'Failed to update organization');
      return false;
    }
  };

  useEffect(() => {
    fetchOrganizationData();
  }, [user]);

  // Set up real-time subscription for organization users
  useEffect(() => {
    if (!organization) return;

    const subscription = supabase
      .channel('organization-users')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'users',
          filter: `organization_id=eq.${organization.id}`
        },
        (payload) => {
          console.log('User change detected:', payload);
          // Refresh users data when changes occur
          fetchOrganizationData();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [organization]);

  const value: CompanyContextType = {
    organization,
    users,
    isLoading,
    error,
    refreshData,
    updateOrganization
  };

  return (
    <CompanyContext.Provider value={value}>
      {children}
    </CompanyContext.Provider>
  );
};
