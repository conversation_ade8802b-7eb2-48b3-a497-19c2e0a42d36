import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import { TrendingUp, Users, Clock, Award, Brain, Calendar, User, Target } from 'lucide-react';

interface TeamMember {
  id: string;
  name: string;
  audits: {
    daily: number;
    weekly: number;
    monthly: number;
    quarterly: number;
    yearly: number;
    overall: number;
  };
  idleTime: {
    daily: number;
    weekly: number;
    monthly: number;
    quarterly: number;
    yearly: number;
    overall: number;
  };
  qualityScore: number;
  breaks: {
    daily: number;
    weekly: number;
    monthly: number;
    quarterly: number;
    yearly: number;
    overall: number;
  };
}

const TeamPerformance: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'overall'>('monthly');
  const [selectedView, setSelectedView] = useState<'team' | 'individual'>('team');
  const [selectedMember, setSelectedMember] = useState<string>('all');

  // Mock team data
  const teamMembers: TeamMember[] = [
    {
      id: '1',
      name: 'Louise',
      audits: { daily: 12, weekly: 84, monthly: 360, quarterly: 1080, yearly: 4320, overall: 8640 },
      idleTime: { daily: 45, weekly: 315, monthly: 1350, quarterly: 4050, yearly: 16200, overall: 32400 },
      qualityScore: 94,
      breaks: { daily: 3, weekly: 21, monthly: 90, quarterly: 270, yearly: 1080, overall: 2160 }
    },
    {
      id: '2',
      name: 'Rose',
      audits: { daily: 15, weekly: 105, monthly: 450, quarterly: 1350, yearly: 5400, overall: 10800 },
      idleTime: { daily: 38, weekly: 266, monthly: 1140, quarterly: 3420, yearly: 13680, overall: 27360 },
      qualityScore: 97,
      breaks: { daily: 2, weekly: 14, monthly: 60, quarterly: 180, yearly: 720, overall: 1440 }
    },
    {
      id: '3',
      name: 'Bless-Ann',
      audits: { daily: 10, weekly: 70, monthly: 300, quarterly: 900, yearly: 3600, overall: 7200 },
      idleTime: { daily: 52, weekly: 364, monthly: 1560, quarterly: 4680, yearly: 18720, overall: 37440 },
      qualityScore: 89,
      breaks: { daily: 4, weekly: 28, monthly: 120, quarterly: 360, yearly: 1440, overall: 2880 }
    },
    {
      id: '4',
      name: 'Pearl',
      audits: { daily: 13, weekly: 91, monthly: 390, quarterly: 1170, yearly: 4680, overall: 9360 },
      idleTime: { daily: 41, weekly: 287, monthly: 1230, quarterly: 3690, yearly: 14760, overall: 29520 },
      qualityScore: 92,
      breaks: { daily: 3, weekly: 21, monthly: 90, quarterly: 270, yearly: 1080, overall: 2160 }
    },
    {
      id: '5',
      name: 'Christine G.',
      audits: { daily: 14, weekly: 98, monthly: 420, quarterly: 1260, yearly: 5040, overall: 10080 },
      idleTime: { daily: 35, weekly: 245, monthly: 1050, quarterly: 3150, yearly: 12600, overall: 25200 },
      qualityScore: 96,
      breaks: { daily: 2, weekly: 14, monthly: 60, quarterly: 180, yearly: 720, overall: 1440 }
    },
    {
      id: '6',
      name: 'Christine D.',
      audits: { daily: 11, weekly: 77, monthly: 330, quarterly: 990, yearly: 3960, overall: 7920 },
      idleTime: { daily: 48, weekly: 336, monthly: 1440, quarterly: 4320, yearly: 17280, overall: 34560 },
      qualityScore: 88,
      breaks: { daily: 4, weekly: 28, monthly: 120, quarterly: 360, yearly: 1440, overall: 2880 }
    },
    {
      id: '7',
      name: 'Ray',
      audits: { daily: 16, weekly: 112, monthly: 480, quarterly: 1440, yearly: 5760, overall: 11520 },
      idleTime: { daily: 32, weekly: 224, monthly: 960, quarterly: 2880, yearly: 11520, overall: 23040 },
      qualityScore: 98,
      breaks: { daily: 2, weekly: 14, monthly: 60, quarterly: 180, yearly: 720, overall: 1440 }
    },
    {
      id: '8',
      name: 'Gizelle',
      audits: { daily: 9, weekly: 63, monthly: 270, quarterly: 810, yearly: 3240, overall: 6480 },
      idleTime: { daily: 55, weekly: 385, monthly: 1650, quarterly: 4950, yearly: 19800, overall: 39600 },
      qualityScore: 85,
      breaks: { daily: 5, weekly: 35, monthly: 150, quarterly: 450, yearly: 1800, overall: 3600 }
    },
    {
      id: '9',
      name: 'Dustin',
      audits: { daily: 12, weekly: 84, monthly: 360, quarterly: 1080, yearly: 4320, overall: 8640 },
      idleTime: { daily: 43, weekly: 301, monthly: 1290, quarterly: 3870, yearly: 15480, overall: 30960 },
      qualityScore: 91,
      breaks: { daily: 3, weekly: 21, monthly: 90, quarterly: 270, yearly: 1080, overall: 2160 }
    }
  ];

  // Calculate team totals
  const getTeamData = () => {
    const totalAudits = teamMembers.reduce((sum, member) => sum + member.audits[selectedPeriod], 0);
    const totalIdleTime = teamMembers.reduce((sum, member) => sum + member.idleTime[selectedPeriod], 0);
    const avgQualityScore = teamMembers.reduce((sum, member) => sum + member.qualityScore, 0) / teamMembers.length;
    const totalBreaks = teamMembers.reduce((sum, member) => sum + member.breaks[selectedPeriod], 0);
    
    return { totalAudits, totalIdleTime, avgQualityScore, totalBreaks };
  };

  // Get individual member data
  const getIndividualData = () => {
    if (selectedMember === 'all') return null;
    return teamMembers.find(member => member.id === selectedMember);
  };

  const teamData = getTeamData();
  const individualData = getIndividualData();

  // Chart data for audits
  const auditChartData = teamMembers.map(member => ({
    name: member.name,
    audits: member.audits[selectedPeriod],
    qualityScore: member.qualityScore
  }));

  // Chart data for idle time
  const idleTimeChartData = teamMembers.map(member => ({
    name: member.name,
    idleTime: member.idleTime[selectedPeriod]
  }));

  // Quality score distribution
  const qualityDistribution = [
    { name: '90-100%', value: teamMembers.filter(m => m.qualityScore >= 90).length, color: '#10b981' },
    { name: '80-89%', value: teamMembers.filter(m => m.qualityScore >= 80 && m.qualityScore < 90).length, color: '#f59e0b' },
    { name: '70-79%', value: teamMembers.filter(m => m.qualityScore >= 70 && m.qualityScore < 80).length, color: '#ef4444' },
    { name: 'Below 70%', value: teamMembers.filter(m => m.qualityScore < 70).length, color: '#dc2626' }
  ];

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-foreground mb-2">
                Team Performance Analytics
              </h1>
              <p className="text-muted-foreground">
                Comprehensive performance metrics and insights
              </p>
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4 text-muted-foreground" />
              <Select value={selectedPeriod} onValueChange={(value: any) => setSelectedPeriod(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="quarterly">Quarterly</SelectItem>
                  <SelectItem value="yearly">Yearly</SelectItem>
                  <SelectItem value="overall">Overall</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Users className="w-4 h-4 text-muted-foreground" />
              <Select value={selectedView} onValueChange={(value: any) => setSelectedView(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Select view" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="team">Team View</SelectItem>
                  <SelectItem value="individual">Individual View</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {selectedView === 'individual' && (
              <div className="flex items-center space-x-2">
                <User className="w-4 h-4 text-muted-foreground" />
                <Select value={selectedMember} onValueChange={setSelectedMember}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Select member" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Members</SelectItem>
                    {teamMembers.map(member => (
                      <SelectItem key={member.id} value={member.id}>{member.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </div>

        {/* Key Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Audits</CardTitle>
              <Target className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {selectedView === 'team' ? teamData.totalAudits.toLocaleString() : 
                 individualData ? individualData.audits[selectedPeriod].toLocaleString() : 'Select Member'}
              </div>
              <p className="text-xs text-muted-foreground">
                {selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)} period
              </p>
            </CardContent>
          </Card>

          <Card className="transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Idle Time (mins)</CardTitle>
              <Clock className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {selectedView === 'team' ? teamData.totalIdleTime.toLocaleString() : 
                 individualData ? individualData.idleTime[selectedPeriod].toLocaleString() : 'Select Member'}
              </div>
              <p className="text-xs text-muted-foreground">
                Unproductive time tracked
              </p>
            </CardContent>
          </Card>

          <Card className="transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Quality Score</CardTitle>
              <Award className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {selectedView === 'team' ? `${teamData.avgQualityScore.toFixed(1)}%` : 
                 individualData ? `${individualData.qualityScore}%` : 'Select Member'}
              </div>
              <p className="text-xs text-muted-foreground">
                {selectedView === 'team' ? 'Average team score' : 'Individual score'}
              </p>
            </CardContent>
          </Card>

          <Card className="transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Breaks</CardTitle>
              <TrendingUp className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {selectedView === 'team' ? teamData.totalBreaks.toLocaleString() : 
                 individualData ? individualData.breaks[selectedPeriod].toLocaleString() : 'Select Member'}
              </div>
              <p className="text-xs text-muted-foreground">
                Break sessions taken
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Audits Performance Chart */}
          <Card className="transform transition-all duration-300 hover:shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="w-5 h-5 text-blue-500" />
                <span>Audits Performance</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={auditChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="audits" fill="#3b82f6" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Idle Time Chart */}
          <Card className="transform transition-all duration-300 hover:shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Clock className="w-5 h-5 text-orange-500" />
                <span>Idle Time Analysis</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={idleTimeChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="idleTime" fill="#f59e0b" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Quality Score Distribution */}
          <Card className="transform transition-all duration-300 hover:shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Award className="w-5 h-5 text-green-500" />
                <span>Quality Score Distribution</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={qualityDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, value }) => `${name}: ${value}`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {qualityDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Quality vs Audits Correlation */}
          <Card className="transform transition-all duration-300 hover:shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-purple-500" />
                <span>Quality vs Performance</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={auditChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Bar yAxisId="left" dataKey="audits" fill="#8884d8" />
                  <Line yAxisId="right" type="monotone" dataKey="qualityScore" stroke="#82ca9d" strokeWidth={3} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* AI Observations and Recommendations */}
        <Card className="transform transition-all duration-300 hover:shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Brain className="w-5 h-5 text-indigo-500" />
              <span>AI Observations & Recommendations</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Performance Insights */}
              <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-2 flex items-center">
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Performance Insights
                </h3>
                <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                  <li>• <strong>Ray</strong> leads in audit completion with {teamMembers.find(m => m.name === 'Ray')?.audits[selectedPeriod]} audits and maintains 98% quality score</li>
                  <li>• <strong>Rose</strong> shows excellent consistency with high audit volume and 97% quality score</li>
                  <li>• Team average quality score of {teamData.avgQualityScore.toFixed(1)}% exceeds industry standards</li>
                  <li>• {teamMembers.filter(m => m.qualityScore >= 90).length} out of {teamMembers.length} team members maintain 90%+ quality scores</li>
                </ul>
              </div>

              {/* Areas for Improvement */}
              <div className="bg-orange-50 dark:bg-orange-950/20 p-4 rounded-lg border border-orange-200 dark:border-orange-800">
                <h3 className="font-semibold text-orange-800 dark:text-orange-200 mb-2 flex items-center">
                  <Clock className="w-4 h-4 mr-2" />
                  Areas for Improvement
                </h3>
                <ul className="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                  <li>• <strong>Gizelle</strong> shows highest idle time ({teamMembers.find(m => m.name === 'Gizelle')?.idleTime[selectedPeriod]} mins) - consider additional training or workload adjustment</li>
                  <li>• <strong>Christine D.</strong> and <strong>Bless-Ann</strong> have quality scores below 90% - recommend focused coaching sessions</li>
                  <li>• Overall idle time of {teamData.totalIdleTime.toLocaleString()} minutes suggests potential for productivity optimization</li>
                  <li>• Break frequency varies significantly across team members - consider standardizing break schedules</li>
                </ul>
              </div>

              {/* Strategic Recommendations */}
              <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                <h3 className="font-semibold text-green-800 dark:text-green-200 mb-2 flex items-center">
                  <Award className="w-4 h-4 mr-2" />
                  Strategic Recommendations
                </h3>
                <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
                  <li>• Implement peer mentoring program pairing high performers (Ray, Rose, Christine G.) with developing team members</li>
                  <li>• Establish quality score targets of 95%+ for all team members within next quarter</li>
                  <li>• Introduce automated idle time alerts to reduce unproductive periods by 20%</li>
                  <li>• Consider workload redistribution to optimize audit completion rates across the team</li>
                  <li>• Schedule monthly performance review sessions to maintain momentum and address challenges early</li>
                </ul>
              </div>

              {/* Predictive Analysis */}
              <div className="bg-purple-50 dark:bg-purple-950/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
                <h3 className="font-semibold text-purple-800 dark:text-purple-200 mb-2 flex items-center">
                  <Brain className="w-4 h-4 mr-2" />
                  Predictive Analysis
                </h3>
                <ul className="text-sm text-purple-700 dark:text-purple-300 space-y-1">
                  <li>• Based on current trends, team is projected to complete {Math.round(teamData.totalAudits * 1.15).toLocaleString()} audits next {selectedPeriod}</li>
                  <li>• Quality scores show upward trajectory - expect 2-3% improvement with targeted interventions</li>
                  <li>• Idle time reduction initiatives could increase overall productivity by 15-25%</li>
                  <li>• Team performance indicates readiness for increased audit volume or complexity</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TeamPerformance;
