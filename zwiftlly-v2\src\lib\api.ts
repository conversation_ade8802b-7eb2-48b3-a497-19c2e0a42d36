import { supabase, type Database } from './supabase'

type Tables = Database['public']['Tables']
type User = Tables['users']['Row']
type Task = Tables['tasks']['Row']
type Announcement = Tables['announcements']['Row']
type Notification = Tables['notifications']['Row']
type AttendanceRecord = Tables['attendance_records']['Row']

// User API functions
export const userApi = {
  // Get current user profile
  async getCurrentUser(): Promise<User | null> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return null

    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single()

    if (error) {
      console.error('Error fetching user profile:', error)
      return null
    }

    return data
  },

  // Update user profile
  async updateProfile(updates: Tables['users']['Update']): Promise<{ success: boolean; error?: string }> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return { success: false, error: 'Not authenticated' }

    const { error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', user.id)

    if (error) {
      console.error('Error updating profile:', error)
      return { success: false, error: error.message }
    }

    return { success: true }
  },

  // Get users in organization
  async getOrganizationUsers(): Promise<User[]> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching organization users:', error)
      return []
    }

    return data || []
  }
}

// Task API functions
export const taskApi = {
  // Get all tasks
  async getTasks(): Promise<Task[]> {
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        assignee:users!tasks_assignee_id_fkey(id, full_name, email),
        creator:users!tasks_created_by_fkey(id, full_name, email)
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching tasks:', error)
      return []
    }

    return data || []
  },

  // Create new task
  async createTask(task: Tables['tasks']['Insert']): Promise<{ success: boolean; error?: string; data?: Task }> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return { success: false, error: 'Not authenticated' }

    const { data, error } = await supabase
      .from('tasks')
      .insert({
        ...task,
        created_by: user.id
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating task:', error)
      return { success: false, error: error.message }
    }

    return { success: true, data }
  },

  // Update task
  async updateTask(id: string, updates: Tables['tasks']['Update']): Promise<{ success: boolean; error?: string }> {
    const { error } = await supabase
      .from('tasks')
      .update(updates)
      .eq('id', id)

    if (error) {
      console.error('Error updating task:', error)
      return { success: false, error: error.message }
    }

    return { success: true }
  },

  // Delete task
  async deleteTask(id: string): Promise<{ success: boolean; error?: string }> {
    const { error } = await supabase
      .from('tasks')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting task:', error)
      return { success: false, error: error.message }
    }

    return { success: true }
  }
}

// Announcement API functions
export const announcementApi = {
  // Get all announcements
  async getAnnouncements(): Promise<Announcement[]> {
    const { data, error } = await supabase
      .from('announcements')
      .select(`
        *,
        author:users!announcements_author_id_fkey(id, full_name, email, avatar_url)
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching announcements:', error)
      return []
    }

    return data || []
  },

  // Create new announcement
  async createAnnouncement(announcement: Tables['announcements']['Insert']): Promise<{ success: boolean; error?: string; data?: Announcement }> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return { success: false, error: 'Not authenticated' }

    const { data, error } = await supabase
      .from('announcements')
      .insert({
        ...announcement,
        author_id: user.id
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating announcement:', error)
      return { success: false, error: error.message }
    }

    return { success: true, data }
  },

  // Like/unlike announcement
  async toggleLike(announcementId: string): Promise<{ success: boolean; error?: string }> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return { success: false, error: 'Not authenticated' }

    // Check if already liked
    const { data: existingLike } = await supabase
      .from('announcement_likes')
      .select('id')
      .eq('announcement_id', announcementId)
      .eq('user_id', user.id)
      .single()

    if (existingLike) {
      // Unlike
      const { error } = await supabase
        .from('announcement_likes')
        .delete()
        .eq('announcement_id', announcementId)
        .eq('user_id', user.id)

      if (error) {
        console.error('Error removing like:', error)
        return { success: false, error: error.message }
      }
    } else {
      // Like
      const { error } = await supabase
        .from('announcement_likes')
        .insert({
          announcement_id: announcementId,
          user_id: user.id
        })

      if (error) {
        console.error('Error adding like:', error)
        return { success: false, error: error.message }
      }
    }

    return { success: true }
  }
}

// Notification API functions
export const notificationApi = {
  // Get user notifications
  async getNotifications(): Promise<Notification[]> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return []

    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching notifications:', error)
      return []
    }

    return data || []
  },

  // Mark notification as read
  async markAsRead(id: string): Promise<{ success: boolean; error?: string }> {
    const { error } = await supabase
      .from('notifications')
      .update({ 
        is_read: true, 
        read_at: new Date().toISOString() 
      })
      .eq('id', id)

    if (error) {
      console.error('Error marking notification as read:', error)
      return { success: false, error: error.message }
    }

    return { success: true }
  },

  // Archive notification
  async archiveNotification(id: string): Promise<{ success: boolean; error?: string }> {
    const { error } = await supabase
      .from('notifications')
      .update({ 
        is_archived: true, 
        archived_at: new Date().toISOString() 
      })
      .eq('id', id)

    if (error) {
      console.error('Error archiving notification:', error)
      return { success: false, error: error.message }
    }

    return { success: true }
  }
}

// Attendance API functions
export const attendanceApi = {
  // Clock in/out
  async clockAction(status: 'CLOCKED_IN' | 'CLOCKED_OUT' | 'BREAK' | 'LUNCH', notes?: string): Promise<{ success: boolean; error?: string }> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return { success: false, error: 'Not authenticated' }

    const { error } = await supabase
      .from('attendance_records')
      .insert({
        user_id: user.id,
        status,
        notes,
        timestamp: new Date().toISOString()
      })

    if (error) {
      console.error('Error recording attendance:', error)
      return { success: false, error: error.message }
    }

    return { success: true }
  },

  // Get attendance records
  async getAttendanceRecords(userId?: string): Promise<AttendanceRecord[]> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return []

    const targetUserId = userId || user.id

    const { data, error } = await supabase
      .from('attendance_records')
      .select(`
        *,
        user:users!attendance_records_user_id_fkey(id, full_name, email)
      `)
      .eq('user_id', targetUserId)
      .order('timestamp', { ascending: false })

    if (error) {
      console.error('Error fetching attendance records:', error)
      return []
    }

    return data || []
  }
}

// Volume Tracking API functions
export const volumeApi = {
  // Get volume tracking data
  async getVolumeData(days: number = 7): Promise<any[]> {
    const { data, error } = await supabase
      .from('volume_tracking')
      .select(`
        *,
        user:users!volume_tracking_user_id_fkey(id, full_name, email)
      `)
      .gte('date', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0])
      .order('date', { ascending: false })

    if (error) {
      console.error('Error fetching volume data:', error)
      return []
    }

    return data || []
  },

  // Get aggregated volume data by user
  async getUserVolumeStats(): Promise<any[]> {
    const { data, error } = await supabase
      .from('volume_tracking')
      .select(`
        user_id,
        calls_count,
        sms_count,
        date,
        user:users!volume_tracking_user_id_fkey(id, full_name, email)
      `)
      .gte('date', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0])
      .order('calls_count', { ascending: false })

    if (error) {
      console.error('Error fetching user volume stats:', error)
      return []
    }

    // Group by user and sum the counts
    const userStats = data?.reduce((acc: any, record: any) => {
      const userId = record.user_id
      if (!acc[userId]) {
        acc[userId] = {
          user: record.user,
          totalCalls: 0,
          totalSms: 0,
          records: []
        }
      }
      acc[userId].totalCalls += record.calls_count || 0
      acc[userId].totalSms += record.sms_count || 0
      acc[userId].records.push(record)
      return acc
    }, {})

    return Object.values(userStats || {})
  }
}
