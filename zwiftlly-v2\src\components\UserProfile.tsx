import React, { useState, useEffect } from 'react';
import {
  User,
  Save,
  Camera
} from 'lucide-react';
import { useAuth } from '../AuthContext';
import { useCompany } from '../contexts/CompanyContext';

interface UserProfileProps {
  darkMode?: boolean;
}

const UserProfile: React.FC<UserProfileProps> = ({ darkMode = false }) => {
  const { user } = useAuth();
  const { users } = useCompany();
  
  const [isEditing, setIsEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    hireStartDate: '',
    avatar: ''
  });

  const [selectedAvatar, setSelectedAvatar] = useState('');
  const [showAvatarSelector, setShowAvatarSelector] = useState(false);

  const currentUserProfile = users.find(u => u.id === user?.id);

  // Default avatar options
  const defaultAvatars = [
    '👤', '👨‍💼', '👩‍💼', '👨‍💻', '👩‍💻', '👨‍🔧', '👩‍🔧', '👨‍🎓', '👩‍🎓', '👨‍⚕️', '👩‍⚕️', '👨‍🏫', '👩‍🏫', '👨‍🎨', '👩‍🎨'
  ];

  useEffect(() => {
    if (currentUserProfile) {
      const fullName = currentUserProfile.full_name || '';
      const nameParts = fullName.split(' ');
      setProfileData({
        firstName: nameParts[0] || '',
        lastName: nameParts.slice(1).join(' ') || '',
        email: currentUserProfile.email || '',
        phone: (currentUserProfile as any).phone || '',
        hireStartDate: (currentUserProfile as any).hire_start_date || '',
        avatar: currentUserProfile.avatar_url || ''
      });
      setSelectedAvatar(currentUserProfile.avatar_url || defaultAvatars[0]);
    }
  }, [currentUserProfile]);

  const handleSave = async () => {
    setSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to save profile:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setShowAvatarSelector(false);
    // Reset form data
    if (currentUserProfile) {
      const fullName = currentUserProfile.full_name || '';
      const nameParts = fullName.split(' ');
      setProfileData({
        firstName: nameParts[0] || '',
        lastName: nameParts.slice(1).join(' ') || '',
        email: currentUserProfile.email || '',
        phone: (currentUserProfile as any).phone || '',
        hireStartDate: (currentUserProfile as any).hire_start_date || '',
        avatar: currentUserProfile.avatar_url || ''
      });
      setSelectedAvatar(currentUserProfile.avatar_url || defaultAvatars[0]);
    }
  };



  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3">
            <User className="w-8 h-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold text-foreground">User Profile</h1>
              <p className="text-muted-foreground">Manage your personal information and profile settings</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Profile Picture Section */}
          <div className="bg-card border border-border rounded-lg p-6">
            <h2 className="text-xl font-semibold text-foreground mb-6">Profile Picture</h2>
            <div className="text-center">
              <p className="text-muted-foreground mb-4">Choose your avatar</p>

              {/* Current Avatar Display */}
              <div className="mb-6">
                <div className="w-32 h-32 mx-auto bg-muted rounded-full flex items-center justify-center text-4xl">
                  {selectedAvatar.startsWith('http') ? (
                    <img
                      src={selectedAvatar}
                      alt="Profile"
                      className="w-32 h-32 rounded-full object-cover"
                    />
                  ) : (
                    <span>{selectedAvatar}</span>
                  )}
                </div>
              </div>

              {/* Upload Custom Photo Button */}
              <button
                className="mb-4 px-4 py-2 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors"
                onClick={() => setShowAvatarSelector(!showAvatarSelector)}
              >
                <Camera className="w-4 h-4 inline mr-2" />
                Choose Photo
              </button>

              {/* Avatar Selector */}
              {showAvatarSelector && (
                <div className="mt-4">
                  <p className="text-sm text-muted-foreground mb-3">Or Choose Default Avatar</p>
                  <div className="grid grid-cols-5 gap-3 max-w-xs mx-auto">
                    {defaultAvatars.map((avatar, index) => (
                      <button
                        key={index}
                        onClick={() => {
                          setSelectedAvatar(avatar);
                          setProfileData(prev => ({ ...prev, avatar }));
                        }}
                        className={`w-12 h-12 rounded-full flex items-center justify-center text-xl border-2 transition-colors ${
                          selectedAvatar === avatar
                            ? 'border-primary bg-primary/10'
                            : 'border-border hover:border-primary/50'
                        }`}
                      >
                        {avatar}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Personal Information Section */}
          <div className="bg-card border border-border rounded-lg p-6">
            <h2 className="text-xl font-semibold text-foreground mb-6">Personal Information</h2>
            <p className="text-muted-foreground mb-6">Update your profile details</p>

            <div className="space-y-6">
              {/* First Name */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  First Name
                </label>
                <input
                  type="text"
                  value={profileData.firstName}
                  onChange={(e) => setProfileData(prev => ({ ...prev, firstName: e.target.value }))}
                  placeholder="Enter your first name"
                  className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>

              {/* Last Name */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Last Name
                </label>
                <input
                  type="text"
                  value={profileData.lastName}
                  onChange={(e) => setProfileData(prev => ({ ...prev, lastName: e.target.value }))}
                  placeholder="Enter your last name"
                  className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>

              {/* Work Email */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Work Email
                </label>
                <input
                  type="email"
                  value={profileData.email}
                  onChange={(e) => setProfileData(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="Enter your work email"
                  className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>

              {/* Phone Number */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={profileData.phone}
                  onChange={(e) => setProfileData(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="Enter your phone number"
                  className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>

              {/* Hire Start Date */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Hire Start Date
                </label>
                <input
                  type="date"
                  value={profileData.hireStartDate}
                  onChange={(e) => setProfileData(prev => ({ ...prev, hireStartDate: e.target.value }))}
                  className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>

              {/* Save Profile Button */}
              <div className="pt-4">
                <button
                  onClick={handleSave}
                  disabled={saving}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 transition-colors"
                >
                  <Save className="w-4 h-4" />
                  <span>{saving ? 'Saving Profile...' : 'Save Profile'}</span>
                </button>
              </div>
            </div>

        </div>
      </div>
    </div>
  );
};

export default UserProfile;
