# ZWIFTLLY Deployment Guide

This guide provides step-by-step instructions for deploying the ZWIFTLLY Team Management System to production environments.

## 🎯 Deployment Overview

### **Recommended Architecture**
- **Frontend**: Vercel (React/Vite application)
- **Backend**: Railway (Node.js/Express API)
- **Database**: Railway PostgreSQL or Supabase
- **Real-time**: Socket.IO (integrated with backend)
- **CDN**: Vercel Edge Network
- **Monitoring**: Built-in health checks + external monitoring

## 🔧 Prerequisites

### **Required Accounts & Services**
1. **GitHub Account** (for code repository)
2. **Vercel Account** (for frontend hosting)
3. **Railway Account** (for backend and database)
4. **Google Cloud Console** (for OAuth credentials)
5. **OpenWeatherMap Account** (optional, for weather features)

### **Required Credentials**
- Google OAuth Client ID and Secret
- Database connection string
- JWT secret key
- API keys for external services

## 🚀 Step-by-Step Deployment

### **Phase 1: Database Setup**

#### **Option A: Railway PostgreSQL**
1. **Create Railway Project**
   ```bash
   # Install Railway CLI
   npm install -g @railway/cli
   
   # Login to Railway
   railway login
   
   # Create new project
   railway init
   ```

2. **Add PostgreSQL Service**
   - Go to Railway dashboard
   - Click "Add Service" → "Database" → "PostgreSQL"
   - Note the connection string from the "Connect" tab

#### **Option B: Supabase PostgreSQL**
1. **Create Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create new project
   - Go to Settings → Database
   - Copy the connection string

### **Phase 2: Backend Deployment**

#### **Railway Backend Deployment**

1. **Prepare Backend for Deployment**
   ```bash
   cd zwiftlly-backend
   
   # Ensure all dependencies are listed
   npm install
   
   # Test build locally
   npm run build
   ```

2. **Deploy to Railway**
   ```bash
   # Link to Railway project
   railway link
   
   # Set environment variables
   railway variables set DATABASE_URL="your-postgresql-connection-string"
   railway variables set JWT_SECRET="your-super-secret-jwt-key-min-32-chars"
   railway variables set GOOGLE_CLIENT_ID="your-google-oauth-client-id"
   railway variables set NODE_ENV="production"
   railway variables set CORS_ORIGIN="https://your-frontend-domain.vercel.app"
   railway variables set FRONTEND_URL="https://your-frontend-domain.vercel.app"
   railway variables set ALLOWED_EMAILS="<EMAIL>"
   railway variables set WEATHER_API_KEY="your-openweathermap-api-key"
   
   # Deploy
   railway up
   ```

3. **Run Database Migrations**
   ```bash
   # After deployment, run migrations
   railway run npx prisma migrate deploy
   ```

4. **Verify Deployment**
   - Check Railway logs: `railway logs`
   - Test health endpoint: `https://your-backend-url.railway.app/health`

### **Phase 3: Frontend Deployment**

#### **Vercel Frontend Deployment**

1. **Prepare Frontend for Deployment**
   ```bash
   cd zwiftlly-frontend
   
   # Install dependencies
   npm install
   
   # Test build locally
   npm run build
   ```

2. **Deploy to Vercel**
   ```bash
   # Install Vercel CLI
   npm install -g vercel
   
   # Login to Vercel
   vercel login
   
   # Deploy
   vercel --prod
   ```

3. **Configure Environment Variables in Vercel**
   - Go to Vercel dashboard → Your project → Settings → Environment Variables
   - Add the following variables:
   ```
   VITE_GOOGLE_CLIENT_ID=your-google-oauth-client-id
   VITE_API_URL=https://your-backend-url.railway.app/api
   VITE_ALLOWED_EMAILS=<EMAIL>
   VITE_APP_URL=https://your-frontend-domain.vercel.app
   VITE_ENVIRONMENT=production
   ```

4. **Redeploy with Environment Variables**
   ```bash
   vercel --prod
   ```

### **Phase 4: Google OAuth Configuration**

1. **Google Cloud Console Setup**
   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Create new project or select existing
   - Enable Google+ API
   - Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"

2. **Configure OAuth Consent Screen**
   - Application name: "ZWIFTLLY Team Management"
   - User support email: <EMAIL>
   - Authorized domains: Add your Vercel domain
   - Developer contact: <EMAIL>

3. **Configure OAuth Client**
   - Application type: Web application
   - Name: ZWIFTLLY Production
   - Authorized JavaScript origins:
     - `https://your-frontend-domain.vercel.app`
   - Authorized redirect URIs:
     - `https://your-frontend-domain.vercel.app`

4. **Update Environment Variables**
   - Copy the Client ID to both frontend and backend environment variables
   - Redeploy both applications

### **Phase 5: Domain Configuration (Optional)**

#### **Custom Domain Setup**

1. **Frontend Custom Domain (Vercel)**
   - Go to Vercel dashboard → Your project → Settings → Domains
   - Add your custom domain (e.g., `app.yourcompany.com`)
   - Configure DNS records as instructed

2. **Backend Custom Domain (Railway)**
   - Go to Railway dashboard → Your service → Settings → Domains
   - Add custom domain (e.g., `api.yourcompany.com`)
   - Configure DNS records as instructed

3. **Update Environment Variables**
   - Update `CORS_ORIGIN` and `FRONTEND_URL` in backend
   - Update `VITE_API_URL` in frontend
   - Update Google OAuth authorized domains

## 🔒 Security Configuration

### **Production Security Checklist**

1. **Environment Variables**
   - ✅ All secrets stored in environment variables
   - ✅ No hardcoded credentials in code
   - ✅ Strong JWT secret (32+ characters)
   - ✅ Database credentials secured

2. **HTTPS Configuration**
   - ✅ HTTPS enforced on all domains
   - ✅ HSTS headers configured
   - ✅ Secure cookie settings

3. **CORS Configuration**
   - ✅ CORS origins restricted to your domains
   - ✅ No wildcard (*) origins in production

4. **Rate Limiting**
   - ✅ API rate limiting enabled
   - ✅ Authentication endpoints protected

5. **Database Security**
   - ✅ Database connection encrypted
   - ✅ Database access restricted
   - ✅ Regular backups configured

## 📊 Monitoring & Maintenance

### **Health Monitoring**

1. **Built-in Health Checks**
   - Backend: `https://your-backend-url/health`
   - Database connectivity check included

2. **External Monitoring Setup**
   - Use services like UptimeRobot, Pingdom, or DataDog
   - Monitor both frontend and backend endpoints
   - Set up alerts for downtime

3. **Log Monitoring**
   - Railway provides built-in logging
   - Set up log aggregation if needed
   - Monitor error rates and performance

### **Backup Strategy**

1. **Database Backups**
   - Railway PostgreSQL: Automatic daily backups
   - Supabase: Automatic backups included
   - Consider additional backup strategy for critical data

2. **Code Backups**
   - GitHub repository serves as code backup
   - Tag releases for easy rollback
   - Maintain deployment documentation

## 🚨 Troubleshooting

### **Common Issues**

1. **Database Connection Issues**
   ```bash
   # Check database connectivity
   railway run npx prisma db pull
   
   # Verify environment variables
   railway variables
   ```

2. **CORS Errors**
   - Verify `CORS_ORIGIN` matches your frontend domain exactly
   - Check for trailing slashes in URLs
   - Ensure HTTPS is used in production

3. **Authentication Issues**
   - Verify Google OAuth configuration
   - Check authorized domains in Google Console
   - Ensure `GOOGLE_CLIENT_ID` matches in both frontend and backend

4. **Build Failures**
   ```bash
   # Check build logs
   railway logs
   vercel logs
   
   # Test build locally
   npm run build
   ```

### **Rollback Procedure**

1. **Backend Rollback**
   ```bash
   # Rollback to previous deployment
   railway rollback
   ```

2. **Frontend Rollback**
   ```bash
   # Rollback to previous deployment
   vercel rollback
   ```

3. **Database Rollback**
   ```bash
   # Restore from backup (if needed)
   # Contact Railway/Supabase support for assistance
   ```

## 📞 Support

### **Getting Help**
- **Railway Support**: [railway.app/help](https://railway.app/help)
- **Vercel Support**: [vercel.com/support](https://vercel.com/support)
- **Google OAuth**: [developers.google.com/identity](https://developers.google.com/identity)

### **Emergency Contacts**
- System Administrator: <EMAIL>
- Technical Support: <EMAIL>
- Emergency Hotline: +1-XXX-XXX-XXXX

---

**🎉 Congratulations! Your ZWIFTLLY Team Management System is now live in production!**
