import React, { useState } from 'react';
import { 
  Clock, 
  Calendar, 
  Users, 
  TrendingUp, 
  Download, 
  Filter,
  Search,
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
  Square,
  Coffee,
  MapPin,
  Timer
} from 'lucide-react';
import { useCompany } from '../contexts/CompanyContext';
import { useAuth } from '../AuthContext';

interface AttendanceManagementProps {
  darkMode?: boolean;
}

const AttendanceManagement: React.FC<AttendanceManagementProps> = ({ darkMode = false }) => {
  const { users } = useCompany();
  const { user } = useAuth();
  
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  const [searchTerm, setSearchTerm] = useState('');

  // Mock attendance data
  const attendanceData = {
    summary: {
      totalHours: 168.5,
      averageHours: 8.4,
      attendanceRate: 94.2,
      lateArrivals: 3,
      earlyDepartures: 1
    },
    records: users.map((member, index) => ({
      id: member.id,
      name: member.full_name || 'Unknown User',
      avatar: member.avatar_url,
      status: ['present', 'late', 'absent', 'on-break'][Math.floor(Math.random() * 4)],
      clockIn: '09:00 AM',
      clockOut: '06:00 PM',
      breakTime: '1h 15m',
      totalHours: 7.75 + (Math.random() * 1.5),
      location: 'Office',
      overtime: Math.random() > 0.7 ? '2h 30m' : null
    })),
    timeEntries: [
      { time: '09:00 AM', action: 'Clock In', location: 'Office' },
      { time: '12:00 PM', action: 'Break Start', location: 'Office' },
      { time: '01:00 PM', action: 'Break End', location: 'Office' },
      { time: '06:00 PM', action: 'Clock Out', location: 'Office' }
    ]
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'late':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'absent':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'on-break':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present':
        return <Play className="w-4 h-4" />;
      case 'late':
        return <Clock className="w-4 h-4" />;
      case 'absent':
        return <Square className="w-4 h-4" />;
      case 'on-break':
        return <Coffee className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const filteredRecords = attendanceData.records.filter(record =>
    record.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Clock className="w-8 h-8 text-primary" />
              <div>
                <h1 className="text-3xl font-bold text-foreground">Attendance Management</h1>
                <p className="text-muted-foreground">Track time, monitor attendance, and manage schedules</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-1 bg-muted rounded-lg p-1">
                {(['daily', 'weekly', 'monthly'] as const).map((mode) => (
                  <button
                    key={mode}
                    onClick={() => setViewMode(mode)}
                    className={`px-3 py-1 text-sm rounded-md transition-colors ${
                      viewMode === mode
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:text-foreground'
                    }`}
                  >
                    {mode.charAt(0).toUpperCase() + mode.slice(1)}
                  </button>
                ))}
              </div>
              <button className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:opacity-90 transition-opacity">
                <Download className="w-4 h-4" />
                <span>Export</span>
              </button>
            </div>
          </div>
        </div>

        {/* Date Navigation */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button className="p-2 rounded-lg border border-border hover:bg-muted transition-colors">
                <ChevronLeft className="w-5 h-5" />
              </button>
              <div className="text-center">
                <h2 className="text-xl font-semibold text-foreground">{formatDate(selectedDate)}</h2>
                <p className="text-sm text-muted-foreground">
                  {viewMode.charAt(0).toUpperCase() + viewMode.slice(1)} View
                </p>
              </div>
              <button className="p-2 rounded-lg border border-border hover:bg-muted transition-colors">
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>
            <button className="flex items-center space-x-2 px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors">
              <Calendar className="w-4 h-4" />
              <span>Select Date</span>
            </button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <div className={`rounded-lg border p-6 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Total Hours
                </p>
                <p className="text-2xl font-bold text-foreground mt-1">
                  {attendanceData.summary.totalHours}h
                </p>
              </div>
              <Timer className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className={`rounded-lg border p-6 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Average Hours
                </p>
                <p className="text-2xl font-bold text-foreground mt-1">
                  {attendanceData.summary.averageHours}h
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div className={`rounded-lg border p-6 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Attendance Rate
                </p>
                <p className="text-2xl font-bold text-foreground mt-1">
                  {attendanceData.summary.attendanceRate}%
                </p>
              </div>
              <Users className="w-8 h-8 text-purple-500" />
            </div>
          </div>

          <div className={`rounded-lg border p-6 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Late Arrivals
                </p>
                <p className="text-2xl font-bold text-foreground mt-1">
                  {attendanceData.summary.lateArrivals}
                </p>
              </div>
              <Clock className="w-8 h-8 text-yellow-500" />
            </div>
          </div>

          <div className={`rounded-lg border p-6 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Early Departures
                </p>
                <p className="text-2xl font-bold text-foreground mt-1">
                  {attendanceData.summary.earlyDepartures}
                </p>
              </div>
              <Square className="w-8 h-8 text-red-500" />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Team Attendance */}
          <div className="lg:col-span-2">
            <div className={`rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-foreground">Team Attendance</h2>
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                      <input
                        type="text"
                        placeholder="Search team members..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className={`pl-10 pr-4 py-2 border rounded-lg text-sm ${
                          darkMode 
                            ? 'bg-gray-700 border-gray-600 text-white' 
                            : 'bg-white border-gray-300 text-gray-900'
                        }`}
                      />
                    </div>
                    <button className={`flex items-center space-x-2 px-3 py-2 border rounded-lg text-sm transition-colors ${
                      darkMode 
                        ? 'border-gray-600 text-gray-300 hover:bg-gray-700' 
                        : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}>
                      <Filter className="w-4 h-4" />
                      <span>Filter</span>
                    </button>
                  </div>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                    <tr>
                      <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                        darkMode ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        Team Member
                      </th>
                      <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                        darkMode ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        Status
                      </th>
                      <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                        darkMode ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        Clock In
                      </th>
                      <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                        darkMode ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        Clock Out
                      </th>
                      <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                        darkMode ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        Total Hours
                      </th>
                      <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                        darkMode ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        Location
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredRecords.map((record) => (
                      <tr key={record.id} className={`${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'} transition-colors`}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                              {record.avatar ? (
                                <img src={record.avatar} alt={record.name} className="w-10 h-10 rounded-full object-cover" />
                              ) : (
                                record.name.charAt(0).toUpperCase()
                              )}
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-foreground">{record.name}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            getStatusColor(record.status)
                          }`}>
                            {getStatusIcon(record.status)}
                            <span className="ml-1 capitalize">{record.status.replace('-', ' ')}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                          {record.clockIn}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                          {record.status === 'absent' ? '-' : record.clockOut}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                          {record.status === 'absent' ? '-' : `${record.totalHours.toFixed(2)}h`}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center text-sm text-foreground">
                            <MapPin className="w-4 h-4 mr-1 text-muted-foreground" />
                            {record.location}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* My Time Tracking */}
          <div className="lg:col-span-1">
            <div className={`rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold text-foreground">My Time Tracking</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {attendanceData.timeEntries.map((entry, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-2 h-2 rounded-full ${
                          entry.action.includes('In') || entry.action.includes('End') 
                            ? 'bg-green-500' 
                            : 'bg-red-500'
                        }`}></div>
                        <div>
                          <p className="text-sm font-medium text-foreground">{entry.action}</p>
                          <p className="text-xs text-muted-foreground">{entry.location}</p>
                        </div>
                      </div>
                      <span className="text-sm text-muted-foreground">{entry.time}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AttendanceManagement;
