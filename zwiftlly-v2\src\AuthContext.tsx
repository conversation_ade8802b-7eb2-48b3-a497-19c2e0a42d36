import React, { createContext, useContext, useState, useEffect } from 'react'
import type { ReactNode } from 'react'
import { supabase } from './lib/supabase'
import type { User as SupabaseUser, Session } from '@supabase/supabase-js'

// Simple user interface for now
interface User {
  id: string
  email: string
  name: string
  role: string
  picture?: string
}

interface AuthContextType {
  user: User | null
  session: Session | null
  signInWithGoogle: () => Promise<{ success: boolean; message: string }>
  signUpWithGoogle: () => Promise<{ success: boolean; message: string }>
  signInWithEmail: (email: string, password: string) => Promise<{ success: boolean; message: string }>
  signUpWithEmail: (email: string, password: string, name: string) => Promise<{ success: boolean; message: string }>
  signOut: () => Promise<void>
  isLoading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Get authorized domains and emails from environment variables
const AUTHORIZED_DOMAINS = process.env.REACT_APP_DEFAULT_ALLOWED_DOMAINS?.split(',') || ['netic.ai', 'zwiftlly.com']
const ALLOWED_EMAILS = process.env.REACT_APP_ALLOWED_EMAILS?.split(',') || ['<EMAIL>']

const validateDomain = (email: string): boolean => {
  const domain = email.split('@')[1]

  // Check if email is in the allowed emails list (for specific Gmail addresses)
  if (ALLOWED_EMAILS.includes(email)) {
    return true
  }

  // Check if domain is in authorized domains
  if (!domain || !AUTHORIZED_DOMAINS.includes(domain)) {
    return false
  }

  return true
}

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const handleUserSession = React.useCallback(async (authUser: SupabaseUser) => {
    try {
      // Validate domain
      if (!validateDomain(authUser.email!)) {
        await supabase.auth.signOut()
        throw new Error(`Domain not authorized. Only netic.ai domains are allowed.`)
      }

      // Create simple user object
      const simpleUser: User = {
        id: authUser.id,
        email: authUser.email!,
        name: authUser.user_metadata?.name || authUser.email!.split('@')[0],
        role: ALLOWED_EMAILS.includes(authUser.email!) ? 'SUPER_ADMIN' : 'AGENT',
        picture: authUser.user_metadata?.picture
      }

      setUser(simpleUser)
      setIsLoading(false)
    } catch (error) {
      console.error('Error handling user session:', error)
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      if (session?.user) {
        handleUserSession(session.user)
      } else {
        setIsLoading(false)
      }
    })

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.email)
      setSession(session)

      if (session?.user) {
        await handleUserSession(session.user)
      } else {
        setUser(null)
        setIsLoading(false)
      }
    })

    return () => subscription.unsubscribe()
  }, [handleUserSession])

  const signInWithGoogle = async (): Promise<{ success: boolean; message: string }> => {
    setIsLoading(true)
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}`,
          queryParams: {
            access_type: 'offline',
            prompt: 'select_account',
          },
        },
      })

      if (error) {
        setIsLoading(false)
        return { success: false, message: error.message }
      }

      return { success: true, message: 'Redirecting to Google...' }
    } catch (error: any) {
      setIsLoading(false)
      return { success: false, message: error.message || 'Sign-in failed' }
    }
  }

  const signUpWithGoogle = async (): Promise<{ success: boolean; message: string }> => {
    setIsLoading(true)
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}?signup=true`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      })

      if (error) {
        setIsLoading(false)
        return { success: false, message: error.message }
      }

      return { success: true, message: 'Redirecting to Google...' }
    } catch (error: any) {
      setIsLoading(false)
      return { success: false, message: error.message || 'Sign-up failed' }
    }
  }

  const signInWithEmail = async (email: string, password: string): Promise<{ success: boolean; message: string }> => {
    setIsLoading(true)
    try {
      if (!validateDomain(email)) {
        setIsLoading(false)
        return { success: false, message: 'Domain not authorized. Only netic.ai domains are allowed.' }
      }

      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        setIsLoading(false)
        return { success: false, message: error.message }
      }

      return { success: true, message: 'Sign-in successful' }
    } catch (error: any) {
      setIsLoading(false)
      return { success: false, message: error.message || 'Sign-in failed' }
    }
  }

  const signUpWithEmail = async (email: string, password: string, name: string): Promise<{ success: boolean; message: string }> => {
    setIsLoading(true)
    try {
      if (!validateDomain(email)) {
        setIsLoading(false)
        return { success: false, message: 'Domain not authorized. Only netic.ai domains are allowed.' }
      }

      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: name,
            first_name: name.split(' ')[0] || '',
            last_name: name.split(' ').slice(1).join(' ') || '',
          }
        }
      })

      if (error) {
        setIsLoading(false)
        return { success: false, message: error.message }
      }

      return { success: true, message: 'Sign-up successful. Please check your email for verification.' }
    } catch (error: any) {
      setIsLoading(false)
      return { success: false, message: error.message || 'Sign-up failed' }
    }
  }

  const signOut = async () => {
    try {
      await supabase.auth.signOut()
      setUser(null)
      setSession(null)
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  const value: AuthContextType = {
    user,
    session,
    signInWithGoogle,
    signUpWithGoogle,
    signInWithEmail,
    signUpWithEmail,
    signOut,
    isLoading,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
