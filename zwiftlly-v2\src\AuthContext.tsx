import React, { createContext, useContext, useState, useEffect } from 'react'
import type { ReactNode } from 'react'
import { supabase } from './lib/supabase'
import type { User as SupabaseUser, Session } from '@supabase/supabase-js'

// Simple user interface for now
interface User {
  id: string
  email: string
  name: string
  role: string
  picture?: string
}

interface AuthContextType {
  user: User | null
  session: Session | null
  signInWithGoogle: () => Promise<{ success: boolean; message: string }>
  signOut: () => Promise<void>
  isLoading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

const AUTHORIZED_DOMAINS = ['gmail.com', 'netic.ai', 'zwiftlly.com']
const ALLOWED_GMAIL = '<EMAIL>'

const validateDomain = (email: string): boolean => {
  const domain = email.split('@')[1]
  if (!domain || !AUTHORIZED_DOMAINS.includes(domain)) {
    return false
  }
  if (domain === 'gmail.com' && email !== ALLOWED_GMAIL) {
    return false
  }
  return true
}

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const handleUserSession = React.useCallback(async (authUser: SupabaseUser) => {
    try {
      // Validate domain
      if (!validateDomain(authUser.email!)) {
        await supabase.auth.signOut()
        throw new Error(`Domain not authorized. Only netic.ai domains are allowed.`)
      }

      // Create simple user object
      const simpleUser: User = {
        id: authUser.id,
        email: authUser.email!,
        name: authUser.user_metadata?.name || authUser.email!.split('@')[0],
        role: authUser.email === ALLOWED_GMAIL ? 'SUPER_ADMIN' : 'AGENT',
        picture: authUser.user_metadata?.picture
      }

      setUser(simpleUser)
      setIsLoading(false)
    } catch (error) {
      console.error('Error handling user session:', error)
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      if (session?.user) {
        handleUserSession(session.user)
      } else {
        setIsLoading(false)
      }
    })

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.email)
      setSession(session)

      if (session?.user) {
        await handleUserSession(session.user)
      } else {
        setUser(null)
        setIsLoading(false)
      }
    })

    return () => subscription.unsubscribe()
  }, [handleUserSession])

  const signInWithGoogle = async (): Promise<{ success: boolean; message: string }> => {
    setIsLoading(true)
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}`,
          queryParams: {
            access_type: 'offline',
            prompt: 'select_account',
          },
        },
      })

      if (error) {
        setIsLoading(false)
        return { success: false, message: error.message }
      }

      return { success: true, message: 'Redirecting to Google...' }
    } catch (error: any) {
      setIsLoading(false)
      return { success: false, message: error.message || 'Sign-in failed' }
    }
  }

  const signOut = async () => {
    try {
      await supabase.auth.signOut()
      setUser(null)
      setSession(null)
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  const value: AuthContextType = {
    user,
    session,
    signInWithGoogle,
    signOut,
    isLoading,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
