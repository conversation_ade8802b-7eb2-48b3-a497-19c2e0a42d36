import React, { useState, useRef, useEffect } from 'react';
import { Clock, Play, Pause, Square, RotateCcw, Timer, Coffee, User } from 'lucide-react';
import { useAuth } from '../AuthContext';
import { useData } from '../contexts/DataContext';

interface ClockSystemDropdownProps {
  darkMode?: boolean;
}

type ClockStatus = 'clocked-out' | 'clocked-in' | 'on-break' | 'lunch-break';

interface TimeEntry {
  id: string;
  type: 'clock-in' | 'clock-out' | 'break-start' | 'break-end' | 'lunch-start' | 'lunch-end';
  timestamp: string;
  note?: string;
}

const ClockSystemDropdown: React.FC<ClockSystemDropdownProps> = ({ darkMode = false }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentStatus, setCurrentStatus] = useState<ClockStatus>('clocked-out');
  const [currentTime, setCurrentTime] = useState(new Date());
  const [todayEntries, setTodayEntries] = useState<TimeEntry[]>([]);
  const [totalHours, setTotalHours] = useState('0:00');
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  const { user } = useAuth();

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Load user's current clock status from database
  useEffect(() => {
    const loadCurrentStatus = async () => {
      if (!user) return;

      try {
        // Fetch current clock status from API
        const { clockApi } = await import('../lib/api');
        const currentSession = await clockApi.getCurrentStatus(user.id);

        if (currentSession) {
          // Map database status to component status
          switch (currentSession.status) {
            case 'Working':
              setCurrentStatus('clocked-in');
              break;
            case 'Break':
              setCurrentStatus('on-break');
              break;
            case 'Lunch':
              setCurrentStatus('lunch-break');
              break;
            default:
              setCurrentStatus('clocked-out');
          }

          // Calculate total hours worked today
          if (currentSession.clock_in_time) {
            const clockInTime = new Date(currentSession.clock_in_time);
            const now = new Date();
            const diffMs = now.getTime() - clockInTime.getTime();
            const hours = Math.floor(diffMs / (1000 * 60 * 60));
            const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
            setTotalHours(`${hours}:${minutes.toString().padStart(2, '0')}`);
          }
        } else {
          // No active session, user is clocked out
          setCurrentStatus('clocked-out');
          setTotalHours('0:00');
        }

        setTodayEntries([]);
      } catch (error) {
        console.error('Error loading clock status:', error);
        // Default to clocked-out on error
        setCurrentStatus('clocked-out');
        setTotalHours('0:00');
      }
    };

    loadCurrentStatus();
  }, [user]);

  const handleClockAction = async (action: string, note?: string) => {
    try {
      if (!user) return;

      const timestamp = new Date().toISOString();
      const newEntry: TimeEntry = {
        id: Date.now().toString(),
        type: action as TimeEntry['type'],
        timestamp,
        note
      };

      // Update local state first for immediate UI feedback
      setTodayEntries(prev => [...prev, newEntry]);

      // Use the clock API for proper integration
      const { clockApi } = await import('../lib/api');

      switch (action) {
        case 'clock-in':
          setCurrentStatus('clocked-in');
          await clockApi.clockIn(user.id);
          break;
        case 'clock-out':
          setCurrentStatus('clocked-out');
          await clockApi.clockOut(user.id);
          break;
        case 'break-start':
          setCurrentStatus('on-break');
          await clockApi.updateStatus(user.id, 'Break');
          break;
        case 'break-end':
          setCurrentStatus('clocked-in');
          await clockApi.updateStatus(user.id, 'Working');
          break;
        case 'lunch-start':
          setCurrentStatus('lunch-break');
          await clockApi.updateStatus(user.id, 'Lunch');
          break;
        case 'lunch-end':
          setCurrentStatus('clocked-in');
          await clockApi.updateStatus(user.id, 'Working');
          break;
        default:
          return;
      }

      // Close dropdown after action
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to record time entry:', error);
      // Revert local state on error
      setTodayEntries(prev => prev.filter(entry => entry.id !== newEntry.id));
      // You might want to show an error message to the user here
    }
  };

  const getStatusColor = (status: ClockStatus) => {
    switch (status) {
      case 'clocked-in':
        return 'text-green-500';
      case 'on-break':
        return 'text-yellow-500';
      case 'lunch-break':
        return 'text-orange-500';
      case 'clocked-out':
      default:
        return darkMode ? 'text-gray-400' : 'text-gray-500';
    }
  };

  const getStatusText = (status: ClockStatus) => {
    switch (status) {
      case 'clocked-in':
        return 'Working';
      case 'on-break':
        return 'On Break';
      case 'lunch-break':
        return 'Lunch Break';
      case 'clocked-out':
      default:
        return 'Clocked Out';
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      timeZone: 'America/Los_Angeles', // San Francisco timezone
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
  };

  const formatEntryTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      timeZone: 'America/Los_Angeles', // San Francisco timezone
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Clock Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center justify-center p-2 rounded-lg transition-colors duration-200 ${
          darkMode
            ? 'hover:bg-gray-700 text-gray-300 hover:text-white'
            : 'hover:bg-gray-100 text-gray-600 hover:text-gray-900'
        }`}
        aria-label="Time Clock"
      >
        <Clock className="w-5 h-5" />
      </button>

      {/* Dropdown Panel */}
      {isOpen && (
        <div className={`absolute right-0 mt-2 w-80 rounded-lg shadow-lg border z-50 ${
          darkMode 
            ? 'bg-gray-800 border-gray-700' 
            : 'bg-white border-gray-200'
        }`}>
          {/* Header */}
          <div className={`p-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <h3 className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Time Clock
              </h3>
              <div className="text-right">
                <div className={`text-lg font-mono ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {formatTime(currentTime)}
                </div>
                <div className={`text-sm ${getStatusColor(currentStatus)}`}>
                  {getStatusText(currentStatus)}
                </div>
              </div>
            </div>
          </div>

          {/* Today's Summary */}
          <div className={`p-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Timer className={`w-4 h-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Today's Hours
                </span>
              </div>
              <span className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {totalHours}
              </span>
            </div>
          </div>

          {/* Clock Actions */}
          <div className="p-4 space-y-2">
            {currentStatus === 'clocked-out' && (
              <button
                onClick={() => handleClockAction('clock-in')}
                className="w-full flex items-center justify-center space-x-2 py-3 px-4 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              >
                <Play className="w-4 h-4" />
                <span>Clock In</span>
              </button>
            )}

            {currentStatus === 'clocked-in' && (
              <>
                <button
                  onClick={() => handleClockAction('break-start')}
                  className={`w-full flex items-center justify-center space-x-2 py-2 px-4 rounded-lg transition-colors ${
                    darkMode 
                      ? 'bg-yellow-600 hover:bg-yellow-700 text-white' 
                      : 'bg-yellow-500 hover:bg-yellow-600 text-white'
                  }`}
                >
                  <Coffee className="w-4 h-4" />
                  <span>Start Break</span>
                </button>
                
                <button
                  onClick={() => handleClockAction('lunch-start')}
                  className={`w-full flex items-center justify-center space-x-2 py-2 px-4 rounded-lg transition-colors ${
                    darkMode 
                      ? 'bg-orange-600 hover:bg-orange-700 text-white' 
                      : 'bg-orange-500 hover:bg-orange-600 text-white'
                  }`}
                >
                  <User className="w-4 h-4" />
                  <span>Start Lunch</span>
                </button>
                
                <button
                  onClick={() => handleClockAction('clock-out')}
                  className="w-full flex items-center justify-center space-x-2 py-2 px-4 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                >
                  <Square className="w-4 h-4" />
                  <span>Clock Out</span>
                </button>
              </>
            )}

            {(currentStatus === 'on-break' || currentStatus === 'lunch-break') && (
              <button
                onClick={() => handleClockAction(currentStatus === 'on-break' ? 'break-end' : 'lunch-end')}
                className="w-full flex items-center justify-center space-x-2 py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                <RotateCcw className="w-4 h-4" />
                <span>Return to Work</span>
              </button>
            )}
          </div>

          {/* Today's Entries */}
          {todayEntries.length > 0 && (
            <div className={`border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <div className="p-4">
                <h4 className={`text-sm font-medium mb-3 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Today's Activity
                </h4>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {todayEntries.slice(-5).reverse().map((entry) => (
                    <div key={entry.id} className="flex items-center justify-between text-sm">
                      <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {entry.type.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                      <span className={`font-mono ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        {formatEntryTime(entry.timestamp)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ClockSystemDropdown;
