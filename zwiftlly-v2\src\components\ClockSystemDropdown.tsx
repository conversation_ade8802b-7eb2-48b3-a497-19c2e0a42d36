import React, { useState, useRef, useEffect } from 'react';
import { Clock, Play, Pause, Square, RotateCcw, Timer, Coffee, User } from 'lucide-react';
import { useAuth } from '../AuthContext';
import { useData } from '../contexts/DataContext';

interface ClockSystemDropdownProps {
  darkMode?: boolean;
}

type ClockStatus = 'clocked-out' | 'clocked-in' | 'on-break' | 'lunch-break';

interface TimeEntry {
  id: string;
  type: 'clock-in' | 'clock-out' | 'break-start' | 'break-end' | 'lunch-start' | 'lunch-end';
  timestamp: string;
  note?: string;
}

const ClockSystemDropdown: React.FC<ClockSystemDropdownProps> = ({ darkMode = false }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentStatus, setCurrentStatus] = useState<ClockStatus>('clocked-out');
  const [currentTime, setCurrentTime] = useState(new Date());
  const [todayEntries, setTodayEntries] = useState<TimeEntry[]>([]);
  const [totalHours, setTotalHours] = useState('0:00');
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  const { user } = useAuth();

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Mock data for demonstration
  useEffect(() => {
    // Simulate loading today's entries
    const mockEntries: TimeEntry[] = [
      {
        id: '1',
        type: 'clock-in',
        timestamp: new Date().toISOString().split('T')[0] + 'T09:00:00Z',
        note: 'Started work'
      }
    ];
    setTodayEntries(mockEntries);
    setCurrentStatus('clocked-in');
    setTotalHours('3:45');
  }, []);

  const handleClockAction = async (action: string, note?: string) => {
    try {
      const timestamp = new Date().toISOString();
      const newEntry: TimeEntry = {
        id: Date.now().toString(),
        type: action as TimeEntry['type'],
        timestamp,
        note
      };

      // Update local state
      setTodayEntries(prev => [...prev, newEntry]);

      // Update status based on action
      switch (action) {
        case 'clock-in':
          setCurrentStatus('clocked-in');
          break;
        case 'clock-out':
          setCurrentStatus('clocked-out');
          break;
        case 'break-start':
          setCurrentStatus('on-break');
          break;
        case 'break-end':
          setCurrentStatus('clocked-in');
          break;
        case 'lunch-start':
          setCurrentStatus('lunch-break');
          break;
        case 'lunch-end':
          setCurrentStatus('clocked-in');
          break;
      }

      // In a real app, you would send this to your backend
      console.log('Clock action:', action, timestamp, note);
      
      // Close dropdown after action
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to record time entry:', error);
    }
  };

  const getStatusColor = (status: ClockStatus) => {
    switch (status) {
      case 'clocked-in':
        return 'text-green-500';
      case 'on-break':
        return 'text-yellow-500';
      case 'lunch-break':
        return 'text-orange-500';
      case 'clocked-out':
      default:
        return darkMode ? 'text-gray-400' : 'text-gray-500';
    }
  };

  const getStatusText = (status: ClockStatus) => {
    switch (status) {
      case 'clocked-in':
        return 'Working';
      case 'on-break':
        return 'On Break';
      case 'lunch-break':
        return 'Lunch Break';
      case 'clocked-out':
      default:
        return 'Clocked Out';
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
  };

  const formatEntryTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Clock Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors duration-200 ${
          darkMode 
            ? 'hover:bg-gray-700 text-gray-300 hover:text-white' 
            : 'hover:bg-gray-100 text-gray-600 hover:text-gray-900'
        }`}
        aria-label="Time Clock"
      >
        <Clock className="w-5 h-5" />
        <div className="hidden sm:block text-left">
          <div className={`text-sm font-medium ${getStatusColor(currentStatus)}`}>
            {getStatusText(currentStatus)}
          </div>
          <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            {formatTime(currentTime)}
          </div>
        </div>
      </button>

      {/* Dropdown Panel */}
      {isOpen && (
        <div className={`absolute right-0 mt-2 w-80 rounded-lg shadow-lg border z-50 ${
          darkMode 
            ? 'bg-gray-800 border-gray-700' 
            : 'bg-white border-gray-200'
        }`}>
          {/* Header */}
          <div className={`p-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <h3 className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Time Clock
              </h3>
              <div className="text-right">
                <div className={`text-lg font-mono ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {formatTime(currentTime)}
                </div>
                <div className={`text-sm ${getStatusColor(currentStatus)}`}>
                  {getStatusText(currentStatus)}
                </div>
              </div>
            </div>
          </div>

          {/* Today's Summary */}
          <div className={`p-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Timer className={`w-4 h-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Today's Hours
                </span>
              </div>
              <span className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {totalHours}
              </span>
            </div>
          </div>

          {/* Clock Actions */}
          <div className="p-4 space-y-2">
            {currentStatus === 'clocked-out' && (
              <button
                onClick={() => handleClockAction('clock-in')}
                className="w-full flex items-center justify-center space-x-2 py-3 px-4 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              >
                <Play className="w-4 h-4" />
                <span>Clock In</span>
              </button>
            )}

            {currentStatus === 'clocked-in' && (
              <>
                <button
                  onClick={() => handleClockAction('break-start')}
                  className={`w-full flex items-center justify-center space-x-2 py-2 px-4 rounded-lg transition-colors ${
                    darkMode 
                      ? 'bg-yellow-600 hover:bg-yellow-700 text-white' 
                      : 'bg-yellow-500 hover:bg-yellow-600 text-white'
                  }`}
                >
                  <Coffee className="w-4 h-4" />
                  <span>Start Break</span>
                </button>
                
                <button
                  onClick={() => handleClockAction('lunch-start')}
                  className={`w-full flex items-center justify-center space-x-2 py-2 px-4 rounded-lg transition-colors ${
                    darkMode 
                      ? 'bg-orange-600 hover:bg-orange-700 text-white' 
                      : 'bg-orange-500 hover:bg-orange-600 text-white'
                  }`}
                >
                  <User className="w-4 h-4" />
                  <span>Start Lunch</span>
                </button>
                
                <button
                  onClick={() => handleClockAction('clock-out')}
                  className="w-full flex items-center justify-center space-x-2 py-2 px-4 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                >
                  <Square className="w-4 h-4" />
                  <span>Clock Out</span>
                </button>
              </>
            )}

            {(currentStatus === 'on-break' || currentStatus === 'lunch-break') && (
              <button
                onClick={() => handleClockAction(currentStatus === 'on-break' ? 'break-end' : 'lunch-end')}
                className="w-full flex items-center justify-center space-x-2 py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                <RotateCcw className="w-4 h-4" />
                <span>Return to Work</span>
              </button>
            )}
          </div>

          {/* Today's Entries */}
          {todayEntries.length > 0 && (
            <div className={`border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <div className="p-4">
                <h4 className={`text-sm font-medium mb-3 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Today's Activity
                </h4>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {todayEntries.slice(-5).reverse().map((entry) => (
                    <div key={entry.id} className="flex items-center justify-between text-sm">
                      <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {entry.type.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                      <span className={`font-mono ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        {formatEntryTime(entry.timestamp)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ClockSystemDropdown;
