import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowLeft, Search, Eye, Download, FileText, Calendar, User, Plus } from 'lucide-react';

interface Document {
  id: string;
  title: string;
  module: string;
  views: number;
  lastUpdated: string;
  description: string;
  author: string;
  fileSize: string;
  tags: string[];
}

interface DocumentsPageProps {
  moduleId: string;
  moduleName: string;
  onBack: () => void;
}

const DocumentsPage: React.FC<DocumentsPageProps> = ({ moduleId, moduleName, onBack }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [documents, setDocuments] = useState<Document[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);

  // Mock documents data
  const mockDocuments: Document[] = [
    {
      id: '1',
      title: 'Tenant Onboarding Checklist',
      module: 'tenants',
      views: 1247,
      lastUpdated: '2024-01-15',
      description: 'Complete checklist for new tenant onboarding process including verification steps, documentation requirements, and setup procedures.',
      author: 'Sarah Johnson',
      fileSize: '2.4 MB',
      tags: ['onboarding', 'checklist', 'verification']
    },
    {
      id: '2',
      title: 'Tenant Communication Standards',
      module: 'tenants',
      views: 523,
      lastUpdated: '2024-01-08',
      description: 'Guidelines for professional communication with tenants including email templates, response times, and escalation procedures.',
      author: 'Mike Chen',
      fileSize: '1.8 MB',
      tags: ['communication', 'standards', 'templates']
    },
    {
      id: '3',
      title: 'Data Privacy Policy',
      module: 'policies',
      views: 892,
      lastUpdated: '2024-01-12',
      description: 'Comprehensive data privacy and protection guidelines covering GDPR compliance, data handling, and user rights.',
      author: 'Legal Team',
      fileSize: '3.2 MB',
      tags: ['privacy', 'gdpr', 'compliance']
    },
    {
      id: '4',
      title: 'Quality Assurance Guidelines',
      module: 'guidelines',
      views: 756,
      lastUpdated: '2024-01-10',
      description: 'Standards and procedures for quality assurance including testing protocols, review processes, and documentation standards.',
      author: 'QA Team',
      fileSize: '2.1 MB',
      tags: ['quality', 'testing', 'standards']
    },
    {
      id: '5',
      title: 'Change Management Process',
      module: 'process',
      views: 367,
      lastUpdated: '2023-12-28',
      description: 'Procedures for managing and implementing changes including approval workflows, impact assessment, and rollback procedures.',
      author: 'Operations Team',
      fileSize: '1.9 MB',
      tags: ['change', 'management', 'workflow']
    }
  ];

  useEffect(() => {
    // Filter documents by module
    const moduleDocuments = mockDocuments.filter(doc => doc.module === moduleId);
    setDocuments(moduleDocuments);
    setFilteredDocuments(moduleDocuments);
  }, [moduleId]);

  useEffect(() => {
    // Filter documents based on search query
    if (searchQuery.trim() === '') {
      setFilteredDocuments(documents);
    } else {
      const filtered = documents.filter(doc =>
        doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredDocuments(filtered);
    }
  }, [searchQuery, documents]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-6">
            <Button
              variant="ghost"
              onClick={onBack}
              className="flex items-center space-x-2 text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Knowledge Base</span>
            </Button>
          </div>
          
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-foreground mb-2">
                {moduleName} Documents
              </h1>
              <p className="text-muted-foreground">
                {filteredDocuments.length} document{filteredDocuments.length !== 1 ? 's' : ''} found
              </p>
            </div>

            {/* Add Document Button - Pure Black for Light Mode */}
            <Button
              onClick={() => alert(`Add document to ${moduleName} - Button is working!`)}
              variant="ghost"
              className="group relative inline-flex items-center justify-center w-10 h-10 rounded-full bg-black dark:bg-white border border-gray-900 dark:border-gray-200 hover:bg-gray-900 dark:hover:bg-gray-50 hover:border-primary/50 shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105"
              title={`Add document to ${moduleName}`}
            >
              <Plus className="w-4 h-4 text-white dark:text-black group-hover:text-white dark:group-hover:text-black group-hover:rotate-90 transition-all duration-200" />
            </Button>
          </div>

          {/* Search Bar */}
          <div className="max-w-md">
            <form onSubmit={handleSearch} className="relative">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  type="text"
                  placeholder="Search documents..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full"
                />
              </div>
            </form>
          </div>
        </div>

        {/* Documents Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredDocuments.map((doc, index) => (
            <Card
              key={doc.id}
              className="document-card cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-xl group relative overflow-hidden"
              style={{
                animationDelay: `${index * 100}ms`,
                animation: 'fadeInUp 0.6s ease-out forwards',
                opacity: 0
              }}
            >
              {/* Hover Background */}
              <div className="absolute inset-0 bg-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              
              <CardHeader className="relative">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <CardTitle className="text-lg font-semibold group-hover:text-primary transition-colors duration-300 line-clamp-2">
                      {doc.title}
                    </CardTitle>
                  </div>
                  <FileText className="w-5 h-5 text-muted-foreground group-hover:text-primary transition-colors duration-300" />
                </div>
                
                <p className="text-sm text-muted-foreground line-clamp-3 mb-4">
                  {doc.description}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap gap-1 mb-3">
                  {doc.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 text-xs bg-muted text-muted-foreground rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </CardHeader>

              <CardContent className="relative">
                {/* Document Info */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <User className="w-3 h-3" />
                    <span>{doc.author}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <Calendar className="w-3 h-3" />
                    <span>Updated {doc.lastUpdated}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <Eye className="w-3 h-3" />
                    <span>{doc.views} views</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">{doc.fileSize}</span>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  >
                    <Download className="w-3 h-3 mr-1" />
                    Download
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredDocuments.length === 0 && (
          <div className="text-center py-20">
            <FileText className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-foreground mb-2">No documents found</h3>
            <p className="text-muted-foreground">
              {searchQuery ? 'Try adjusting your search terms' : 'No documents available in this module'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentsPage;
