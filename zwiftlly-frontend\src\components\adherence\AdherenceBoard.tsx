import React, { useState, useEffect } from 'react';
import StatusLegend from './StatusLegend';
import RightSidePanels from './RightSidePanels';
import WeeklySchedule from './WeeklySchedule';

interface TeamMember {
  id: string;
  name: string;
  avatar?: string;
  status: 'online' | 'technical' | 'offline' | 'break' | 'meeting' | 'scheduled' | 'absent';
  clockedIn: boolean;
  clockInTime?: string;
}

interface TimeBlock {
  startTime: number;
  endTime: number;
  status: 'online' | 'technical' | 'offline' | 'break' | 'meeting' | 'scheduled' | 'absent';
  activity?: string;
  project?: string;
}

const AdherenceBoard: React.FC = () => {
  const [format24Hour, setFormat24Hour] = useState(false);
  const [currentPacificTime, setCurrentPacificTime] = useState(new Date());

  const startHour = 0;
  const endHour = 24;

  // Default timezone: Pacific Time (San Francisco)
  const PACIFIC_TIMEZONE = 'America/Los_Angeles';

  // Refs for scroll synchronization
  const leftColumnRef = React.useRef<HTMLDivElement>(null);
  const timelineRef = React.useRef<HTMLDivElement>(null);
  const timeHeaderRef = React.useRef<HTMLDivElement>(null);

  // Update Pacific time every minute
  useEffect(() => {
    const updatePacificTime = () => {
      const now = new Date();
      const pacificTime = new Date(now.toLocaleString("en-US", {timeZone: PACIFIC_TIMEZONE}));
      setCurrentPacificTime(pacificTime);
    };

    updatePacificTime(); // Initial update
    const interval = setInterval(updatePacificTime, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  // Auto-scroll to current time on component mount
  useEffect(() => {
    const scrollToCurrentTime = () => {
      if (timelineRef.current) {
        const currentHour = currentPacificTime.getHours();
        const currentMinutes = currentPacificTime.getMinutes();
        const currentTimePosition = currentHour + currentMinutes / 60;

        // Calculate scroll position to center the current time
        const totalWidth = (endHour - startHour) * 100; // 100px per hour
        const currentTimePixel = ((currentTimePosition - startHour) / (endHour - startHour)) * totalWidth;
        const containerWidth = timelineRef.current.clientWidth;

        // Center the current time in the view
        const scrollLeft = Math.max(0, currentTimePixel - containerWidth / 2);

        timelineRef.current.scrollLeft = scrollLeft;

        // Sync with time header
        if (timeHeaderRef.current) {
          timeHeaderRef.current.scrollLeft = scrollLeft;
        }
      }
    };

    // Delay scroll to ensure component is fully rendered
    const timer = setTimeout(scrollToCurrentTime, 500);
    return () => clearTimeout(timer);
  }, [currentPacificTime, endHour, startHour]);

  // Scroll synchronization
  const handleLeftColumnScroll = () => {
    // Sync timeline scroll with left column - prevent infinite loop
    if (leftColumnRef.current && timelineRef.current && timelineRef.current.scrollTop !== leftColumnRef.current.scrollTop) {
      timelineRef.current.scrollTop = leftColumnRef.current.scrollTop;
    }
  };

  const handleTimelineScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;

    // Sync vertical scroll with left column (team members) - prevent infinite loop
    if (leftColumnRef.current && timelineRef.current && leftColumnRef.current.scrollTop !== timelineRef.current.scrollTop) {
      leftColumnRef.current.scrollTop = timelineRef.current.scrollTop;
    }

    // Sync horizontal scroll with time header (programmatically)
    if (timeHeaderRef.current && timelineRef.current) {
      timeHeaderRef.current.scrollLeft = timelineRef.current.scrollLeft;
    }
  };

  const handleTimeHeaderScroll = () => {
    // This function is kept for compatibility but time header scroll is hidden
    if (timeHeaderRef.current && timelineRef.current) {
      timelineRef.current.scrollLeft = timeHeaderRef.current.scrollLeft;
    }
  };

  // Removed mouse hover functionality to fix timeline hover bug

  // Mock data for testing
  const teamMembers: TeamMember[] = [
    {
      id: '1',
      name: 'Louise',
      status: 'online',
      clockedIn: true,
      clockInTime: '8:00 AM'
    },
    {
      id: '2',
      name: 'Rose',
      status: 'break',
      clockedIn: true,
      clockInTime: '8:15 AM'
    },
    {
      id: '3',
      name: 'Bless-Ann',
      status: 'technical',
      clockedIn: true,
      clockInTime: '8:30 AM'
    },
    {
      id: '4',
      name: 'Pearl',
      status: 'scheduled',
      clockedIn: false,
      clockInTime: ''
    },
    {
      id: '5',
      name: 'Christine G.',
      status: 'absent',
      clockedIn: false,
      clockInTime: ''
    },
    {
      id: '6',
      name: 'Ray',
      status: 'online',
      clockedIn: true,
      clockInTime: '6:00 AM'
    },
    {
      id: '7',
      name: 'Christine D.',
      status: 'online',
      clockedIn: true,
      clockInTime: '8:00 AM'
    },
    {
      id: '8',
      name: 'Gizelle',
      status: 'scheduled',
      clockedIn: false,
      clockInTime: ''
    },
    {
      id: '9',
      name: 'Dustin',
      status: 'online',
      clockedIn: true,
      clockInTime: '8:00 AM'
    }
  ];

  // Mock time blocks for each member
  const getTimeBlocks = (memberId: string): TimeBlock[] => {
    switch (memberId) {
      case '1': // Louise
        return [
          { startTime: 8, endTime: 10, status: 'online', activity: 'Auditing', project: 'Paschal' },
          { startTime: 10, endTime: 10.5, status: 'break', activity: 'Coffee Break' },
          { startTime: 10.5, endTime: 12, status: 'online', activity: 'Auditing', project: 'Vredevoogd' },
          { startTime: 12, endTime: 13, status: 'meeting', activity: 'Meeting' },
          { startTime: 13, endTime: 15, status: 'online', activity: 'Auditing', project: 'Classic' },
          { startTime: 15, endTime: 17, status: 'meeting', activity: 'Meeting' }
        ];
      case '2': // Rose
        return [
          { startTime: 8, endTime: 9, status: 'online', activity: 'Setup', project: 'Hurlburt' },
          { startTime: 9, endTime: 11, status: 'online', activity: 'Auditing', project: 'Hoffmann' },
          { startTime: 11, endTime: 11.5, status: 'break', activity: 'Break' },
          { startTime: 11.5, endTime: 12, status: 'technical', activity: 'System Issue' },
          { startTime: 12, endTime: 12.5, status: 'meeting', activity: 'Meeting' },
          { startTime: 12.5, endTime: 16, status: 'online', activity: 'Auditing', project: 'Randazzo' }
        ];
      case '3': // Bless-Ann
        return [
          { startTime: 8, endTime: 9.5, status: 'technical', activity: 'System Setup' },
          { startTime: 9.5, endTime: 12, status: 'online', activity: 'Auditing', project: 'Flame Fox & Sons' },
          { startTime: 12, endTime: 13, status: 'meeting', activity: 'Meeting' },
          { startTime: 13, endTime: 15, status: 'online', activity: 'Auditing', project: 'A1' },
          { startTime: 15, endTime: 15.5, status: 'break', activity: 'Break' },
          { startTime: 15.5, endTime: 17, status: 'online', activity: 'Auditing', project: 'Mr. Sparky' }
        ];
      case '4': // Pearl - Scheduled but not logged in
        return [
          { startTime: 8, endTime: 17, status: 'scheduled', activity: 'Scheduled Shift' }
        ];
      case '5': // Christine G. - Absent
        return [
          { startTime: 7, endTime: 16, status: 'absent', activity: 'Absent' }
        ];
      case '6': // Ray
        return [
          { startTime: 6, endTime: 8, status: 'online', activity: 'Auditing', project: 'Hoffmann' },
          { startTime: 8, endTime: 8.5, status: 'break', activity: 'Break' },
          { startTime: 8.5, endTime: 10, status: 'online', activity: 'Auditing', project: 'Randazzo' },
          { startTime: 10, endTime: 11, status: 'break', activity: 'Break' },
          { startTime: 11, endTime: 12, status: 'meeting', activity: 'Meeting' },
          { startTime: 12, endTime: 15, status: 'online', activity: 'Training', project: 'A1' }
        ];
      case '7': // Christine D.
        return [
          { startTime: 8, endTime: 10, status: 'online', activity: 'Auditing', project: 'Vredevoogd' },
          { startTime: 10, endTime: 10.5, status: 'break', activity: 'Break' },
          { startTime: 10.5, endTime: 12, status: 'online', activity: 'Auditing', project: 'Classic' },
          { startTime: 12, endTime: 13, status: 'meeting', activity: 'Meeting' },
          { startTime: 13, endTime: 15, status: 'online', activity: 'Auditing', project: 'Hurlburt' },
          { startTime: 15, endTime: 17, status: 'online', activity: 'Training' }
        ];
      case '8': // Gizelle - Scheduled but not logged in
        return [
          { startTime: 11, endTime: 20, status: 'scheduled', activity: 'Scheduled Shift' }
        ];
      case '9': // Dustin
        return [
          { startTime: 8, endTime: 10, status: 'online', activity: 'Auditing', project: 'Blue Sky' },
          { startTime: 10, endTime: 10.5, status: 'break', activity: 'Break' },
          { startTime: 10.5, endTime: 12, status: 'online', activity: 'Auditing', project: 'Freguson' },
          { startTime: 12, endTime: 13, status: 'meeting', activity: 'Meeting' },
          { startTime: 13, endTime: 16, status: 'online', activity: 'Auditing', project: 'Paschal' }
        ];
      default:
        return [];
    }
  };

  return (
    <div className="space-y-6">
      {/* Status Legend */}
      <StatusLegend />

      {/* Main Layout: Full Width Adherence Board */}
      <div className="space-y-4">
        {/* Combined Adherence Board */}
        <div className="bg-card rounded-xl shadow-lg border border-border/50 overflow-hidden animate-fade-in-up animate-delay-0">
          {/* Header with current date */}
          <div className="p-6 border-b border-border/50 bg-gradient-to-r from-background to-muted/20">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold text-foreground mb-1">Live Adherence Board</h3>
                <p className="text-sm text-muted-foreground">Real-time team monitoring • Pacific Time</p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <div className="text-sm font-medium text-foreground">
                    {currentPacificTime.toLocaleDateString('en-US', {
                      weekday: 'long',
                      month: 'short',
                      day: 'numeric',
                      timeZone: PACIFIC_TIMEZONE
                    })}
                  </div>
                  <div className="text-xs text-muted-foreground">Pacific Time</div>
                </div>
                <button
                  onClick={() => setFormat24Hour(!format24Hour)}
                  className="px-3 py-2 text-xs font-medium bg-primary/10 hover:bg-primary/20 text-primary rounded-lg border border-primary/20 transition-all duration-200 hover:scale-105"
                >
                  {format24Hour ? '24H' : '12H'}
                </button>
              </div>
            </div>
          </div>

          {/* Combined Time Header and Team Rows - Frozen Panes Layout */}
          <div className="flex bg-background" style={{ height: '350px' }}>
            {/* Fixed Left Column - Team Members (No Scroll) */}
            <div className="w-48 flex-shrink-0 bg-card border-r border-border flex flex-col" style={{ height: '350px' }}>
              {/* Team Members Header - Fixed */}
              <div className="p-2 border-b border-border bg-muted/20 h-12 flex items-center">
                <h4 className="text-xs font-bold text-foreground uppercase tracking-wide">Team Members</h4>
              </div>

              {/* Team Member Names - Scrolls Vertically with Timeline */}
              <div
                ref={leftColumnRef}
                className="flex-1 overflow-y-auto overflow-x-hidden"
                onScroll={handleLeftColumnScroll}
                style={{
                  scrollbarWidth: 'none',
                  msOverflowStyle: 'none'
                }}
              >
                {teamMembers.map((member, index) => (
                  <div key={member.id} className="p-2 border-b border-border/50 flex items-center space-x-2 h-12 hover:bg-muted/20 transition-all duration-200">
                    <div className="w-6 h-6 bg-muted rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-foreground">
                        {member.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-xs font-medium text-foreground truncate">
                        {member.name}
                      </div>
                    </div>
                  </div>
                ))}
                {/* Spacer to account for timeline's horizontal scrollbar */}
                <div style={{ height: '17px', flexShrink: 0 }}></div>
              </div>
            </div>

            {/* Right Side - Timeline with Frozen Header */}
            <div className="flex-1 flex flex-col min-w-0 bg-background" style={{ height: '350px' }}>
              {/* Time Header - Fixed (No Scroll) */}
              <div className="flex-shrink-0 bg-muted/10 border-b border-border h-12 flex items-center overflow-hidden">
                <div
                  ref={timeHeaderRef}
                  className="overflow-x-hidden"
                  style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
                >
                  <div className="flex" style={{ minWidth: `${(endHour - startHour) * 100}px` }}>
                    {Array.from({ length: endHour - startHour }, (_, i) => {
                      const hour = startHour + i;
                      const formatTime = (hour: number) => {
                        if (format24Hour) {
                          if (hour === 24) return '00:00';
                          return `${hour.toString().padStart(2, '0')}:00`;
                        } else {
                          if (hour === 0 || hour === 24) return '12AM';
                          if (hour === 12) return '12PM';
                          if (hour < 12) return `${hour}AM`;
                          return `${hour - 12}PM`;
                        }
                      };

                      const isCurrentHour = hour === currentPacificTime.getHours();

                      return (
                        <div
                          key={hour}
                          className={`py-2 px-1 text-center border-r border-border/30 last:border-r-0 flex-shrink-0 transition-all duration-200 ${
                            isCurrentHour
                              ? 'bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800'
                              : 'bg-background hover:bg-muted/20'
                          }`}
                          style={{ minWidth: '100px' }}
                        >
                          <span className={`text-xs font-medium ${
                            isCurrentHour
                              ? 'text-blue-600 dark:text-blue-400'
                              : 'text-foreground'
                          }`}>
                            {formatTime(hour)}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>

              {/* Team Timeline Rows - ONLY This Area Scrolls */}
              <div
                ref={timelineRef}
                className="flex-1 bg-background overflow-auto"
                onScroll={handleTimelineScroll}
                style={{
                  scrollbarWidth: 'thin',
                  scrollbarColor: 'rgba(156, 163, 175, 0.7) transparent'
                }}
              >
                <div style={{ minWidth: `${(endHour - startHour) * 100}px` }}>
                  {teamMembers.map((member, memberIndex) => (
                    <div key={member.id} className="flex h-12 border-b border-border/50 hover:bg-muted/10 transition-all duration-200 relative" style={{ minWidth: `${(endHour - startHour) * 100}px` }}>
                      {/* Timeline area */}
                      <div className="relative bg-background" style={{ width: `${(endHour - startHour) * 100}px` }}>
                      {/* Time blocks */}
                      {getTimeBlocks(member.id).map((block, index) => {
                        const startPercent = ((block.startTime - startHour) / (endHour - startHour)) * 100;
                        const widthPercent = ((block.endTime - block.startTime) / (endHour - startHour)) * 100;

                        // Professional, clean styling
                        const getBlockStyle = (status: string) => {
                          switch (status) {
                            case 'online':
                              return {
                                backgroundColor: '#10b981',
                                border: '2px solid #059669',
                                color: 'white'
                              };
                            case 'technical':
                              return {
                                backgroundColor: '#3b82f6',
                                border: '2px solid #2563eb',
                                color: 'white',
                                backgroundImage: 'repeating-linear-gradient(45deg, transparent, transparent 4px, rgba(255,255,255,0.1) 4px, rgba(255,255,255,0.1) 8px)'
                              };
                            case 'offline':
                              return {
                                backgroundColor: '#8b5cf6',
                                border: '2px solid #7c3aed',
                                color: 'white'
                              };
                            case 'break':
                              return {
                                backgroundColor: '#f59e0b',
                                border: '2px solid #d97706',
                                color: 'white',
                                backgroundImage: 'repeating-linear-gradient(0deg, transparent, transparent 4px, rgba(255,255,255,0.1) 4px, rgba(255,255,255,0.1) 8px)'
                              };
                            case 'meeting':
                              return {
                                backgroundColor: '#ef4444',
                                border: '2px solid #dc2626',
                                color: 'white',
                                backgroundImage: 'repeating-linear-gradient(90deg, transparent, transparent 4px, rgba(255,255,255,0.1) 4px, rgba(255,255,255,0.1) 8px)'
                              };
                            case 'scheduled':
                              return {
                                backgroundColor: '#6b7280',
                                border: '2px solid #4b5563',
                                color: 'white'
                              };
                            case 'absent':
                              return {
                                backgroundColor: '#dc2626',
                                border: '2px solid #b91c1c',
                                color: 'white'
                              };
                            default:
                              return {
                                backgroundColor: '#6b7280',
                                border: '2px solid #4b5563',
                                color: 'white'
                              };
                          }
                        };

                        return (
                          <div
                            key={index}
                            className="absolute top-1/2 transform -translate-y-1/2 h-10 rounded transition-all duration-200"
                            style={{
                              left: `${startPercent}%`,
                              width: `${widthPercent}%`,
                              ...getBlockStyle(block.status)
                            }}
                          >
                            {/* Activity label inside block */}
                            <div className="absolute inset-0 flex items-center justify-center px-2">
                              {block.activity && (block.endTime - block.startTime) >= 2 ? (
                                <span className="text-xs font-medium truncate text-white">
                                  {block.activity}
                                </span>
                              ) : block.activity && (block.endTime - block.startTime) >= 1 ? (
                                <span className="text-xs font-medium truncate text-white">
                                  {block.activity.substring(0, 6)}
                                </span>
                              ) : (
                                <div className="w-2 h-2 bg-white/90 rounded-full"></div>
                              )}
                            </div>


                          </div>
                        );
                      })}

                      {/* Hour grid lines */}
                      {Array.from({ length: endHour - startHour + 1 }, (_, i) => (
                        <div
                          key={i}
                          className={`absolute top-0 bottom-0 border-l ${
                            i % 2 === 0 ? 'border-border/30' : 'border-border/10'
                          }`}
                          style={{ left: `${(i / (endHour - startHour)) * 100}%` }}
                        />
                      ))}

                      {/* Live time indicator - thick red line with no dots */}
                      <div
                        className="absolute top-0 bottom-0 w-1 bg-red-500 z-20"
                        style={{
                          left: `${((currentPacificTime.getHours() + currentPacificTime.getMinutes() / 60 - startHour) / (endHour - startHour)) * 100}%`
                        }}
                      />

                      {/* Mouse hover functionality removed to fix timeline hover bug */}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Sections - Full Width */}
        <div className="space-y-4">
          <div className="animate-slide-in-right animate-delay-300">
            <WeeklySchedule />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdherenceBoard;
