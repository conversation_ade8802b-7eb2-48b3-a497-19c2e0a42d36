import React, { useState } from 'react';
import { 
  ArrowLeft, 
  Download, 
  Share2, 
  Star, 
  Edit, 
  Trash2, 
  Eye, 
  Calendar, 
  User, 
  Tag, 
  FileText,
  Video,
  Image,
  Link,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Bookmark,
  MoreVertical
} from 'lucide-react';

interface DocumentViewerProps {
  documentId: string;
  onBack: () => void;
  darkMode?: boolean;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({ documentId, onBack, darkMode = false }) => {
  const [isStarred, setIsStarred] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [showComments, setShowComments] = useState(false);
  const [newComment, setNewComment] = useState('');

  // Mock document data
  const document = {
    id: documentId,
    title: 'Employee Handbook 2025',
    description: 'Complete guide to company policies, benefits, and procedures for all team members',
    type: 'document',
    category: 'policies',
    author: 'HR Department',
    authorAvatar: null,
    createdAt: '2025-01-15',
    updatedAt: '2025-01-20',
    views: 156,
    likes: 23,
    dislikes: 2,
    starred: isStarred,
    bookmarked: isBookmarked,
    tags: ['HR', 'Policies', 'Handbook', 'Guidelines', 'Benefits'],
    fileSize: '2.4 MB',
    version: '1.2',
    content: `
# Employee Handbook 2025

## Welcome to ZWIFTLLY

Welcome to our team! This handbook contains important information about our company policies, procedures, and benefits.

## Table of Contents

1. Company Overview
2. Employment Policies
3. Benefits and Compensation
4. Code of Conduct
5. Safety Guidelines
6. IT Policies
7. Leave Policies
8. Performance Management

## 1. Company Overview

ZWIFTLLY is a leading team management platform dedicated to improving workplace efficiency and collaboration. Our mission is to empower teams with the tools they need to succeed.

### Our Values
- **Innovation**: We constantly seek new ways to improve
- **Collaboration**: We work together to achieve common goals
- **Integrity**: We act with honesty and transparency
- **Excellence**: We strive for the highest quality in everything we do

## 2. Employment Policies

### Equal Opportunity
We are committed to providing equal employment opportunities to all employees and applicants regardless of race, color, religion, gender, sexual orientation, age, national origin, disability, or veteran status.

### Work Hours
Standard work hours are Monday through Friday, 9:00 AM to 6:00 PM. Flexible work arrangements may be available based on role requirements and manager approval.

### Remote Work Policy
We support hybrid and remote work arrangements. Please coordinate with your manager to establish a schedule that works for both you and your team.

## 3. Benefits and Compensation

### Health Insurance
We provide comprehensive health insurance coverage including medical, dental, and vision plans.

### Retirement Plan
We offer a 401(k) retirement plan with company matching up to 4% of your salary.

### Paid Time Off
- Vacation: 15 days per year (increasing with tenure)
- Sick Leave: 10 days per year
- Personal Days: 3 days per year
- Holidays: All major holidays plus floating holidays

### Professional Development
We encourage continuous learning and provide:
- Annual training budget of $2,000 per employee
- Conference attendance opportunities
- Internal training programs
- Mentorship programs

## 4. Code of Conduct

All employees are expected to:
- Treat colleagues with respect and professionalism
- Maintain confidentiality of company and client information
- Follow all safety protocols and procedures
- Report any violations of company policies
- Represent the company positively in all interactions

## 5. Safety Guidelines

Your safety is our priority. Please:
- Report any safety hazards immediately
- Follow all emergency procedures
- Use proper ergonomics at your workstation
- Take regular breaks to prevent fatigue

## 6. IT Policies

### Equipment
Company-provided equipment must be used responsibly and returned upon termination.

### Security
- Use strong passwords and enable two-factor authentication
- Do not share login credentials
- Report security incidents immediately
- Follow data protection guidelines

### Acceptable Use
Company technology resources should be used primarily for business purposes. Limited personal use is acceptable but should not interfere with work responsibilities.

## 7. Leave Policies

### Family and Medical Leave
We comply with all applicable family and medical leave laws and provide additional benefits where possible.

### Bereavement Leave
Up to 5 days of paid bereavement leave is available for immediate family members.

### Jury Duty
Time off for jury duty is provided with pay continuation.

## 8. Performance Management

### Reviews
Performance reviews are conducted annually, with mid-year check-ins to ensure you're on track to meet your goals.

### Goal Setting
Work with your manager to set SMART goals that align with company objectives.

### Career Development
We're committed to helping you grow in your career through:
- Regular feedback and coaching
- Skill development opportunities
- Internal mobility programs
- Leadership development tracks

## Questions?

If you have any questions about this handbook or company policies, please contact:
- Your direct manager
- HR Department: <EMAIL>
- Employee Relations: <EMAIL>

---

*This handbook is updated regularly. Please check for the latest version on our knowledge base.*

**Last Updated**: January 20, 2025  
**Version**: 1.2
    `
  };

  const comments = [
    {
      id: '1',
      author: 'Sarah Johnson',
      avatar: null,
      content: 'This is really comprehensive! Thanks for putting this together.',
      timestamp: '2025-01-22 10:30 AM',
      likes: 5
    },
    {
      id: '2',
      author: 'Mike Chen',
      avatar: null,
      content: 'Could we add more details about the remote work policy? Specifically around equipment setup.',
      timestamp: '2025-01-21 2:15 PM',
      likes: 3
    },
    {
      id: '3',
      author: 'Emily Davis',
      avatar: null,
      content: 'The benefits section is very clear. Love the professional development budget!',
      timestamp: '2025-01-20 4:45 PM',
      likes: 7
    }
  ];

  const handleStarToggle = () => {
    setIsStarred(!isStarred);
  };

  const handleBookmarkToggle = () => {
    setIsBookmarked(!isBookmarked);
  };

  const handleAddComment = () => {
    if (newComment.trim()) {
      // Add comment logic here
      setNewComment('');
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'document':
        return FileText;
      case 'video':
        return Video;
      case 'image':
        return Image;
      case 'link':
        return Link;
      default:
        return FileText;
    }
  };

  const TypeIcon = getTypeIcon(document.type);

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={onBack}
                className="flex items-center space-x-2 px-3 py-2 border border-border rounded-lg hover:bg-muted transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Knowledge Base</span>
              </button>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                  <TypeIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">{document.title}</h1>
                  <p className="text-sm text-muted-foreground">{document.description}</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleStarToggle}
                className={`p-2 rounded-lg transition-colors ${
                  isStarred 
                    ? 'text-yellow-500 bg-yellow-100 dark:bg-yellow-900/20' 
                    : 'text-muted-foreground hover:bg-muted'
                }`}
              >
                <Star className={`w-5 h-5 ${isStarred ? 'fill-current' : ''}`} />
              </button>
              <button
                onClick={handleBookmarkToggle}
                className={`p-2 rounded-lg transition-colors ${
                  isBookmarked 
                    ? 'text-blue-500 bg-blue-100 dark:bg-blue-900/20' 
                    : 'text-muted-foreground hover:bg-muted'
                }`}
              >
                <Bookmark className={`w-5 h-5 ${isBookmarked ? 'fill-current' : ''}`} />
              </button>
              <button className="p-2 rounded-lg text-muted-foreground hover:bg-muted transition-colors">
                <Download className="w-5 h-5" />
              </button>
              <button className="p-2 rounded-lg text-muted-foreground hover:bg-muted transition-colors">
                <Share2 className="w-5 h-5" />
              </button>
              <button className="p-2 rounded-lg text-muted-foreground hover:bg-muted transition-colors">
                <Edit className="w-5 h-5" />
              </button>
              <button className="p-2 rounded-lg text-muted-foreground hover:bg-muted transition-colors">
                <MoreVertical className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className={`rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
              <div className="p-8">
                <div className="prose prose-lg max-w-none dark:prose-invert">
                  <div dangerouslySetInnerHTML={{ __html: document.content.replace(/\n/g, '<br>').replace(/#{1,6}\s/g, '<h3>').replace(/<h3>/g, '</p><h3>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>').replace(/\*(.*?)\*/g, '<em>$1</em>') }} />
                </div>
              </div>
            </div>

            {/* Comments Section */}
            <div className={`mt-8 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-foreground">Comments ({comments.length})</h3>
                  <button
                    onClick={() => setShowComments(!showComments)}
                    className="text-sm text-primary hover:underline"
                  >
                    {showComments ? 'Hide Comments' : 'Show Comments'}
                  </button>
                </div>
              </div>
              
              {showComments && (
                <div className="p-6">
                  {/* Add Comment */}
                  <div className="mb-6">
                    <textarea
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      placeholder="Add a comment..."
                      className={`w-full p-3 border rounded-lg resize-none ${
                        darkMode 
                          ? 'bg-gray-700 border-gray-600 text-white' 
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      rows={3}
                    />
                    <div className="flex justify-end mt-2">
                      <button
                        onClick={handleAddComment}
                        disabled={!newComment.trim()}
                        className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed transition-opacity"
                      >
                        Add Comment
                      </button>
                    </div>
                  </div>

                  {/* Comments List */}
                  <div className="space-y-4">
                    {comments.map((comment) => (
                      <div key={comment.id} className="flex space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                          {comment.author.charAt(0)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-medium text-foreground">{comment.author}</span>
                            <span className="text-xs text-muted-foreground">{comment.timestamp}</span>
                          </div>
                          <p className="text-sm text-foreground mb-2">{comment.content}</p>
                          <div className="flex items-center space-x-2">
                            <button className="flex items-center space-x-1 text-xs text-muted-foreground hover:text-foreground">
                              <ThumbsUp className="w-3 h-3" />
                              <span>{comment.likes}</span>
                            </button>
                            <button className="text-xs text-muted-foreground hover:text-foreground">
                              Reply
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className={`rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-foreground mb-4">Document Info</h3>
                
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <User className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium text-foreground">{document.author}</p>
                      <p className="text-xs text-muted-foreground">Author</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium text-foreground">{new Date(document.updatedAt).toLocaleDateString()}</p>
                      <p className="text-xs text-muted-foreground">Last Updated</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Eye className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium text-foreground">{document.views}</p>
                      <p className="text-xs text-muted-foreground">Views</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <FileText className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium text-foreground">{document.fileSize}</p>
                      <p className="text-xs text-muted-foreground">File Size</p>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <h4 className="text-sm font-semibold text-foreground mb-3">Tags</h4>
                  <div className="flex flex-wrap gap-2">
                    {document.tags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center px-2 py-1 text-xs bg-muted text-muted-foreground rounded-full"
                      >
                        <Tag className="w-3 h-3 mr-1" />
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="mt-6">
                  <h4 className="text-sm font-semibold text-foreground mb-3">Actions</h4>
                  <div className="space-y-2">
                    <button className="w-full flex items-center space-x-2 px-3 py-2 text-sm border border-border rounded-lg hover:bg-muted transition-colors">
                      <Download className="w-4 h-4" />
                      <span>Download PDF</span>
                    </button>
                    <button className="w-full flex items-center space-x-2 px-3 py-2 text-sm border border-border rounded-lg hover:bg-muted transition-colors">
                      <Share2 className="w-4 h-4" />
                      <span>Share Link</span>
                    </button>
                    <button className="w-full flex items-center space-x-2 px-3 py-2 text-sm border border-border rounded-lg hover:bg-muted transition-colors">
                      <Edit className="w-4 h-4" />
                      <span>Edit Document</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentViewer;
