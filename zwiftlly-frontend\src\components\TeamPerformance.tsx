import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TrendingUp, Users, Clock, Target, Award, AlertTriangle } from 'lucide-react';

const TeamPerformance: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('monthly');
  const [selectedTeam, setSelectedTeam] = useState('all');

  const performanceMetrics = [
    {
      title: 'Audit Quality',
      value: '94.2%',
      change: '+2.1%',
      trend: 'up',
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-100/50 dark:bg-green-900/20',
      icon: Award
    },
    {
      title: 'Idle Time',
      value: '2.3 hrs',
      change: '-0.8 hrs',
      trend: 'up',
      color: 'text-orange-600 dark:text-orange-400',
      bgColor: 'bg-orange-100/50 dark:bg-orange-900/20',
      icon: Clock
    },
    {
      title: 'Attendance',
      value: '96.5%',
      change: '+1.2%',
      trend: 'up',
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-100/50 dark:bg-blue-900/20',
      icon: Users
    },
    {
      title: 'Total Audited',
      value: '1,247',
      change: '+156',
      trend: 'up',
      color: 'text-purple-600 dark:text-purple-400',
      bgColor: 'bg-purple-100/50 dark:bg-purple-900/20',
      icon: Target
    }
  ];

  const teamMembers = [
    { name: 'Louise', qualityScore: 96, auditsCompleted: 45, idleTime: '2h 15m', adherence: 94 },
    { name: 'Marcus', qualityScore: 93, auditsCompleted: 42, idleTime: '3h 30m', adherence: 89 },
    { name: 'Sarah', qualityScore: 95, auditsCompleted: 48, idleTime: '1h 45m', adherence: 96 },
    { name: 'David', qualityScore: 91, auditsCompleted: 38, idleTime: '4h 20m', adherence: 87 },
    { name: 'Emma', qualityScore: 97, auditsCompleted: 52, idleTime: '1h 30m', adherence: 98 },
    { name: 'James', qualityScore: 89, auditsCompleted: 35, idleTime: '5h 10m', adherence: 85 }
  ];

  const getScoreColor = (score: number) => {
    if (score >= 95) return 'text-green-600 dark:text-green-400';
    if (score >= 90) return 'text-blue-600 dark:text-blue-400';
    if (score >= 85) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header with Animation */}
        <div className="mb-8 animate-fade-in-up animate-delay-0">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Team Performance
          </h1>
          <p className="text-muted-foreground">
            Analytics, quality scores, and performance insights
          </p>
        </div>

        {/* Controls */}
        <div className="mb-6 flex flex-col sm:flex-row gap-4 animate-fade-in animate-delay-150">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-48 bg-card border-border/50">
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={selectedTeam} onValueChange={setSelectedTeam}>
            <SelectTrigger className="w-48 bg-card border-border/50">
              <SelectValue placeholder="Select team" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Teams</SelectItem>
              <SelectItem value="audit">Audit Team</SelectItem>
              <SelectItem value="quality">Quality Team</SelectItem>
              <SelectItem value="support">Support Team</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Performance Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 animate-slide-in-right animate-delay-300">
          {performanceMetrics.map((metric, index) => (
            <Card key={metric.title} className="bg-card rounded-xl shadow-lg border border-border/50 hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-3 rounded-xl ${metric.bgColor}`}>
                    <metric.icon className={`w-6 h-6 ${metric.color}`} />
                  </div>
                  <div className={`text-sm font-medium ${metric.trend === 'up' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                    {metric.change}
                  </div>
                </div>
                <div className={`text-2xl font-bold ${metric.color} mb-1`}>
                  {metric.value}
                </div>
                <div className="text-sm text-muted-foreground font-medium">
                  {metric.title}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Team Performance Table */}
        <Card className="bg-card rounded-xl shadow-lg border border-border/50 animate-fade-in-up animate-delay-450">
          <CardHeader className="p-6 border-b border-border/50 bg-gradient-to-r from-background to-muted/20">
            <CardTitle className="text-xl flex items-center space-x-3">
              <div className="p-3 bg-muted/20 rounded-xl shadow-sm border border-border/30">
                <TrendingUp className="w-6 h-6 text-foreground" />
              </div>
              <div>
                <span className="text-foreground font-bold text-xl">Individual Performance</span>
                <div className="text-sm text-muted-foreground font-medium">Detailed team member analytics</div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-muted/10 border-b border-border">
                    <th className="text-left p-4 font-bold text-foreground">Team Member</th>
                    <th className="text-center p-4 font-bold text-foreground">Audit Quality</th>
                    <th className="text-center p-4 font-bold text-foreground">Total Audited</th>
                    <th className="text-center p-4 font-bold text-foreground">Idle Time</th>
                    <th className="text-center p-4 font-bold text-foreground">Attendance</th>
                  </tr>
                </thead>
                <tbody>
                  {teamMembers.map((member, index) => (
                    <tr key={member.name} className={`border-b border-border/50 hover:bg-muted/10 transition-colors ${
                      index % 2 === 0 ? 'bg-muted/5' : 'bg-background'
                    }`}>
                      <td className="p-4 font-semibold">
                        <div className="flex items-center space-x-3">
                          <div className="w-9 h-9 bg-muted rounded-full flex items-center justify-center shadow-sm border border-border/30">
                            <span className="text-xs font-bold text-foreground">
                              {member.name.split(' ').map(n => n[0]).join('')}
                            </span>
                          </div>
                          <span className="text-foreground font-medium">{member.name}</span>
                        </div>
                      </td>
                      <td className="p-4 text-center">
                        <span className={`text-lg font-bold ${getScoreColor(member.qualityScore)}`}>
                          {member.qualityScore}%
                        </span>
                      </td>
                      <td className="p-4 text-center">
                        <span className="text-foreground font-semibold">{member.auditsCompleted}</span>
                      </td>
                      <td className="p-4 text-center">
                        <span className="text-orange-600 dark:text-orange-400 font-semibold">{member.idleTime}</span>
                      </td>
                      <td className="p-4 text-center">
                        <span className={`font-bold ${getScoreColor(member.adherence)}`}>
                          {member.adherence}%
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TeamPerformance;
