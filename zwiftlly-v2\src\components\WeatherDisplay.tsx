import React, { useState, useEffect } from 'react';
import { 
  Cloud, 
  Sun, 
  CloudRain, 
  CloudSnow, 
  CloudLightning, 
  CloudDrizzle,
  Eye,
  Wind,
  Droplets,
  Thermometer,
  Loader2,
  AlertTriangle
} from 'lucide-react';

interface WeatherData {
  location: string;
  temperature: number;
  condition: string;
  description: string;
  humidity: number;
  windSpeed: number;
  visibility: number;
  icon: string;
  feelsLike: number;
}

interface WeatherDisplayProps {
  darkMode?: boolean;
  compact?: boolean;
  location?: string;
}

const WeatherDisplay: React.FC<WeatherDisplayProps> = ({ 
  darkMode = false, 
  compact = false,
  location = 'Manila, Philippines' // Default location
}) => {
  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock weather data for demonstration
  // In a real implementation, you would fetch from a weather API
  const mockWeatherData: WeatherData = {
    location: location,
    temperature: 28,
    condition: 'partly-cloudy',
    description: 'Partly Cloudy',
    humidity: 75,
    windSpeed: 12,
    visibility: 10,
    icon: 'partly-cloudy',
    feelsLike: 32
  };

  useEffect(() => {
    // Simulate API call
    const fetchWeather = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // For demo purposes, we'll use mock data
        // In production, replace with actual weather API call
        setWeather(mockWeatherData);
      } catch (err) {
        setError('Failed to fetch weather data');
        console.error('Weather fetch error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchWeather();
    
    // Refresh weather every 30 minutes
    const interval = setInterval(fetchWeather, 30 * 60 * 1000);
    return () => clearInterval(interval);
  }, [location]);

  const getWeatherIcon = (condition: string) => {
    const iconClass = `w-6 h-6 ${darkMode ? 'text-yellow-400' : 'text-yellow-500'}`;
    
    switch (condition.toLowerCase()) {
      case 'sunny':
      case 'clear':
        return <Sun className={iconClass} />;
      case 'partly-cloudy':
      case 'partly-sunny':
        return <Cloud className={iconClass} />;
      case 'cloudy':
      case 'overcast':
        return <Cloud className={`w-6 h-6 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />;
      case 'rain':
      case 'rainy':
        return <CloudRain className={`w-6 h-6 ${darkMode ? 'text-blue-400' : 'text-blue-500'}`} />;
      case 'drizzle':
        return <CloudDrizzle className={`w-6 h-6 ${darkMode ? 'text-blue-400' : 'text-blue-500'}`} />;
      case 'snow':
      case 'snowy':
        return <CloudSnow className={`w-6 h-6 ${darkMode ? 'text-blue-200' : 'text-blue-300'}`} />;
      case 'thunderstorm':
      case 'storm':
        return <CloudLightning className={`w-6 h-6 ${darkMode ? 'text-purple-400' : 'text-purple-500'}`} />;
      default:
        return <Sun className={iconClass} />;
    }
  };

  if (loading) {
    return (
      <div className={`flex items-center space-x-2 ${compact ? 'p-2' : 'p-4'}`}>
        <Loader2 className={`w-4 h-4 animate-spin ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
        <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          Loading weather...
        </span>
      </div>
    );
  }

  if (error || !weather) {
    return (
      <div className={`flex items-center space-x-2 ${compact ? 'p-2' : 'p-4'}`}>
        <AlertTriangle className={`w-4 h-4 ${darkMode ? 'text-red-400' : 'text-red-500'}`} />
        <span className={`text-sm ${darkMode ? 'text-red-400' : 'text-red-600'}`}>
          Weather unavailable
        </span>
      </div>
    );
  }

  if (compact) {
    return (
      <div className="flex items-center space-x-3">
        {getWeatherIcon(weather.condition)}
        <div className="flex items-center space-x-2">
          <span className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            {weather.temperature}°C
          </span>
          <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {weather.description}
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className={`rounded-lg border p-4 ${
      darkMode 
        ? 'bg-gray-800 border-gray-700' 
        : 'bg-white border-gray-200'
    }`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Weather
          </h3>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {weather.location}
          </p>
        </div>
        {getWeatherIcon(weather.condition)}
      </div>

      {/* Main Temperature */}
      <div className="flex items-baseline space-x-2 mb-4">
        <span className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          {weather.temperature}°
        </span>
        <span className={`text-lg ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          C
        </span>
        <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          Feels like {weather.feelsLike}°C
        </span>
      </div>

      {/* Condition */}
      <p className={`text-lg mb-4 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
        {weather.description}
      </p>

      {/* Weather Details */}
      <div className="grid grid-cols-2 gap-4">
        <div className="flex items-center space-x-2">
          <Droplets className={`w-4 h-4 ${darkMode ? 'text-blue-400' : 'text-blue-500'}`} />
          <div>
            <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Humidity
            </p>
            <p className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              {weather.humidity}%
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Wind className={`w-4 h-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
          <div>
            <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Wind Speed
            </p>
            <p className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              {weather.windSpeed} km/h
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Eye className={`w-4 h-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
          <div>
            <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Visibility
            </p>
            <p className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              {weather.visibility} km
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Thermometer className={`w-4 h-4 ${darkMode ? 'text-red-400' : 'text-red-500'}`} />
          <div>
            <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Feels Like
            </p>
            <p className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              {weather.feelsLike}°C
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WeatherDisplay;
