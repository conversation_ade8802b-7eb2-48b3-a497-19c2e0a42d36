import React from 'react';

interface ZwiftllyLogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  animated?: boolean;
}

const ZwiftllyLogo: React.FC<ZwiftllyLogoProps> = ({ 
  size = 'md', 
  className = '', 
  animated = false 
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  };

  const strokeWidths = {
    sm: '3',
    md: '4',
    lg: '5',
    xl: '6'
  };

  return (
    <div className={`${sizeClasses[size]} ${className} relative`}>
      <svg
        viewBox="0 0 100 100"
        className="w-full h-full"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          {/* Gradient for the paint brush effect */}
          <linearGradient id="brushGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#3B82F6" />
            <stop offset="50%" stopColor="#6366F1" />
            <stop offset="100%" stopColor="#8B5CF6" />
          </linearGradient>
          
          {/* Filter for paint brush texture */}
          <filter id="roughPaper" x="0%" y="0%" width="100%" height="100%">
            <feTurbulence 
              baseFrequency="0.04" 
              numOctaves="5" 
              result="noise" 
              seed="1"
            />
            <feDisplacementMap 
              in="SourceGraphic" 
              in2="noise" 
              scale="1.5"
            />
          </filter>

          {/* Drop shadow */}
          <filter id="dropshadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="2" dy="2" stdDeviation="3" floodOpacity="0.3"/>
          </filter>
        </defs>

        {/* Paint brush stroke Z */}
        <path
          d="M 20 25 L 75 25 C 78 25 80 27 80 30 C 80 33 78 35 75 35 L 45 35 L 75 65 L 80 65 C 83 65 85 67 85 70 C 85 73 83 75 80 75 L 25 75 C 22 75 20 73 20 70 C 20 67 22 65 25 65 L 55 65 L 25 35 L 20 35 C 17 35 15 33 15 30 C 15 27 17 25 20 25 Z"
          fill="url(#brushGradient)"
          stroke="url(#brushGradient)"
          strokeWidth={strokeWidths[size]}
          strokeLinecap="round"
          strokeLinejoin="round"
          filter="url(#roughPaper)"
          className={animated ? 'animate-pulse' : ''}
        />

        {/* Additional paint texture strokes for authenticity */}
        <path
          d="M 22 27 L 73 27"
          stroke="rgba(255,255,255,0.3)"
          strokeWidth="1"
          strokeLinecap="round"
          filter="url(#roughPaper)"
        />
        <path
          d="M 27 73 L 78 73"
          stroke="rgba(255,255,255,0.3)"
          strokeWidth="1"
          strokeLinecap="round"
          filter="url(#roughPaper)"
        />

        {/* Paint drips for extra brush effect */}
        <circle
          cx="25"
          cy="78"
          r="1.5"
          fill="url(#brushGradient)"
          opacity="0.6"
          filter="url(#roughPaper)"
        />
        <circle
          cx="75"
          cy="78"
          r="1"
          fill="url(#brushGradient)"
          opacity="0.4"
          filter="url(#roughPaper)"
        />
      </svg>

      {/* Animated paint brush effect overlay */}
      {animated && (
        <div className="absolute inset-0 animate-ping opacity-20">
          <svg viewBox="0 0 100 100" className="w-full h-full">
            <path
              d="M 20 25 L 75 25 C 78 25 80 27 80 30 C 80 33 78 35 75 35 L 45 35 L 75 65 L 80 65 C 83 65 85 67 85 70 C 85 73 83 75 80 75 L 25 75 C 22 75 20 73 20 70 C 20 67 22 65 25 65 L 55 65 L 25 35 L 20 35 C 17 35 15 33 15 30 C 15 27 17 25 20 25 Z"
              fill="url(#brushGradient)"
              strokeWidth={strokeWidths[size]}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      )}
    </div>
  );
};

export default ZwiftllyLogo;
