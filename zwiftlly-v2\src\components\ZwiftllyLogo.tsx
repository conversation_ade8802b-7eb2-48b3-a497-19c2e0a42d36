import React from 'react';

interface ZwiftllyLogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  animated?: boolean;
  darkMode?: boolean;
}

const ZwiftllyLogo: React.FC<ZwiftllyLogoProps> = ({
  size = 'md',
  className = '',
  animated = false,
  darkMode = false
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  };

  // Color based on theme - black for light mode, white for dark mode
  const logoColor = darkMode ? '#FFFFFF' : '#000000';

  return (
    <div className={`${sizeClasses[size]} ${className} relative`}>
      <svg
        viewBox="0 0 100 100"
        className="w-full h-full"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Modern Z shape based on the provided design */}
        <path
          d="M 15 15 L 85 15 L 85 30 L 40 30 L 85 70 L 85 85 L 15 85 L 15 70 L 60 70 L 15 30 L 15 15 Z"
          fill={logoColor}
          className="transition-colors duration-200"
        />

        {/* Inner cutout to create the distinctive Z shape */}
        <path
          d="M 30 30 L 70 30 L 45 52.5 L 70 70 L 30 70 L 55 47.5 L 30 30 Z"
          fill={darkMode ? '#1f2937' : '#f8fafc'}
          className="transition-colors duration-200"
        />
      </svg>
    </div>
  );
};

export default ZwiftllyLogo;
