import React from 'react';

interface ZwiftllyLogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  animated?: boolean;
  darkMode?: boolean;
}

const ZwiftllyLogo: React.FC<ZwiftllyLogoProps> = ({
  size = 'md',
  className = '',
  animated = false,
  darkMode = false
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  };

  return (
    <div className={`${sizeClasses[size]} ${className} relative`}>
      <img
        src="/zwiftlly-logo.svg"
        alt="ZWIFTLLY Logo"
        className={`w-full h-full object-contain transition-all duration-200 ${
          darkMode ? 'filter invert' : ''
        }`}
        style={{
          filter: darkMode ? 'invert(1)' : 'none'
        }}
      />
    </div>
  );
};

export default ZwiftllyLogo;
