import React from 'react';

interface ZwiftllyLogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  animated?: boolean;
  darkMode?: boolean;
}

const ZwiftllyLogo: React.FC<ZwiftllyLogoProps> = ({
  size = 'md',
  className = '',
  animated = false,
  darkMode = false
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  };

  const strokeWidths = {
    sm: '2',
    md: '3',
    lg: '4',
    xl: '5'
  };

  // Color based on theme - black for light mode, white for dark mode
  const logoColor = darkMode ? '#FFFFFF' : '#000000';
  const shadowColor = darkMode ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)';

  return (
    <div className={`${sizeClasses[size]} ${className} relative`}>
      <svg
        viewBox="0 0 100 100"
        className="w-full h-full"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          {/* Drop shadow */}
          <filter id="dropshadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="1" dy="1" stdDeviation="2" floodColor={shadowColor} floodOpacity="0.3"/>
          </filter>
        </defs>

        {/* Main Z shape - inspired by 1Z Entertainment with thick brush style */}
        <path
          d="M 15 20 L 80 20 C 83 20 85 22 85 25 C 85 28 83 30 80 30 L 35 30 L 80 70 L 85 70 C 88 70 90 72 90 75 C 90 78 88 80 85 80 L 20 80 C 17 80 15 78 15 75 C 15 72 17 70 20 70 L 65 70 L 20 30 L 15 30 C 12 30 10 28 10 25 C 10 22 12 20 15 20 Z"
          fill={logoColor}
          stroke={logoColor}
          strokeWidth={strokeWidths[size]}
          strokeLinecap="round"
          strokeLinejoin="round"
          filter="url(#dropshadow)"
          className={animated ? 'animate-pulse' : ''}
        />

        {/* Pillar elements - representing support (1Z Entertainment concept) */}
        <rect
          x="12"
          y="18"
          width="6"
          height="64"
          fill={logoColor}
          opacity="0.8"
          rx="3"
          className={animated ? 'animate-pulse' : ''}
        />
        <rect
          x="82"
          y="18"
          width="6"
          height="64"
          fill={logoColor}
          opacity="0.8"
          rx="3"
          className={animated ? 'animate-pulse' : ''}
        />

        {/* Equilibrium symbol - small accent */}
        <circle
          cx="50"
          cy="50"
          r="2"
          fill={logoColor}
          opacity="0.6"
        />
      </svg>

      {/* Animated overlay for enhanced effect */}
      {animated && (
        <div className="absolute inset-0 animate-ping opacity-10">
          <svg viewBox="0 0 100 100" className="w-full h-full">
            <path
              d="M 15 20 L 80 20 C 83 20 85 22 85 25 C 85 28 83 30 80 30 L 35 30 L 80 70 L 85 70 C 88 70 90 72 90 75 C 90 78 88 80 85 80 L 20 80 C 17 80 15 78 15 75 C 15 72 17 70 20 70 L 65 70 L 20 30 L 15 30 C 12 30 10 28 10 25 C 10 22 12 20 15 20 Z"
              fill={logoColor}
              strokeWidth={strokeWidths[size]}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      )}
    </div>
  );
};

export default ZwiftllyLogo;
