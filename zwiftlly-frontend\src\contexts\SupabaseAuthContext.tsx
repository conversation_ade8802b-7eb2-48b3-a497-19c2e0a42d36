import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import { supabase } from '../lib/supabase';
import type { User as SupabaseUser, Session } from '@supabase/supabase-js';
import type { Tables } from '../lib/supabase';

// Define the CredentialResponse type locally
interface CredentialResponse {
  credential?: string;
  select_by?: string;
}

// User type based on our database schema
interface User extends Tables<'users'> {
  permissions?: string[];
}

interface Organization extends Tables<'organizations'> {}

interface AuthContextType {
  user: User | null;
  session: Session | null;
  googleLogin: () => Promise<{ success: boolean; message: string; }>;
  googleSignUp: () => Promise<{ success: boolean; message: string; }>;
  logout: () => Promise<void>;
  isLoading: boolean;
  hasPermission: (permission: string) => boolean;
  checkUserExists: (email: string) => Promise<boolean>;
  // Organization management functions
  getAllOrganizations: () => Promise<Organization[]>;
  addOrganization: (orgData: { domain: string; name: string; default_role: User['role']; admin_emails: string[]; }) => Promise<{ success: boolean; message: string; }>;
  updateOrganization: (orgId: string, orgData: Partial<Organization>) => Promise<{ success: boolean; message: string; }>;
  deleteOrganization: (orgId: string) => Promise<{ success: boolean; message: string; }>;
  // User management functions
  getAllUsers: () => Promise<User[]>;
  updateUser: (userId: string, userData: Partial<User>) => Promise<{ success: boolean; message: string; }>;
  deleteUser: (userId: string) => Promise<{ success: boolean; message: string; }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const ROLE_PERMISSIONS = {
  SUPER_ADMIN: ['all'],
  ADMIN: ['all'],
  MANAGER: ['view_dashboard', 'view_schedules', 'view_performance', 'manage_tasks', 'view_knowledge', 'manage_users'],
  AGENT: ['view_dashboard', 'view_schedules', 'view_tasks', 'view_knowledge'],
  VIEWER: ['view_dashboard', 'view_knowledge']
};

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  console.log('🔧 SupabaseAuthProvider: Initializing...');
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      if (session?.user) {
        handleUserSession(session.user);
      } else {
        setIsLoading(false);
      }
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔐 Auth state changed:', event, session?.user?.email);
      setSession(session);

      if (session?.user) {
        await handleUserSession(session.user);
      } else {
        setUser(null);
        setIsLoading(false);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const handleUserSession = async (authUser: SupabaseUser) => {
    try {
      // Check if this is a sign-up flow
      const urlParams = new URLSearchParams(window.location.search);
      const isSignUp = urlParams.get('signup') === 'true';

      if (isSignUp) {
        // For sign-up, create user profile if it doesn't exist
        await createUserProfile(authUser);
      } else {
        // For sign-in, just fetch existing profile
        await fetchUserProfile(authUser.id);
      }
    } catch (error) {
      console.error('Error handling user session:', error);
      setIsLoading(false);
    }
  };

  const createUserProfile = async (authUser: SupabaseUser) => {
    try {
      console.log('🔧 Creating user profile for:', authUser.email);

      // Check if user already exists
      const { data: existingUser, error: checkError } = await supabase
        .from('users')
        .select('id')
        .eq('id', authUser.id)
        .single();

      console.log('🔧 Existing user check:', { existingUser, checkError });

      if (existingUser) {
        // User already exists, just fetch profile
        console.log('✅ User exists, fetching profile');
        await fetchUserProfile(authUser.id);
        return;
      }

      // Validate domain
      const domain = authUser.email?.split('@')[1];
      const authorizedDomains = ['gmail.com', 'netic.ai', 'zwiftlly.com'];

      console.log('🔧 Domain validation:', { domain, authorizedDomains });

      if (!domain || !authorizedDomains.includes(domain)) {
        console.error('❌ Unauthorized domain:', domain);
        throw new Error(`Domain ${domain} is not authorized to sign up`);
      }

      if (domain === 'gmail.com' && authUser.email !== '<EMAIL>') {
        console.error('❌ Unauthorized Gmail account:', authUser.email);
        throw new Error('Only <EMAIL> is allowed from gmail.com domain');
      }

      console.log('✅ Domain authorized, waiting for trigger to create user...');

      // The trigger function will handle user creation
      // Poll for user creation instead of using timeout
      let attempts = 0;
      const maxAttempts = 10;

      const pollForUser = async () => {
        attempts++;
        console.log(`🔄 Polling for user creation, attempt ${attempts}/${maxAttempts}`);

        const { data: newUser, error: pollError } = await supabase
          .from('users')
          .select('id')
          .eq('id', authUser.id)
          .single();

        if (newUser) {
          console.log('✅ User created by trigger, fetching full profile');
          await fetchUserProfile(authUser.id);
          return;
        }

        if (attempts < maxAttempts) {
          setTimeout(pollForUser, 500);
        } else {
          console.error('❌ User creation timeout');
          setIsLoading(false);
        }
      };

      pollForUser();

    } catch (error) {
      console.error('❌ Error creating user profile:', error);
      // Sign out the user if domain is not authorized
      await supabase.auth.signOut();
      setIsLoading(false);
      throw error;
    }
  };

  const fetchUserProfile = async (userId: string) => {
    try {
      console.log('🔧 Fetching user profile for ID:', userId);

      const { data: userData, error } = await supabase
        .from('users')
        .select(`
          *,
          organizations (*)
        `)
        .eq('id', userId)
        .single();

      console.log('🔧 User profile fetch result:', { userData, error });

      if (error) {
        console.error('❌ Error fetching user profile:', error);
        if (error.code === 'PGRST116') {
          console.log('🔧 User not found in database, might be new user');
        }
        setIsLoading(false);
        return;
      }

      if (userData) {
        console.log('✅ User profile fetched successfully:', userData.email);
        // Add permissions based on role
        const userWithPermissions: User = {
          ...userData,
          permissions: ROLE_PERMISSIONS[userData.role] || []
        };
        setUser(userWithPermissions);
        console.log('✅ User state updated, redirecting to dashboard');
      }
    } catch (error) {
      console.error('❌ Error in fetchUserProfile:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const checkUserExists = async (email: string): Promise<boolean> => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id')
        .eq('email', email)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Error checking user existence:', error);
        return false;
      }

      return !!data;
    } catch (error) {
      console.error('Error in checkUserExists:', error);
      return false;
    }
  };

  const googleLogin = async (): Promise<{ success: boolean; message: string; }> => {
    setIsLoading(true);

    try {
      console.log('🔐 Starting Supabase Google OAuth sign-in flow...');

      // Use Supabase OAuth flow for Google
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}`,
          queryParams: {
            access_type: 'offline',
            prompt: 'select_account',
          },
        },
      });

      if (error) {
        console.error('❌ Supabase OAuth error:', error);
        setIsLoading(false);
        return {
          success: false,
          message: error.message || 'Authentication failed'
        };
      }

      console.log('🔄 Google OAuth sign-in redirect initiated...');
      return {
        success: true,
        message: 'Redirecting to Google...'
      };

    } catch (error) {
      console.error('❌ Google OAuth error:', error);
      setIsLoading(false);
      return {
        success: false,
        message: 'Login failed. Please try again.'
      };
    }
  };

  const googleSignUp = async (): Promise<{ success: boolean; message: string; }> => {
    setIsLoading(true);

    try {
      console.log('🔐 Starting Supabase Google OAuth sign-up flow...');

      // Use Supabase OAuth flow for Google with sign-up intent
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}?signup=true`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) {
        console.error('❌ Supabase OAuth error:', error);
        setIsLoading(false);
        return {
          success: false,
          message: error.message || 'Authentication failed'
        };
      }

      console.log('🔄 Google OAuth sign-up redirect initiated...');
      return {
        success: true,
        message: 'Redirecting to Google...'
      };

    } catch (error) {
      console.error('❌ Google OAuth error:', error);
      setIsLoading(false);
      return {
        success: false,
        message: 'Sign-up failed. Please try again.'
      };
    }
  };

  const logout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Logout error:', error);
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      setSession(null);
      // Clear any local storage items
      localStorage.removeItem('zwiftlly_user');
      localStorage.removeItem('zwiftlly_auth_token');
      localStorage.removeItem('auth_token');
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    const userPermissions = user.permissions || [];
    return userPermissions.includes('all') || userPermissions.includes(permission);
  };

  // Organization management functions
  const getAllOrganizations = async (): Promise<Organization[]> => {
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .eq('is_active', true);

      if (error) {
        console.error('Error fetching organizations:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getAllOrganizations:', error);
      return [];
    }
  };

  const addOrganization = async (orgData: {
    domain: string;
    name: string;
    default_role: User['role'];
    admin_emails: string[];
  }): Promise<{ success: boolean; message: string; }> => {
    if (!hasPermission('all')) {
      return { success: false, message: 'You do not have permission to add organizations.' };
    }

    try {
      const { data, error } = await supabase
        .from('organizations')
        .insert([{
          domain: orgData.domain.toLowerCase(),
          name: orgData.name,
          default_role: orgData.default_role,
          admin_emails: orgData.admin_emails.map(email => email.toLowerCase())
        }])
        .select()
        .single();

      if (error) {
        console.error('Error adding organization:', error);
        return { success: false, message: error.message };
      }

      return { success: true, message: 'Organization added successfully.' };
    } catch (error) {
      console.error('Error in addOrganization:', error);
      return { success: false, message: 'Failed to add organization.' };
    }
  };

  const updateOrganization = async (orgId: string, orgData: Partial<Organization>): Promise<{ success: boolean; message: string; }> => {
    if (!hasPermission('all')) {
      return { success: false, message: 'You do not have permission to update organizations.' };
    }

    try {
      const { error } = await supabase
        .from('organizations')
        .update(orgData)
        .eq('id', orgId);

      if (error) {
        console.error('Error updating organization:', error);
        return { success: false, message: error.message };
      }

      return { success: true, message: 'Organization updated successfully.' };
    } catch (error) {
      console.error('Error in updateOrganization:', error);
      return { success: false, message: 'Failed to update organization.' };
    }
  };

  const deleteOrganization = async (orgId: string): Promise<{ success: boolean; message: string; }> => {
    if (!hasPermission('all')) {
      return { success: false, message: 'You do not have permission to delete organizations.' };
    }

    try {
      const { error } = await supabase
        .from('organizations')
        .update({ is_active: false })
        .eq('id', orgId);

      if (error) {
        console.error('Error deleting organization:', error);
        return { success: false, message: error.message };
      }

      return { success: true, message: 'Organization deleted successfully.' };
    } catch (error) {
      console.error('Error in deleteOrganization:', error);
      return { success: false, message: 'Failed to delete organization.' };
    }
  };

  // User management functions
  const getAllUsers = async (): Promise<User[]> => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select(`
          *,
          organizations (*)
        `)
        .eq('is_active', true);

      if (error) {
        console.error('Error fetching users:', error);
        return [];
      }

      return data?.map(userData => ({
        ...userData,
        permissions: ROLE_PERMISSIONS[userData.role] || []
      })) || [];
    } catch (error) {
      console.error('Error in getAllUsers:', error);
      return [];
    }
  };

  const updateUser = async (userId: string, userData: Partial<User>): Promise<{ success: boolean; message: string; }> => {
    if (!hasPermission('manage_users') && !hasPermission('all') && userId !== user?.id) {
      return { success: false, message: 'You do not have permission to update this user.' };
    }

    try {
      const { error } = await supabase
        .from('users')
        .update(userData)
        .eq('id', userId);

      if (error) {
        console.error('Error updating user:', error);
        return { success: false, message: error.message };
      }

      // If updating current user, refresh profile
      if (userId === user?.id) {
        await fetchUserProfile(userId);
      }

      return { success: true, message: 'User updated successfully.' };
    } catch (error) {
      console.error('Error in updateUser:', error);
      return { success: false, message: 'Failed to update user.' };
    }
  };

  const deleteUser = async (userId: string): Promise<{ success: boolean; message: string; }> => {
    if (!hasPermission('manage_users') && !hasPermission('all')) {
      return { success: false, message: 'You do not have permission to delete users.' };
    }

    if (user && user.id === userId) {
      return { success: false, message: 'You cannot delete your own account.' };
    }

    try {
      const { error } = await supabase
        .from('users')
        .update({ is_active: false })
        .eq('id', userId);

      if (error) {
        console.error('Error deleting user:', error);
        return { success: false, message: error.message };
      }

      return { success: true, message: 'User deleted successfully.' };
    } catch (error) {
      console.error('Error in deleteUser:', error);
      return { success: false, message: 'Failed to delete user.' };
    }
  };

  const value: AuthContextType = {
    user,
    session,
    googleLogin,
    googleSignUp,
    logout,
    isLoading,
    hasPermission,
    checkUserExists,
    getAllOrganizations,
    addOrganization,
    updateOrganization,
    deleteOrganization,
    getAllUsers,
    updateUser,
    deleteUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  console.log('🔧 useAuth: Called from component');
  const context = useContext(AuthContext);
  console.log('🔧 useAuth: Context value:', context);
  if (context === undefined) {
    console.error('❌ useAuth: Context is undefined - component not wrapped in AuthProvider');
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
