import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL!
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

// Database types (based on our schema)
export interface Database {
  public: {
    Tables: {
      organizations: {
        Row: {
          id: string
          name: string
          domain: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          domain: string
        }
        Update: {
          name?: string
          domain?: string
        }
      }
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          nickname: string | null
          role: 'ADMIN' | 'AGENT' | 'USER'
          organization_id: string | null
          avatar_url: string | null
          is_active: boolean
          last_login: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          nickname?: string | null
          role?: 'ADMIN' | 'AGENT' | 'USER'
          organization_id?: string | null
          avatar_url?: string | null
          is_active?: boolean
          last_login?: string | null
        }
        Update: {
          full_name?: string | null
          nickname?: string | null
          role?: 'ADMIN' | 'AGENT' | 'USER'
          avatar_url?: string | null
          is_active?: boolean
          last_login?: string | null
        }
      }
      tasks: {
        Row: {
          id: string
          title: string
          description: string | null
          status: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'DONE'
          priority: 'LOW' | 'MEDIUM' | 'HIGH'
          assignee_id: string | null
          created_by: string | null
          organization_id: string | null
          due_date: string | null
          completed_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          title: string
          description?: string | null
          status?: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'DONE'
          priority?: 'LOW' | 'MEDIUM' | 'HIGH'
          assignee_id?: string | null
          created_by?: string | null
          organization_id?: string | null
          due_date?: string | null
        }
        Update: {
          title?: string
          description?: string | null
          status?: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'DONE'
          priority?: 'LOW' | 'MEDIUM' | 'HIGH'
          assignee_id?: string | null
          due_date?: string | null
          completed_at?: string | null
        }
      }
      announcements: {
        Row: {
          id: string
          title: string
          content: string
          category: 'QA' | 'ENGINEERING' | 'MANAGEMENT' | 'GENERAL'
          is_important: boolean
          author_id: string | null
          organization_id: string | null
          likes_count: number
          comments_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          title: string
          content: string
          category?: 'QA' | 'ENGINEERING' | 'MANAGEMENT' | 'GENERAL'
          is_important?: boolean
          author_id?: string | null
          organization_id?: string | null
        }
        Update: {
          title?: string
          content?: string
          category?: 'QA' | 'ENGINEERING' | 'MANAGEMENT' | 'GENERAL'
          is_important?: boolean
        }
      }
      notifications: {
        Row: {
          id: string
          user_id: string | null
          title: string
          message: string
          type: string
          is_read: boolean
          is_archived: boolean
          related_id: string | null
          related_type: string | null
          created_at: string
          read_at: string | null
          archived_at: string | null
        }
        Insert: {
          user_id?: string | null
          title: string
          message: string
          type?: string
          related_id?: string | null
          related_type?: string | null
        }
        Update: {
          is_read?: boolean
          is_archived?: boolean
          read_at?: string | null
          archived_at?: string | null
        }
      }
      attendance_records: {
        Row: {
          id: string
          user_id: string | null
          status: 'CLOCKED_IN' | 'CLOCKED_OUT' | 'BREAK' | 'LUNCH'
          timestamp: string
          notes: string | null
          created_at: string
        }
        Insert: {
          user_id?: string | null
          status: 'CLOCKED_IN' | 'CLOCKED_OUT' | 'BREAK' | 'LUNCH'
          timestamp?: string
          notes?: string | null
        }
        Update: {
          status?: 'CLOCKED_IN' | 'CLOCKED_OUT' | 'BREAK' | 'LUNCH'
          notes?: string | null
        }
      }
      client_assignments: {
        Row: {
          id: string
          agent_id: string | null
          client_name: string
          assigned_date: string
          scheduled_date: string | null
          status: string
          assigned_by: string | null
          organization_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          agent_id?: string | null
          client_name: string
          assigned_date?: string
          scheduled_date?: string | null
          status?: string
          assigned_by?: string | null
        }
        Update: {
          agent_id?: string | null
          client_name?: string
          assigned_date?: string
          scheduled_date?: string | null
          status?: string
        }
      }
      clock_sessions: {
        Row: {
          id: string
          user_id: string | null
          status: string
          clock_in_time: string | null
          clock_out_time: string | null
          session_date: string
          organization_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          user_id?: string | null
          status: string
          clock_in_time?: string | null
          clock_out_time?: string | null
          session_date?: string
        }
        Update: {
          status?: string
          clock_in_time?: string | null
          clock_out_time?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'ADMIN' | 'AGENT' | 'USER'
      task_status: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'DONE'
      task_priority: 'LOW' | 'MEDIUM' | 'HIGH'
      announcement_category: 'QA' | 'ENGINEERING' | 'MANAGEMENT' | 'GENERAL'
      attendance_status: 'CLOCKED_IN' | 'CLOCKED_OUT' | 'BREAK' | 'LUNCH'
    }
  }
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T]
