import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';

interface CompanySettings {
  name: string;
  logo: string;
}

interface CompanyContextType {
  companySettings: CompanySettings;
  updateCompanySettings: (settings: Partial<CompanySettings>) => void;
}

const CompanyContext = createContext<CompanyContextType | undefined>(undefined);

export const useCompany = () => {
  const context = useContext(CompanyContext);
  if (context === undefined) {
    throw new Error('useCompany must be used within a CompanyProvider');
  }
  return context;
};

interface CompanyProviderProps {
  children: ReactNode;
}

export const CompanyProvider: React.FC<CompanyProviderProps> = ({ children }) => {
  const [companySettings, setCompanySettings] = useState<CompanySettings>({
    name: 'ZWIFTLLY',
    logo: ''
  });

  // Load company settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('company_settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setCompanySettings(parsed);
      } catch (error) {
        console.error('Failed to parse company settings:', error);
      }
    }
  }, []);

  const updateCompanySettings = (settings: Partial<CompanySettings>) => {
    const newSettings = { ...companySettings, ...settings };
    setCompanySettings(newSettings);
    localStorage.setItem('company_settings', JSON.stringify(newSettings));
  };

  return (
    <CompanyContext.Provider value={{ companySettings, updateCompanySettings }}>
      {children}
    </CompanyContext.Provider>
  );
};
