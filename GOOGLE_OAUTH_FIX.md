# 🔧 Google OAuth Configuration Fix

## 🚨 Current Issue
**Error**: "Access blocked: This app's request is invalid - Error 400: redirect_uri_mismatch"

**Cause**: The Google Cloud Console OAuth configuration doesn't include the production Vercel URL as an authorized redirect URI.

## 🎯 Solution: Update Google Cloud Console

### Step 1: Access Google Cloud Console
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Sign in with your Google account (<EMAIL>)
3. Select your project or create a new one for ZWIFTLLY

### Step 2: Navigate to OAuth Configuration
1. In the left sidebar, go to **APIs & Services** → **Credentials**
2. Find your OAuth 2.0 Client ID: `************-ikrhv4ipi0opcbd4e7eddvvfod3dcafk.apps.googleusercontent.com`
3. Click on the client ID to edit it

### Step 3: Add Authorized Redirect URIs
Add these URLs to the **Authorized redirect URIs** section:

```
https://uhasayjbnnhoxmyjdtme.supabase.co/auth/v1/callback
https://zwiftlly-team-management-system-e3tgdexbn-daddyrays-projects.vercel.app
https://zwiftlly-team-management-system-66tpvoe1o-daddyrays-projects.vercel.app
https://zwiftlly-team-management-system.vercel.app
http://localhost:5176
http://localhost:3000
http://localhost:5173
```

### Step 4: Add Authorized JavaScript Origins
Add these URLs to the **Authorized JavaScript origins** section:

```
https://zwiftlly-team-management-system-e3tgdexbn-daddyrays-projects.vercel.app
https://zwiftlly-team-management-system-66tpvoe1o-daddyrays-projects.vercel.app
https://zwiftlly-team-management-system.vercel.app
http://localhost:5176
http://localhost:3000
http://localhost:5173
```

### Step 5: Save Configuration
1. Click **Save** at the bottom of the page
2. Wait 5-10 minutes for changes to propagate

## 🚀 Current Deployment URLs

### Production (Live) - ✅ SUPABASE INTEGRATED
- **Main URL**: https://zwiftlly-team-management-system-66tpvoe1o-daddyrays-projects.vercel.app
- **Inspect**: https://vercel.com/daddyrays-projects/zwiftlly-team-management-system/JCotXJWfKgxtpDg3zqewiKkT5sxt
- **Database**: Supabase PostgreSQL (uhasayjbnnhoxmyjdtme.supabase.co)
- **Authentication**: Supabase Auth with Google OAuth
- **Real-time**: Supabase Realtime

### Development (Local)
- **Frontend**: http://localhost:5176
- **Database**: Supabase (same as production)

## 🔍 Verification Steps

After updating Google Cloud Console:

1. **Wait 5-10 minutes** for changes to propagate
2. **Clear browser cache** and cookies
3. **Try logging in** at the production URL
4. **Check browser console** for any remaining errors

## 🛠️ Alternative: Create New OAuth Client

If the above doesn't work, create a new OAuth client:

1. In Google Cloud Console → **APIs & Services** → **Credentials**
2. Click **+ CREATE CREDENTIALS** → **OAuth client ID**
3. Choose **Web application**
4. Name: `ZWIFTLLY Production`
5. Add the redirect URIs listed above
6. Update the `.env.production` file with the new client ID

## 📝 Environment Variables

Current production configuration in `.env.production`:
```
VITE_GOOGLE_CLIENT_ID=************-ikrhv4ipi0opcbd4e7eddvvfod3dcafk.apps.googleusercontent.com
VITE_APP_URL=https://zwiftlly-team-management-system.vercel.app
```

## 🎯 Next Steps

1. ✅ **Update Google Cloud Console** (follow steps above)
2. ⏳ **Wait for propagation** (5-10 minutes)
3. 🧪 **Test login** at production URL
4. 🔄 **Redeploy if needed** with updated client ID

The application build is successful and deployed - only the OAuth configuration needs to be updated!
