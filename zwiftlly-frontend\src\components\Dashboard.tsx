import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Phone, MessageSquare } from 'lucide-react';
import WeatherTimeDisplay from './WeatherTimeDisplay';
import TeamStatus from './TeamStatus';

const Dashboard: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [volumeWatcherMode, setVolumeWatcherMode] = useState<'calls' | 'sms'>('calls');

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-1 sm:gap-2 h-full">
      {/* Left Column - Main Content */}
      <div className="lg:col-span-2 space-y-1 sm:space-y-2">
        {/* Weather & Time Zones - Always on top */}
        <div className="dashboard-widget animate-fade-in-up animate-delay-0">
          <WeatherTimeDisplay />
        </div>

        {/* Team Status - Below weather */}
        <div className="dashboard-widget animate-fade-in-up animate-delay-150">
          <TeamStatus />
        </div>
      </div>

      {/* Right Column - Dashboard Widgets */}
      <div className="lg:col-span-1 space-y-1 sm:space-y-2">
        {/* Quick Stats */}
        <div className="dashboard-widget animate-slide-in-right animate-delay-300">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm sm:text-base">Who's in?</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-3 gap-1">
                {/* Team Member Tiles */}
                <div className="p-1.5 rounded-md bg-green-100 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-center">
                  <div className="text-xs font-medium text-green-800 dark:text-green-400 truncate">Louise</div>
                </div>
                <div className="p-1.5 rounded-md bg-yellow-100 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 text-center">
                  <div className="text-xs font-medium text-yellow-800 dark:text-yellow-400 truncate">Rose</div>
                </div>
                <div className="p-1.5 rounded-md bg-green-100 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-center">
                  <div className="text-xs font-medium text-green-800 dark:text-green-400 truncate">Bless-Ann</div>
                </div>
                <div className="p-1.5 rounded-md bg-gray-100 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-800 text-center">
                  <div className="text-xs font-medium text-gray-800 dark:text-gray-400 truncate">Pearl</div>
                </div>
                <div className="p-1.5 rounded-md bg-green-100 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-center">
                  <div className="text-xs font-medium text-green-800 dark:text-green-400 truncate">Christine G.</div>
                </div>
                <div className="p-1.5 rounded-md bg-yellow-100 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 text-center">
                  <div className="text-xs font-medium text-yellow-800 dark:text-yellow-400 truncate">Christine D.</div>
                </div>
                <div className="p-1.5 rounded-md bg-green-100 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-center">
                  <div className="text-xs font-medium text-green-800 dark:text-green-400 truncate">Ray</div>
                </div>
                <div className="p-1.5 rounded-md bg-green-100 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-center">
                  <div className="text-xs font-medium text-green-800 dark:text-green-400 truncate">Gizelle</div>
                </div>
                <div className="p-1.5 rounded-md bg-yellow-100 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 text-center">
                  <div className="text-xs font-medium text-yellow-800 dark:text-yellow-400 truncate">Dustin</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>



        {/* Volume Watcher */}
        <div className="dashboard-widget animate-slide-in-right animate-delay-450">
          <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm sm:text-base">Volume Watcher</CardTitle>
              <div className="flex items-center space-x-1">
                <Button
                  variant={volumeWatcherMode === 'calls' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setVolumeWatcherMode('calls')}
                  className="h-6 px-2 text-xs"
                >
                  <Phone className="w-3 h-3 mr-1" />
                  Calls
                </Button>
                <Button
                  variant={volumeWatcherMode === 'sms' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setVolumeWatcherMode('sms')}
                  className="h-6 px-2 text-xs"
                >
                  <MessageSquare className="w-3 h-3 mr-1" />
                  SMS
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div>
              {(() => {
                const allProjects = [
                  { project: 'Paschal', calls: 25, sms: 18, audited: false },
                  { project: 'Vredevoogd', calls: 15, sms: 12, audited: true },
                  { project: 'Classic', calls: 8, sms: 5, audited: false },
                  { project: 'Hurlburt', calls: 22, sms: 16, audited: false },
                  { project: 'Hoffmann', calls: 11, sms: 9, audited: false },
                  { project: 'Randazzo', calls: 7, sms: 4, audited: true },
                  { project: 'Flame Fox & Sons', calls: 19, sms: 14, audited: false },
                  { project: 'A1', calls: 13, sms: 8, audited: false },
                  { project: 'Mr. Sparky', calls: 6, sms: 3, audited: true },
                  { project: 'Robin Aire', calls: 21, sms: 17, audited: false },
                  { project: 'Blind & Sons', calls: 16, sms: 12, audited: false },
                  { project: 'Blue Sky', calls: 9, sms: 7, audited: false },
                  { project: 'Freguson', calls: 18, sms: 15, audited: false }
                ];

                const filteredProjects = allProjects
                  .filter(item => {
                    return !item.audited; // Show all unaudited projects
                  })
                  .sort((a, b) => {
                    const valueA = volumeWatcherMode === 'calls' ? a.calls : a.sms;
                    const valueB = volumeWatcherMode === 'calls' ? b.calls : b.sms;
                    return valueB - valueA; // Sort highest to lowest
                  });

                const shouldScroll = filteredProjects.length > 10;

                return (
                  <div className={`${shouldScroll ? 'max-h-80 overflow-y-auto scroll-smooth volume-trends-scroll' : ''}`}>
                    {filteredProjects.map((item, index) => {
                  const currentVolume = volumeWatcherMode === 'calls' ? item.calls : item.sms;
                  const getVolumeIcon = () => {
                    if (!item.audited && currentVolume >= 20) return { emoji: '🔴', class: 'pulse-fast' };
                    if (!item.audited && currentVolume >= 10) return { emoji: '🟡', class: 'pulse-medium' };
                    if (!item.audited && currentVolume < 10) return { emoji: '🔵', class: 'pulse-slow' };
                    return { emoji: '', class: '' };
                  };
                  const showPendingAudits = !item.audited; // Show for all unaudited projects

                  return (
                    <div key={index}>
                      <div className="flex items-center justify-between p-1.5">
                        {/* Left: Emoji and Project Name */}
                        <div className="flex items-center space-x-1 flex-1">
                          {getVolumeIcon().emoji && (
                            <span className={`text-xs ${getVolumeIcon().class}`}>{getVolumeIcon().emoji}</span>
                          )}
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                            {item.project}
                          </span>
                        </div>

                        {/* Right: Volume and Status */}
                        <div className="flex items-center space-x-2">
                          <span className="text-xs font-semibold text-foreground">
                            {currentVolume}
                          </span>
                          {showPendingAudits && (
                            <span className="text-xs text-orange-600 dark:text-orange-400">
                              Pending Audits
                            </span>
                          )}
                        </div>
                      </div>
                      {/* Horizontal separator line between projects */}
                      {index < filteredProjects.length - 1 && (
                        <hr className="border-t border-border my-1" />
                      )}
                    </div>
                  );
                    })}
                  </div>
                );
              })()}
            </div>
          </CardContent>
        </Card>
        </div>

        {/* System Status */}
        <div className="dashboard-widget animate-slide-in-right animate-delay-600">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm sm:text-base">System Status</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground text-xs">API</span>
                  <span className="flex items-center">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1"></div>
                    <span className="text-xs text-green-600">Online</span>
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground text-xs">Database</span>
                  <span className="flex items-center">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1"></div>
                    <span className="text-xs text-green-600">Connected</span>
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground text-xs">Updates</span>
                  <span className="flex items-center">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1"></div>
                    <span className="text-xs text-green-600">Active</span>
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
