import React, { useState, useEffect } from 'react'
import { AuthProvider, useAuth } from './AuthContext'
import { CompanyProvider, useCompany } from './contexts/CompanyContext'
import { DataProvider } from './contexts/DataContext'
import Login from './components/Login'
import Layout from './components/Layout'
import Dashboard from './components/Dashboard'
import Announcements from './components/Announcements'
import TaskBoard from './components/TaskBoard'
import Settings from './components/Settings'
import UserProfile from './components/UserProfile'

import KnowledgeBase from './components/KnowledgeBase'
import TeamSchedules from './components/TeamSchedules'
import ProtectedRoute from './components/ProtectedRoute'
import ZwiftllyLogo from './components/ZwiftllyLogo'



// Simple Error Boundary Component
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('App Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-red-50 flex items-center justify-center p-4">
          <div className="text-center space-y-4">
            <h1 className="text-2xl font-bold text-red-800">Something went wrong</h1>
            <p className="text-red-600">Error: {this.state.error?.message}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

const AppContent: React.FC = () => {
  const { user, isLoading } = useAuth()
  const [currentPage, setCurrentPage] = useState('dashboard')

  const [darkMode, setDarkMode] = useState(() => {
    // Check if user has a saved preference, otherwise use system preference
    const saved = localStorage.getItem('darkMode');
    if (saved !== null) {
      return JSON.parse(saved);
    }
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  });

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  useEffect(() => {
    // Apply dark mode class and save preference
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    localStorage.setItem('darkMode', JSON.stringify(darkMode));
  }, [darkMode]);



  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="mx-auto">
            <ZwiftllyLogo size="xl" animated={true} darkMode={darkMode} />
          </div>
          <div className="text-lg font-medium text-foreground">Loading ZWIFTLLY...</div>
        </div>
      </div>
    )
  }

  // Show login if not authenticated
  if (!user) {
    return <Login />
  }

  return (
    <Layout
      currentPage={currentPage}
      onPageChange={setCurrentPage}
      darkMode={darkMode}
      onToggleDarkMode={toggleDarkMode}
    >
      {/* Page Content */}
      {currentPage === 'dashboard' && (
        <ProtectedRoute permission="view_dashboard">
          <Dashboard />
        </ProtectedRoute>
      )}

      {currentPage === 'announcements' && (
        <ProtectedRoute permission="view_announcements">
          <Announcements />
        </ProtectedRoute>
      )}

      {currentPage === 'tasks' && (
        <ProtectedRoute permission="view_tasks">
          <TaskBoard />
        </ProtectedRoute>
      )}

      {currentPage === 'calendar' && (
        <ProtectedRoute permission="view_schedules">
          <TeamSchedules />
        </ProtectedRoute>
      )}

      {/* Placeholder pages for other routes */}


      {currentPage === 'knowledge' && (
        <ProtectedRoute permission="view_knowledge">
          <KnowledgeBase />
        </ProtectedRoute>
      )}

      {currentPage === 'settings' && (
        <ProtectedRoute permission="view_settings">
          <Settings darkMode={darkMode} onToggleDarkMode={toggleDarkMode} />
        </ProtectedRoute>
      )}

      {currentPage === 'profile' && (
        <ProtectedRoute permission="view_settings">
          <UserProfile darkMode={darkMode} />
        </ProtectedRoute>
      )}


    </Layout>
  )
}

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <CompanyProvider>
          <DataProvider>
            <AppContent />
          </DataProvider>
        </CompanyProvider>
      </AuthProvider>
    </ErrorBoundary>
  )
}

export default App
