import React, { useState, useEffect } from 'react';
import { 
  X, 
  Clock, 
  Coffee, 
  Play, 
  Pause, 
  Calendar,
  TrendingUp,
  User
} from 'lucide-react';
import { useAuth } from '../AuthContext';

interface UserProfilePopupProps {
  isOpen: boolean;
  onClose: () => void;
}

const UserProfilePopup: React.FC<UserProfilePopupProps> = ({ isOpen, onClose }) => {
  const { user } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [clockStatus, setClockStatus] = useState<'clocked-out' | 'clocked-in' | 'on-break'>('clocked-out');
  const [clockInTime, setClockInTime] = useState<Date | null>(null);
  const [breakStartTime, setBreakStartTime] = useState<Date | null>(null);
  const [totalWorkedTime, setTotalWorkedTime] = useState(0); // in minutes
  const [totalBreakTime, setTotalBreakTime] = useState(0); // in minutes

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleClockIn = () => {
    setClockStatus('clocked-in');
    setClockInTime(new Date());
  };

  const handleClockOut = () => {
    if (clockInTime) {
      const workedMinutes = Math.floor((new Date().getTime() - clockInTime.getTime()) / (1000 * 60));
      setTotalWorkedTime(prev => prev + workedMinutes);
    }
    setClockStatus('clocked-out');
    setClockInTime(null);
    setBreakStartTime(null);
  };

  const handleStartBreak = () => {
    setClockStatus('on-break');
    setBreakStartTime(new Date());
  };

  const handleEndBreak = () => {
    if (breakStartTime) {
      const breakMinutes = Math.floor((new Date().getTime() - breakStartTime.getTime()) / (1000 * 60));
      setTotalBreakTime(prev => prev + breakMinutes);
    }
    setClockStatus('clocked-in');
    setBreakStartTime(null);
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const getCurrentSessionTime = () => {
    if (!clockInTime) return '0h 0m';
    
    let sessionTime = new Date().getTime() - clockInTime.getTime();
    
    // Subtract break time if currently on break
    if (breakStartTime) {
      sessionTime -= (new Date().getTime() - breakStartTime.getTime());
    }
    
    const minutes = Math.floor(sessionTime / (1000 * 60));
    return formatTime(minutes);
  };

  const getStatusColor = () => {
    switch (clockStatus) {
      case 'clocked-in':
        return 'bg-green-500';
      case 'on-break':
        return 'bg-orange-500';
      case 'clocked-out':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = () => {
    switch (clockStatus) {
      case 'clocked-in':
        return 'Working';
      case 'on-break':
        return 'On Break';
      case 'clocked-out':
        return 'Clocked Out';
      default:
        return 'Unknown';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-card border border-border rounded-lg w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
              <User className="w-5 h-5 text-primary" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-foreground">
                {user?.email?.split('@')[0] || 'User'}
              </h2>
              <p className="text-sm text-muted-foreground">Agent Profile</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-muted rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-muted-foreground" />
          </button>
        </div>

        {/* Current Status */}
        <div className="p-6 border-b border-border">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <div className={`w-3 h-3 rounded-full ${getStatusColor()}`}></div>
              <span className="text-lg font-semibold text-foreground">{getStatusText()}</span>
            </div>
            
            <div className="text-2xl font-bold text-foreground mb-2">
              {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </div>
            
            <div className="text-sm text-muted-foreground">
              {currentTime.toLocaleDateString([], { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </div>
          </div>
        </div>

        {/* Clock Actions */}
        <div className="p-6 border-b border-border">
          <div className="space-y-3">
            {clockStatus === 'clocked-out' && (
              <button
                onClick={handleClockIn}
                className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Play className="w-4 h-4" />
                <span>Clock In</span>
              </button>
            )}

            {clockStatus === 'clocked-in' && (
              <div className="space-y-2">
                <button
                  onClick={handleStartBreak}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                >
                  <Coffee className="w-4 h-4" />
                  <span>Start Break</span>
                </button>
                <button
                  onClick={handleClockOut}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  <Pause className="w-4 h-4" />
                  <span>Clock Out</span>
                </button>
              </div>
            )}

            {clockStatus === 'on-break' && (
              <div className="space-y-2">
                <button
                  onClick={handleEndBreak}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Play className="w-4 h-4" />
                  <span>End Break</span>
                </button>
                <button
                  onClick={handleClockOut}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  <Pause className="w-4 h-4" />
                  <span>Clock Out</span>
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Daily Summary */}
        <div className="p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center space-x-2">
            <Calendar className="w-5 h-5" />
            <span>Today's Summary</span>
          </h3>

          <div className="space-y-4">
            {/* Current Session */}
            <div className="bg-muted rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm font-medium text-foreground">Current Session</span>
                </div>
                <span className="text-sm font-bold text-foreground">{getCurrentSessionTime()}</span>
              </div>
            </div>

            {/* Total Worked */}
            <div className="bg-muted rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium text-foreground">Total Worked</span>
                </div>
                <span className="text-sm font-bold text-green-600">{formatTime(totalWorkedTime)}</span>
              </div>
            </div>

            {/* Total Break Time */}
            <div className="bg-muted rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Coffee className="w-4 h-4 text-orange-600" />
                  <span className="text-sm font-medium text-foreground">Break Time</span>
                </div>
                <span className="text-sm font-bold text-orange-600">{formatTime(totalBreakTime)}</span>
              </div>
            </div>

            {/* Clock In Time */}
            {clockInTime && (
              <div className="bg-muted rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Play className="w-4 h-4 text-blue-600" />
                    <span className="text-sm font-medium text-foreground">Clocked In At</span>
                  </div>
                  <span className="text-sm font-bold text-blue-600">
                    {clockInTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfilePopup;
