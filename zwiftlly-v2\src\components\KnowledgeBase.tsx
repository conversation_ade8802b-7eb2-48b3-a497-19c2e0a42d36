import React, { useState } from 'react';
import { 
  BookOpen, 
  Search, 
  Plus, 
  Filter, 
  Download, 
  Upload,
  FileText,
  Video,
  Image,
  Link,
  Star,
  Eye,
  Edit,
  Trash2,
  Tag,
  Calendar,
  User,
  ChevronRight,
  Folder,
  FolderOpen
} from 'lucide-react';
import { useCompany } from '../contexts/CompanyContext';
import { useAuth } from '../AuthContext';

interface KnowledgeBaseProps {
  darkMode?: boolean;
}

const KnowledgeBase: React.FC<KnowledgeBaseProps> = ({ darkMode = false }) => {
  const { users } = useCompany();
  const { user } = useAuth();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);

  // Mock knowledge base data
  const categories = [
    { id: 'all', name: 'All Documents', count: 24 },
    { id: 'policies', name: 'Policies & Procedures', count: 8 },
    { id: 'training', name: 'Training Materials', count: 6 },
    { id: 'guides', name: 'User Guides', count: 5 },
    { id: 'templates', name: 'Templates', count: 3 },
    { id: 'faqs', name: 'FAQs', count: 2 }
  ];

  const folders = [
    { id: 'onboarding', name: 'Employee Onboarding', count: 12, icon: Folder },
    { id: 'hr-policies', name: 'HR Policies', count: 8, icon: Folder },
    { id: 'tech-docs', name: 'Technical Documentation', count: 15, icon: Folder },
    { id: 'training-videos', name: 'Training Videos', count: 6, icon: Folder }
  ];

  const documents = [
    {
      id: '1',
      title: 'Employee Handbook 2025',
      description: 'Complete guide to company policies, benefits, and procedures',
      type: 'document',
      category: 'policies',
      author: 'HR Department',
      createdAt: '2025-01-15',
      updatedAt: '2025-01-20',
      views: 156,
      starred: true,
      tags: ['HR', 'Policies', 'Handbook'],
      fileSize: '2.4 MB',
      icon: FileText
    },
    {
      id: '2',
      title: 'ZWIFTLLY System Training',
      description: 'Step-by-step video tutorial for new team members',
      type: 'video',
      category: 'training',
      author: 'Training Team',
      createdAt: '2025-01-10',
      updatedAt: '2025-01-18',
      views: 89,
      starred: false,
      tags: ['Training', 'Video', 'System'],
      fileSize: '45.2 MB',
      icon: Video
    },
    {
      id: '3',
      title: 'API Documentation',
      description: 'Complete API reference and integration guide',
      type: 'document',
      category: 'guides',
      author: 'Development Team',
      createdAt: '2025-01-08',
      updatedAt: '2025-01-22',
      views: 234,
      starred: true,
      tags: ['API', 'Development', 'Guide'],
      fileSize: '1.8 MB',
      icon: FileText
    },
    {
      id: '4',
      title: 'Team Performance Dashboard',
      description: 'Visual guide to understanding performance metrics',
      type: 'image',
      category: 'guides',
      author: 'Analytics Team',
      createdAt: '2025-01-12',
      updatedAt: '2025-01-19',
      views: 67,
      starred: false,
      tags: ['Performance', 'Dashboard', 'Guide'],
      fileSize: '3.1 MB',
      icon: Image
    },
    {
      id: '5',
      title: 'Meeting Notes Template',
      description: 'Standardized template for team meeting documentation',
      type: 'template',
      category: 'templates',
      author: 'Project Management',
      createdAt: '2025-01-05',
      updatedAt: '2025-01-16',
      views: 45,
      starred: false,
      tags: ['Template', 'Meetings', 'Documentation'],
      fileSize: '0.5 MB',
      icon: FileText
    },
    {
      id: '6',
      title: 'Frequently Asked Questions',
      description: 'Common questions and answers about ZWIFTLLY',
      type: 'document',
      category: 'faqs',
      author: 'Support Team',
      createdAt: '2025-01-03',
      updatedAt: '2025-01-21',
      views: 178,
      starred: true,
      tags: ['FAQ', 'Support', 'Help'],
      fileSize: '0.8 MB',
      icon: FileText
    }
  ];

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || doc.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'document':
        return 'text-blue-600 dark:text-blue-400';
      case 'video':
        return 'text-red-600 dark:text-red-400';
      case 'image':
        return 'text-green-600 dark:text-green-400';
      case 'template':
        return 'text-purple-600 dark:text-purple-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getTypeBg = (type: string) => {
    switch (type) {
      case 'document':
        return 'bg-blue-100 dark:bg-blue-900/20';
      case 'video':
        return 'bg-red-100 dark:bg-red-900/20';
      case 'image':
        return 'bg-green-100 dark:bg-green-900/20';
      case 'template':
        return 'bg-purple-100 dark:bg-purple-900/20';
      default:
        return 'bg-gray-100 dark:bg-gray-900/20';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <BookOpen className="w-8 h-8 text-primary" />
              <div>
                <h1 className="text-3xl font-bold text-foreground">Knowledge Base</h1>
                <p className="text-muted-foreground">Centralized documentation and resources</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button className="flex items-center space-x-2 px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors">
                <Upload className="w-4 h-4" />
                <span>Upload</span>
              </button>
              <button className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:opacity-90 transition-opacity">
                <Plus className="w-4 h-4" />
                <span>New Document</span>
              </button>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Search documents, guides, and resources..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full pl-10 pr-4 py-3 border rounded-lg ${
                    darkMode 
                      ? 'bg-gray-800 border-gray-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className={`px-4 py-3 border rounded-lg ${
                  darkMode 
                    ? 'bg-gray-800 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name} ({category.count})
                  </option>
                ))}
              </select>
              <div className="flex items-center space-x-1 bg-muted rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`px-3 py-2 text-sm rounded-md transition-colors ${
                    viewMode === 'grid'
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                >
                  Grid
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`px-3 py-2 text-sm rounded-md transition-colors ${
                    viewMode === 'list'
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                >
                  List
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar - Folders */}
          <div className="lg:col-span-1">
            <div className={`rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-lg font-semibold text-foreground">Folders</h2>
              </div>
              <div className="p-4">
                <div className="space-y-2">
                  {folders.map((folder) => {
                    const IconComponent = selectedFolder === folder.id ? FolderOpen : folder.icon;
                    return (
                      <button
                        key={folder.id}
                        onClick={() => setSelectedFolder(selectedFolder === folder.id ? null : folder.id)}
                        className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors ${
                          selectedFolder === folder.id
                            ? 'bg-primary/10 text-primary'
                            : 'hover:bg-muted text-muted-foreground hover:text-foreground'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <IconComponent className="w-5 h-5" />
                          <span className="text-sm font-medium">{folder.name}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs bg-muted px-2 py-1 rounded-full">{folder.count}</span>
                          <ChevronRight className="w-4 h-4" />
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* Main Content - Documents */}
          <div className="lg:col-span-3">
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {filteredDocuments.map((doc) => {
                  const IconComponent = doc.icon;
                  return (
                    <div
                      key={doc.id}
                      className={`rounded-lg border p-6 hover:shadow-lg transition-all duration-200 cursor-pointer ${
                        darkMode ? 'bg-gray-800 border-gray-700 hover:border-gray-600' : 'bg-white border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getTypeBg(doc.type)}`}>
                          <IconComponent className={`w-6 h-6 ${getTypeColor(doc.type)}`} />
                        </div>
                        <div className="flex items-center space-x-2">
                          {doc.starred && <Star className="w-4 h-4 text-yellow-500 fill-current" />}
                          <button className="p-1 hover:bg-muted rounded">
                            <Edit className="w-4 h-4 text-muted-foreground" />
                          </button>
                        </div>
                      </div>
                      
                      <h3 className="text-lg font-semibold text-foreground mb-2 line-clamp-2">{doc.title}</h3>
                      <p className="text-sm text-muted-foreground mb-4 line-clamp-2">{doc.description}</p>
                      
                      <div className="flex flex-wrap gap-1 mb-4">
                        {doc.tags.slice(0, 3).map((tag) => (
                          <span
                            key={tag}
                            className="inline-flex items-center px-2 py-1 text-xs bg-muted text-muted-foreground rounded-full"
                          >
                            <Tag className="w-3 h-3 mr-1" />
                            {tag}
                          </span>
                        ))}
                      </div>
                      
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-1">
                            <Eye className="w-3 h-3" />
                            <span>{doc.views}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <User className="w-3 h-3" />
                            <span>{doc.author}</span>
                          </div>
                        </div>
                        <span>{doc.fileSize}</span>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className={`rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                      <tr>
                        <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                          darkMode ? 'text-gray-300' : 'text-gray-500'
                        }`}>
                          Document
                        </th>
                        <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                          darkMode ? 'text-gray-300' : 'text-gray-500'
                        }`}>
                          Author
                        </th>
                        <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                          darkMode ? 'text-gray-300' : 'text-gray-500'
                        }`}>
                          Updated
                        </th>
                        <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                          darkMode ? 'text-gray-300' : 'text-gray-500'
                        }`}>
                          Views
                        </th>
                        <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                          darkMode ? 'text-gray-300' : 'text-gray-500'
                        }`}>
                          Size
                        </th>
                        <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                          darkMode ? 'text-gray-300' : 'text-gray-500'
                        }`}>
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {filteredDocuments.map((doc) => {
                        const IconComponent = doc.icon;
                        return (
                          <tr key={doc.id} className={`${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'} transition-colors`}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className={`w-10 h-10 rounded-lg flex items-center justify-center mr-4 ${getTypeBg(doc.type)}`}>
                                  <IconComponent className={`w-5 h-5 ${getTypeColor(doc.type)}`} />
                                </div>
                                <div>
                                  <div className="flex items-center">
                                    <div className="text-sm font-medium text-foreground">{doc.title}</div>
                                    {doc.starred && <Star className="w-4 h-4 text-yellow-500 fill-current ml-2" />}
                                  </div>
                                  <div className="text-sm text-muted-foreground">{doc.description}</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                              {doc.author}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                              {new Date(doc.updatedAt).toLocaleDateString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                              {doc.views}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                              {doc.fileSize}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                              <div className="flex items-center space-x-2">
                                <button className="p-1 hover:bg-muted rounded">
                                  <Eye className="w-4 h-4 text-muted-foreground" />
                                </button>
                                <button className="p-1 hover:bg-muted rounded">
                                  <Download className="w-4 h-4 text-muted-foreground" />
                                </button>
                                <button className="p-1 hover:bg-muted rounded">
                                  <Edit className="w-4 h-4 text-muted-foreground" />
                                </button>
                                <button className="p-1 hover:bg-muted rounded">
                                  <Trash2 className="w-4 h-4 text-red-500" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default KnowledgeBase;
