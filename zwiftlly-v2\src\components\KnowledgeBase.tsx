import React, { useState } from 'react';
import {
  BookOpen,
  Search,
  FileText,
  Folder,
  User,
  ChevronRight,
  TrendingUp,
  Eye
} from 'lucide-react';
import { useCompany } from '../contexts/CompanyContext';
import { useAuth } from '../AuthContext';

interface KnowledgeBaseProps {
  darkMode?: boolean;
}

const KnowledgeBase: React.FC<KnowledgeBaseProps> = ({ darkMode = false }) => {
  const { users } = useCompany();
  const { user } = useAuth();
  
  const [searchTerm, setSearchTerm] = useState('');

  // Knowledge base categories matching the design
  const categories = [
    {
      id: 'tenants',
      name: 'Tenants',
      count: 24,
      description: 'Tenant management, onboarding, and relationship guidelines',
      icon: User,
      color: 'blue'
    },
    {
      id: 'guidelines',
      name: 'Guidelines',
      count: 16,
      description: 'Best practices, standards, and operational guidelines',
      icon: BookOpen,
      color: 'green'
    },
    {
      id: 'policies',
      name: 'Policies',
      count: 8,
      description: 'Company policies, compliance, and regulatory documents',
      icon: FileText,
      color: 'purple'
    },
    {
      id: 'process',
      name: 'Process',
      count: 27,
      description: 'Workflows, procedures, and step-by-step processes',
      icon: Folder,
      color: 'orange'
    }
  ];

  // Trending documents
  const trendingDocuments = [
    { id: '1', name: 'Tenant Onboarding Checklist', category: 'Tenants', views: 147 },
    { id: '2', name: 'Data Privacy Policy', category: 'Policies', views: 132 },
    { id: '3', name: 'Quality Assurance Guidelines', category: 'Guidelines', views: 126 },
    { id: '4', name: 'Incident Response Process', category: 'Process', views: 114 },
    { id: '5', name: 'Tenant Communication Standards', category: 'Tenants', views: 103 }
  ];

  const getCategoryColor = (color: string) => {
    switch (color) {
      case 'blue':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'green':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'purple':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'orange':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };



  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Search Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div className="relative flex-1 max-w-md">
              <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search knowledge base..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-background border border-border rounded-lg text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
            <button className="flex items-center space-x-2 px-4 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors">
              <span>Search</span>
            </button>
          </div>
        </div>

        {/* Main Categories */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {categories.map((category) => {
            const Icon = category.icon;
            return (
              <div key={category.id} className="bg-card border border-border rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-3 rounded-lg ${getCategoryColor(category.color)}`}>
                      <Icon className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-foreground">{category.name}</h3>
                      <p className="text-sm text-muted-foreground">{category.count} documents</p>
                    </div>
                  </div>
                  <button className="flex items-center space-x-2 px-3 py-1.5 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors text-sm">
                    <span>View Documents</span>
                    <ChevronRight className="w-4 h-4" />
                  </button>
                </div>
                <p className="text-sm text-muted-foreground">{category.description}</p>
              </div>
            );
          })}
        </div>

        {/* Trending Documents */}
        <div className="bg-card border border-border rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-foreground">Trending Documents</h3>
            <TrendingUp className="w-5 h-5 text-primary" />
          </div>
          <div className="space-y-3">
            {trendingDocuments.map((doc) => (
              <div key={doc.id} className="flex items-center justify-between p-3 bg-muted rounded-lg hover:bg-muted/80 transition-colors cursor-pointer">
                <div className="flex items-center space-x-3">
                  <FileText className="w-5 h-5 text-primary" />
                  <div>
                    <p className="font-medium text-foreground">{doc.name}</p>
                    <p className="text-sm text-muted-foreground">{doc.category}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Eye className="w-4 h-4" />
                  <span>{doc.views}</span>
                </div>
              </div>
            ))}
          </div>
        </div>


      </div>
    </div>
  );
};

export default KnowledgeBase;
