import React, { useState } from 'react';
import {
  BookOpen,
  Search,
  FileText,
  Folder,
  User,
  ChevronRight,
  TrendingUp,
  Eye,
  Award
} from 'lucide-react';
import { useCompany } from '../contexts/CompanyContext';
import { useAuth } from '../AuthContext';

interface KnowledgeBaseProps {
  darkMode?: boolean;
}

const KnowledgeBase: React.FC<KnowledgeBaseProps> = ({ darkMode = false }) => {
  const { users } = useCompany();
  const { user } = useAuth();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const handleViewDocuments = (categoryId: string) => {
    setSelectedCategory(categoryId);
  };

  // Knowledge base categories matching the design
  const categories = [
    {
      id: 'tenants',
      name: 'Tenants',
      count: 24,
      description: 'Tenant management, onboarding, and relationship guidelines',
      icon: User,
      color: 'blue'
    },
    {
      id: 'guidelines',
      name: 'Guidelines',
      count: 16,
      description: 'Best practices, standards, and operational guidelines',
      icon: BookOpen,
      color: 'green'
    },
    {
      id: 'policies',
      name: 'Policies',
      count: 8,
      description: 'Company policies, compliance, and regulatory documents',
      icon: FileText,
      color: 'purple'
    },
    {
      id: 'process',
      name: 'Process',
      count: 27,
      description: 'Workflows, procedures, and step-by-step processes',
      icon: Folder,
      color: 'orange'
    }
  ];

  // Trending documents (top 5 most viewed)
  const trendingDocuments = [
    { id: '1', name: 'Tenant Onboarding Checklist', category: 'Tenants', views: 147, rank: 1 },
    { id: '2', name: 'Data Privacy Policy', category: 'Policies', views: 132, rank: 2 },
    { id: '3', name: 'Quality Assurance Guidelines', category: 'Guidelines', views: 126, rank: 3 },
    { id: '4', name: 'Incident Response Process', category: 'Process', views: 114, rank: 4 },
    { id: '5', name: 'Tenant Communication Standards', category: 'Tenants', views: 103, rank: 5 }
  ];

  const getRankEmoji = (rank: number) => {
    switch (rank) {
      case 1: return '🥇';
      case 2: return '🥈';
      case 3: return '🥉';
      case 4: return '🏅';
      case 5: return '🏅';
      default: return '📄';
    }
  };

  const getCategoryColor = (color: string) => {
    switch (color) {
      case 'blue':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'green':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'purple':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'orange':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };



  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header with centered search and trending documents */}
        <div className="mb-8">
          <div className="flex items-start justify-between mb-6">
            {/* Left spacer */}
            <div className="w-80"></div>

            {/* Centered Search Bar */}
            <div className="flex-1 max-w-2xl">
              <div className="text-center mb-6">
                <h1 className="text-3xl font-bold text-foreground mb-2">Knowledge Base</h1>
                <p className="text-muted-foreground">Search and explore our comprehensive documentation</p>
              </div>
              <div className="relative">
                <Search className="w-5 h-5 absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Search knowledge base..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 bg-background border border-border rounded-xl text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm"
                />
              </div>
            </div>

            {/* Trending Documents - Upper Right */}
            <div className="w-80 ml-6">
              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-4">
                  <TrendingUp className="w-5 h-5 text-primary" />
                  <h3 className="font-semibold text-foreground">Trending Documents</h3>
                </div>
                <div className="space-y-2">
                  {trendingDocuments.map((doc) => (
                    <div key={doc.id} className="flex items-center space-x-2 p-2 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer">
                      <span className="text-lg">{getRankEmoji(doc.rank)}</span>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-foreground truncate">{doc.name}</p>
                        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                          <Eye className="w-3 h-3" />
                          <span>{doc.views} views</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Categories */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {categories.map((category) => {
            const Icon = category.icon;
            return (
              <div key={category.id} className="bg-card border border-border rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-3 rounded-lg ${getCategoryColor(category.color)}`}>
                      <Icon className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-foreground">{category.name}</h3>
                      <p className="text-sm text-muted-foreground">{category.count} documents</p>
                    </div>
                  </div>
                  <button
                    onClick={() => handleViewDocuments(category.id)}
                    className="flex items-center space-x-2 px-3 py-1.5 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors text-sm"
                  >
                    <span>View Documents</span>
                    <ChevronRight className="w-4 h-4" />
                  </button>
                </div>
                <p className="text-sm text-muted-foreground">{category.description}</p>
              </div>
            );
          })}
        </div>

        {/* Document View */}
        {selectedCategory && (
          <div className="mt-8">
            <DocumentView
              category={categories.find(c => c.id === selectedCategory)!}
              onBack={() => setSelectedCategory(null)}
            />
          </div>
        )}


      </div>
    </div>
  );
};

// Document View Component
interface DocumentViewProps {
  category: any;
  onBack: () => void;
}

const DocumentView: React.FC<DocumentViewProps> = ({ category, onBack }) => {
  return (
    <div className="bg-card border border-border rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <button
            onClick={onBack}
            className="p-2 hover:bg-muted rounded-lg transition-colors"
          >
            <ChevronRight className="w-5 h-5 rotate-180" />
          </button>
          <div>
            <h2 className="text-xl font-semibold text-foreground">{category.name} Documents</h2>
            <p className="text-sm text-muted-foreground">{category.description}</p>
          </div>
        </div>
      </div>

      <div className="text-center py-12">
        <div className="text-6xl mb-4">📚</div>
        <h3 className="text-xl font-semibold text-foreground mb-2">Ready for Production</h3>
        <p className="text-muted-foreground max-w-md mx-auto">
          This section is ready for real documents to be uploaded.
          No mock data - connect your document management system or upload files directly.
        </p>
        <div className="mt-6 space-y-2">
          <p className="text-sm text-muted-foreground">
            <strong>Category:</strong> {category.name}
          </p>
          <p className="text-sm text-muted-foreground">
            <strong>Expected Documents:</strong> {category.count}
          </p>
        </div>
      </div>
    </div>
  );
};

export default KnowledgeBase;
