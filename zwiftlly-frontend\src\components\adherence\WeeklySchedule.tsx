import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, Edit3 } from 'lucide-react';
import ScheduleEditModal from './ScheduleEditModal';

interface ScheduleEntry {
  day: string;
  date: string;
  shift: string;
  isOff?: boolean;
}

interface TeamMemberSchedule {
  id: string;
  name: string;
  schedule: ScheduleEntry[];
}

const WeeklySchedule: React.FC = () => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState<{ id: string; name: string } | null>(null);

  // Get current day based on real-time timezone data
  const getCurrentDayIndex = () => {
    const now = new Date();
    const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    // Convert to our array index (0 = Monday, 1 = Tuesday, etc.)
    return dayOfWeek === 0 ? 6 : dayOfWeek - 1;
  };

  const currentDayIndex = getCurrentDayIndex();

  const handleEditSchedule = (member: { id: string; name: string }) => {
    setSelectedMember(member);
    setIsEditModalOpen(true);
  };

  const handleSaveSchedule = (memberId: string, schedules: any[]) => {
    console.log('Saving schedule for member:', memberId, schedules);
    // Here you would typically save to your backend
    // Example: await saveSchedule(memberId, schedules);
    setIsEditModalOpen(false);
    setSelectedMember(null);
  };

  const handleCloseModal = () => {
    setIsEditModalOpen(false);
    setSelectedMember(null);
  };

  const weekDays = [
    { day: 'Monday', date: 'Jun 17, 2025' },
    { day: 'Tuesday', date: 'Jun 18, 2025' },
    { day: 'Wednesday', date: 'Jun 19, 2025' },
    { day: 'Thursday', date: 'Jun 20, 2025' },
    { day: 'Friday', date: 'Jun 21, 2025' },
    { day: 'Saturday', date: 'Jun 22, 2025' },
    { day: 'Sunday', date: 'Jun 23, 2025' }
  ];

  const teamSchedules: TeamMemberSchedule[] = [
    {
      id: '1',
      name: 'Louise',
      schedule: [
        { day: 'Monday', date: 'Jun 17', shift: '8:00 AM - 5:00 PM' },
        { day: 'Tuesday', date: 'Jun 18', shift: '8:00 AM - 5:00 PM' },
        { day: 'Wednesday', date: 'Jun 19', shift: '8:00 AM - 5:00 PM' },
        { day: 'Thursday', date: 'Jun 20', shift: '8:00 AM - 5:00 PM' },
        { day: 'Friday', date: 'Jun 21', shift: '8:00 AM - 5:00 PM' },
        { day: 'Saturday', date: 'Jun 22', shift: 'Off', isOff: true },
        { day: 'Sunday', date: 'Jun 23', shift: 'Off', isOff: true }
      ]
    },
    {
      id: '2',
      name: 'Rose',
      schedule: [
        { day: 'Monday', date: 'Jun 17', shift: '9:00 AM - 6:00 PM' },
        { day: 'Tuesday', date: 'Jun 18', shift: '9:00 AM - 6:00 PM' },
        { day: 'Wednesday', date: 'Jun 19', shift: '9:00 AM - 6:00 PM' },
        { day: 'Thursday', date: 'Jun 20', shift: '9:00 AM - 6:00 PM' },
        { day: 'Friday', date: 'Jun 21', shift: '9:00 AM - 6:00 PM' },
        { day: 'Saturday', date: 'Jun 22', shift: 'Off', isOff: true },
        { day: 'Sunday', date: 'Jun 23', shift: 'Off', isOff: true }
      ]
    },
    {
      id: '3',
      name: 'Bless-Ann',
      schedule: [
        { day: 'Monday', date: 'Jun 17', shift: '10:00 AM - 7:00 PM' },
        { day: 'Tuesday', date: 'Jun 18', shift: '10:00 AM - 7:00 PM' },
        { day: 'Wednesday', date: 'Jun 19', shift: '10:00 AM - 7:00 PM' },
        { day: 'Thursday', date: 'Jun 20', shift: '10:00 AM - 7:00 PM' },
        { day: 'Friday', date: 'Jun 21', shift: '10:00 AM - 7:00 PM' },
        { day: 'Saturday', date: 'Jun 22', shift: 'Off', isOff: true },
        { day: 'Sunday', date: 'Jun 23', shift: 'Off', isOff: true }
      ]
    },
    {
      id: '4',
      name: 'Pearl',
      schedule: [
        { day: 'Monday', date: 'Jun 17', shift: 'Off', isOff: true },
        { day: 'Tuesday', date: 'Jun 18', shift: '8:00 AM - 5:00 PM' },
        { day: 'Wednesday', date: 'Jun 19', shift: '8:00 AM - 5:00 PM' },
        { day: 'Thursday', date: 'Jun 20', shift: '8:00 AM - 5:00 PM' },
        { day: 'Friday', date: 'Jun 21', shift: '8:00 AM - 5:00 PM' },
        { day: 'Saturday', date: 'Jun 22', shift: '8:00 AM - 5:00 PM' },
        { day: 'Sunday', date: 'Jun 23', shift: 'Off', isOff: true }
      ]
    },
    {
      id: '5',
      name: 'Christine G.',
      schedule: [
        { day: 'Monday', date: 'Jun 17', shift: '7:00 AM - 4:00 PM' },
        { day: 'Tuesday', date: 'Jun 18', shift: '7:00 AM - 4:00 PM' },
        { day: 'Wednesday', date: 'Jun 19', shift: '7:00 AM - 4:00 PM' },
        { day: 'Thursday', date: 'Jun 20', shift: '7:00 AM - 4:00 PM' },
        { day: 'Friday', date: 'Jun 21', shift: '7:00 AM - 4:00 PM' },
        { day: 'Saturday', date: 'Jun 22', shift: 'Off', isOff: true },
        { day: 'Sunday', date: 'Jun 23', shift: 'Off', isOff: true }
      ]
    },
    {
      id: '6',
      name: 'Ray',
      schedule: [
        { day: 'Monday', date: 'Jun 17', shift: '6:00 AM - 3:00 PM' },
        { day: 'Tuesday', date: 'Jun 18', shift: '6:00 AM - 3:00 PM' },
        { day: 'Wednesday', date: 'Jun 19', shift: '6:00 AM - 3:00 PM' },
        { day: 'Thursday', date: 'Jun 20', shift: '6:00 AM - 3:00 PM' },
        { day: 'Friday', date: 'Jun 21', shift: '6:00 AM - 3:00 PM' },
        { day: 'Saturday', date: 'Jun 22', shift: 'Off', isOff: true },
        { day: 'Sunday', date: 'Jun 23', shift: 'Off', isOff: true }
      ]
    }
  ];

  return (
    <Card className="bg-card rounded-xl shadow-lg border border-border/50 overflow-hidden">
      <CardHeader className="p-6 border-b border-border/50 bg-gradient-to-r from-background to-muted/20 animate-fade-in animate-delay-0">
        <CardTitle className="text-xl flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-muted/20 rounded-xl shadow-sm border border-border/30">
              <Calendar className="w-6 h-6 text-foreground" />
            </div>
            <div>
              <span className="text-foreground font-bold text-xl">Weekly Schedule</span>
              <div className="text-sm text-muted-foreground font-medium">Team availability overview</div>
            </div>
          </div>
          <div className="text-sm text-muted-foreground bg-muted/20 px-4 py-2 rounded-xl border border-border/30 shadow-sm">
            Week of June 17-23, 2025
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="w-full animate-fade-in animate-delay-150">
          <table className="w-full">
            <thead>
              <tr className="bg-muted/10 border-b border-border">
                <th className="text-left p-4 font-bold text-foreground w-48 border-r border-border/50">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-primary/60 rounded-full shadow-sm"></div>
                    <span className="text-foreground font-bold">Team Member</span>
                  </div>
                </th>
                {weekDays.map((day, index) => (
                  <th key={day.day} className={`text-center p-4 font-bold border-r border-border/50 last:border-r-0 transition-all duration-300 ${
                    index === currentDayIndex
                      ? 'bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800'
                      : 'bg-background hover:bg-muted/20'
                  }`}>
                    <div className="space-y-1">
                      <div className={`font-bold transition-all duration-300 ${
                        index === currentDayIndex
                          ? 'text-blue-600 dark:text-blue-400 bg-blue-100/80 dark:bg-blue-800/50 px-3 py-1.5 rounded-lg shadow-sm border border-blue-200/50 dark:border-blue-700/50'
                          : 'text-foreground'
                      }`}>
                        {day.day}
                        {index === currentDayIndex && (
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mx-auto mt-1"></div>
                        )}
                      </div>
                      <div className={`text-xs font-medium px-2 py-1 rounded-full border transition-all duration-300 ${
                        index === currentDayIndex
                          ? 'text-blue-600 dark:text-blue-400 bg-blue-100/70 dark:bg-blue-800/30 border-blue-200/50 dark:border-blue-700/50'
                          : 'text-muted-foreground bg-muted/20 border-border/30'
                      }`}>
                        {day.date}
                      </div>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {teamSchedules.map((member, memberIndex) => (
                <tr key={member.id} className={`border-b border-border/50 hover:bg-muted/10 transition-all duration-300 ${
                  memberIndex % 2 === 0 ? 'bg-muted/5' : 'bg-background'
                }`}>
                  <td className="p-4 font-semibold border-r border-border/50 w-48">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-9 h-9 bg-muted rounded-full flex items-center justify-center shadow-sm border border-border/30">
                          <span className="text-xs font-bold text-foreground">
                            {member.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                        <span className="text-foreground font-medium">{member.name}</span>
                      </div>
                      <Button
                        onClick={() => handleEditSchedule({ id: member.id, name: member.name })}
                        variant="ghost"
                        size="sm"
                        className="w-8 h-8 p-0 hover:bg-muted/50 transition-colors"
                        title={`Edit ${member.name}'s schedule`}
                      >
                        <Edit3 className="w-4 h-4 text-muted-foreground hover:text-foreground transition-colors" />
                      </Button>
                    </div>
                  </td>
                  {member.schedule.map((entry, index) => (
                    <td key={index} className={`p-3 text-center border-r border-border/50 last:border-r-0 transition-all duration-300 ${
                      index === currentDayIndex
                        ? 'bg-blue-50/40 dark:bg-blue-950/10'
                        : ''
                    }`}>
                      {entry.isOff ? (
                        <div className={`inline-flex items-center justify-center px-3 py-2 rounded-lg text-xs font-semibold shadow-sm min-w-[60px] transition-all duration-300 ${
                          index === currentDayIndex
                            ? 'bg-blue-100/80 dark:bg-blue-800/60 text-blue-600 dark:text-blue-400 border border-blue-200/60 dark:border-blue-700/60'
                            : 'bg-muted/50 text-muted-foreground border border-border/40'
                        }`}>
                          <div className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
                            index === currentDayIndex
                              ? 'bg-blue-500 dark:bg-blue-400'
                              : 'bg-muted-foreground/60'
                          }`}></div>
                          Off
                        </div>
                      ) : (
                        <div className={`inline-flex items-center justify-center px-3 py-2 rounded-lg text-white text-xs font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 min-w-[120px] ${
                          index === currentDayIndex
                            ? 'bg-gradient-to-br from-blue-500/95 to-indigo-600/95 border border-blue-600/30 ring-2 ring-blue-400/20'
                            : 'bg-gradient-to-br from-emerald-500/90 to-teal-600/90 border border-emerald-600/20'
                        }`}>
                          <div className="w-1.5 h-1.5 bg-white/80 rounded-full mr-1.5"></div>
                          <span className="tracking-wide">
                            {entry.shift}
                          </span>
                        </div>
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Enhanced Schedule Summary Footer */}
        <div className="p-6 bg-gradient-to-r from-muted/20 via-background to-muted/20 border-t border-border/50 animate-slide-in-right animate-delay-300">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-3 px-3 py-2 bg-emerald-100/50 dark:bg-emerald-900/20 rounded-lg border border-emerald-200/50 dark:border-emerald-700/50">
                <div className="w-3 h-3 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-full shadow-sm"></div>
                <span className="text-muted-foreground">Scheduled Shifts: <span className="font-bold text-emerald-700 dark:text-emerald-300">42</span></span>
              </div>
              <div className="flex items-center space-x-3 px-3 py-2 bg-muted/30 rounded-lg border border-border/40">
                <div className="w-3 h-3 bg-gradient-to-br from-muted-foreground/60 to-muted-foreground/80 rounded-full shadow-sm"></div>
                <span className="text-muted-foreground">Days Off: <span className="font-bold text-foreground">16</span></span>
              </div>
            </div>
            <div className="px-4 py-2 bg-blue-100/50 dark:bg-blue-900/20 rounded-lg border border-blue-200/50 dark:border-blue-700/50">
              <span className="text-muted-foreground">Total Coverage: <span className="font-bold text-blue-700 dark:text-blue-300">72.4%</span></span>
            </div>
          </div>
        </div>
      </CardContent>

      {/* Schedule Edit Modal */}
      <ScheduleEditModal
        isOpen={isEditModalOpen}
        onClose={handleCloseModal}
        member={selectedMember}
        onSave={handleSaveSchedule}
      />
    </Card>
  );
};

export default WeeklySchedule;
