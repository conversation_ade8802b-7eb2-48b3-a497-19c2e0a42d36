/* Task Board Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-10px);
  }
  70% {
    transform: translateY(-5px);
  }
  90% {
    transform: translateY(-2px);
  }
}

.task-card {
  animation: slideInUp 0.3s ease-out;
}

.task-card:hover {
  animation: pulse 0.3s ease-in-out;
}

.drag-preview {
  transform: rotate(5deg);
  opacity: 0.8;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.drop-zone-active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border: 2px dashed rgba(59, 130, 246, 0.5);
  animation: pulse 1s infinite;
}

.quick-action-menu {
  animation: fadeIn 0.2s ease-out;
}

.priority-high {
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.priority-medium {
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.priority-low {
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
}

.card-hover-effect {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-effect:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.stagger-animation {
  animation: slideInUp 0.5s ease-out;
}

.stagger-animation:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation:nth-child(5) { animation-delay: 0.5s; }

.column-header {
  transition: all 0.3s ease;
}

.column-header:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.add-card-button {
  transition: all 0.2s ease;
}

.add-card-button:hover {
  animation: bounce 0.6s;
}
