import React from 'react';

interface TimeHeaderProps {
  startHour: number;
  endHour: number;
  format24Hour?: boolean;
}

const TimeHeader: React.FC<TimeHeaderProps> = ({ 
  startHour = 8, 
  endHour = 17, 
  format24Hour = false 
}) => {
  const generateTimeSlots = () => {
    const slots = [];
    for (let hour = startHour; hour <= endHour; hour++) {
      slots.push(hour);
    }
    return slots;
  };

  const formatTime = (hour: number) => {
    if (format24Hour) {
      return `${hour.toString().padStart(2, '0')}:00`;
    } else {
      if (hour === 0) return '12AM';
      if (hour === 12) return '12PM';
      if (hour < 12) return `${hour}AM`;
      return `${hour - 12}PM`;
    }
  };

  const timeSlots = generateTimeSlots();

  return (
    <div className="bg-card rounded-lg shadow-sm border mb-4">
      {/* Header with current date */}
      <div className="p-3 border-b border-border">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-foreground">Live Adherence Board</h3>
          <div className="text-sm text-muted-foreground">
            {new Date().toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </div>
        </div>
      </div>

      {/* Time slots header */}
      <div className="flex">
        {/* Team member column header */}
        <div className="w-48 p-3 border-r border-border bg-muted/30 flex-shrink-0">
          <span className="text-sm font-semibold text-foreground">Team Members</span>
        </div>

        {/* Time slots */}
        <div className="flex min-w-max">
          {timeSlots.map((hour, index) => (
            <div
              key={hour}
              className={`w-24 p-3 text-center border-r border-border last:border-r-0 flex-shrink-0 ${
                index % 2 === 0 ? 'bg-muted/10' : 'bg-background'
              }`}
            >
              <span className="text-sm font-medium text-foreground">
                {formatTime(hour)}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TimeHeader;
