import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './SupabaseAuthContext';

interface DataState {
  users: any[];
  attendance: any[];
  tasks: any[];
  announcements: any[];
  loading: {
    users: boolean;
    attendance: boolean;
    tasks: boolean;
    announcements: boolean;
  };
  errors: {
    users: string | null;
    attendance: string | null;
    tasks: string | null;
    announcements: string | null;
  };
}

interface DataContextType extends DataState {
  refreshUsers: () => Promise<void>;
  refreshAttendance: () => Promise<void>;
  refreshTasks: () => Promise<void>;
  refreshAnnouncements: () => Promise<void>;
  refreshAll: () => Promise<void>;
  clearErrors: () => void;
  // User functions
  updateUser: (userId: string, data: any) => Promise<void>;
  deleteUser: (userId: string) => Promise<void>;
  // Task functions
  createTask: (data: any) => Promise<any>;
  updateTask: (taskId: string, data: any) => Promise<any>;
  deleteTask: (taskId: string) => Promise<void>;
  // Announcement functions
  createAnnouncement: (data: any) => Promise<any>;
  updateAnnouncement: (announcementId: string, data: any) => Promise<any>;
  deleteAnnouncement: (announcementId: string) => Promise<void>;
  addComment: (announcementId: string, data: any) => Promise<any>;
  // Attendance functions
  clockAction: (data: any) => Promise<any>;
  updateAttendanceRecord: (recordId: string, data: any) => Promise<void>;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export const useData = (): DataContextType => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export const DataProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  
  const [state, setState] = useState<DataState>({
    users: [],
    attendance: [],
    tasks: [],
    announcements: [],
    loading: {
      users: false,
      attendance: false,
      tasks: false,
      announcements: false
    },
    errors: {
      users: null,
      attendance: null,
      tasks: null,
      announcements: null
    }
  });

  // Helper functions
  const setLoading = (key: keyof DataState['loading'], loading: boolean) => {
    setState(prev => ({
      ...prev,
      loading: { ...prev.loading, [key]: loading }
    }));
  };

  const setError = (key: keyof DataState['errors'], error: string | null) => {
    setState(prev => ({
      ...prev,
      errors: { ...prev.errors, [key]: error }
    }));
  };

  // Users functions
  const refreshUsers = async () => {
    if (!user) return;
    
    setLoading('users', true);
    setError('users', null);
    
    try {
      const { data, error } = await supabase
        .from('users')
        .select(`
          *,
          organizations (*)
        `)
        .eq('is_active', true);

      if (error) throw error;

      setState(prev => ({
        ...prev,
        users: data || []
      }));
    } catch (error: any) {
      setError('users', error.message);
    } finally {
      setLoading('users', false);
    }
  };

  const updateUser = async (userId: string, data: any) => {
    try {
      const { data: updatedUser, error } = await supabase
        .from('users')
        .update(data)
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;

      setState(prev => ({
        ...prev,
        users: prev.users.map(u => u.id === userId ? updatedUser : u)
      }));
    } catch (error: any) {
      setError('users', error.message);
      throw error;
    }
  };

  const deleteUser = async (userId: string) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ is_active: false })
        .eq('id', userId);

      if (error) throw error;

      setState(prev => ({
        ...prev,
        users: prev.users.filter(u => u.id !== userId)
      }));
    } catch (error: any) {
      setError('users', error.message);
      throw error;
    }
  };

  // Attendance functions
  const refreshAttendance = async () => {
    if (!user) return;
    
    setLoading('attendance', true);
    setError('attendance', null);
    
    try {
      const { data, error } = await supabase
        .from('attendance_records')
        .select(`
          *,
          users (name, email)
        `)
        .order('date', { ascending: false });

      if (error) throw error;

      setState(prev => ({
        ...prev,
        attendance: data || []
      }));
    } catch (error: any) {
      setError('attendance', error.message);
    } finally {
      setLoading('attendance', false);
    }
  };

  const clockAction = async (data: any) => {
    try {
      // Call Supabase function for clock action
      const { data: result, error } = await supabase.rpc('handle_clock_action', {
        action_type: data.action,
        location: data.location || null
      });

      if (error) throw error;

      // Refresh attendance data
      await refreshAttendance();
      
      return { success: true, data: result };
    } catch (error: any) {
      setError('attendance', error.message);
      throw error;
    }
  };

  const updateAttendanceRecord = async (recordId: string, data: any) => {
    try {
      const { data: updatedRecord, error } = await supabase
        .from('attendance_records')
        .update(data)
        .eq('id', recordId)
        .select()
        .single();

      if (error) throw error;

      setState(prev => ({
        ...prev,
        attendance: prev.attendance.map(r => r.id === recordId ? updatedRecord : r)
      }));
    } catch (error: any) {
      setError('attendance', error.message);
      throw error;
    }
  };

  // Tasks functions
  const refreshTasks = async () => {
    if (!user) return;
    
    setLoading('tasks', true);
    setError('tasks', null);
    
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select(`
          *,
          assignee:users!assignee_id (name, email),
          creator:users!created_by (name, email)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      setState(prev => ({
        ...prev,
        tasks: data || []
      }));
    } catch (error: any) {
      setError('tasks', error.message);
    } finally {
      setLoading('tasks', false);
    }
  };

  const createTask = async (data: any) => {
    try {
      const { data: newTask, error } = await supabase
        .from('tasks')
        .insert({
          ...data,
          created_by: user?.id
        })
        .select(`
          *,
          assignee:users!assignee_id (name, email),
          creator:users!created_by (name, email)
        `)
        .single();

      if (error) throw error;

      setState(prev => ({
        ...prev,
        tasks: [newTask, ...prev.tasks]
      }));
      
      return { success: true, data: newTask };
    } catch (error: any) {
      setError('tasks', error.message);
      throw error;
    }
  };

  const updateTask = async (taskId: string, data: any) => {
    try {
      const { data: updatedTask, error } = await supabase
        .from('tasks')
        .update(data)
        .eq('id', taskId)
        .select(`
          *,
          assignee:users!assignee_id (name, email),
          creator:users!created_by (name, email)
        `)
        .single();

      if (error) throw error;

      setState(prev => ({
        ...prev,
        tasks: prev.tasks.map(t => t.id === taskId ? updatedTask : t)
      }));
      
      return { success: true, data: updatedTask };
    } catch (error: any) {
      setError('tasks', error.message);
      throw error;
    }
  };

  const deleteTask = async (taskId: string) => {
    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', taskId);

      if (error) throw error;

      setState(prev => ({
        ...prev,
        tasks: prev.tasks.filter(t => t.id !== taskId)
      }));
    } catch (error: any) {
      setError('tasks', error.message);
      throw error;
    }
  };

  // Announcements functions
  const refreshAnnouncements = async () => {
    if (!user) return;
    
    setLoading('announcements', true);
    setError('announcements', null);
    
    try {
      const { data, error } = await supabase
        .from('announcements')
        .select(`
          *,
          author:users!created_by (name, email, picture),
          comments (
            *,
            author:users!user_id (name, email, picture)
          )
        `)
        .eq('is_published', true)
        .order('published_at', { ascending: false });

      if (error) throw error;

      setState(prev => ({
        ...prev,
        announcements: data || []
      }));
    } catch (error: any) {
      setError('announcements', error.message);
    } finally {
      setLoading('announcements', false);
    }
  };

  const createAnnouncement = async (data: any) => {
    try {
      const { data: newAnnouncement, error } = await supabase
        .from('announcements')
        .insert({
          ...data,
          created_by: user?.id,
          published_at: data.is_published ? new Date().toISOString() : null
        })
        .select(`
          *,
          author:users!created_by (name, email, picture)
        `)
        .single();

      if (error) throw error;

      setState(prev => ({
        ...prev,
        announcements: [newAnnouncement, ...prev.announcements]
      }));
      
      return { success: true, data: newAnnouncement };
    } catch (error: any) {
      setError('announcements', error.message);
      throw error;
    }
  };

  const updateAnnouncement = async (announcementId: string, data: any) => {
    try {
      const { data: updatedAnnouncement, error } = await supabase
        .from('announcements')
        .update(data)
        .eq('id', announcementId)
        .select(`
          *,
          author:users!created_by (name, email, picture)
        `)
        .single();

      if (error) throw error;

      setState(prev => ({
        ...prev,
        announcements: prev.announcements.map(a => a.id === announcementId ? updatedAnnouncement : a)
      }));
      
      return { success: true, data: updatedAnnouncement };
    } catch (error: any) {
      setError('announcements', error.message);
      throw error;
    }
  };

  const deleteAnnouncement = async (announcementId: string) => {
    try {
      const { error } = await supabase
        .from('announcements')
        .delete()
        .eq('id', announcementId);

      if (error) throw error;

      setState(prev => ({
        ...prev,
        announcements: prev.announcements.filter(a => a.id !== announcementId)
      }));
    } catch (error: any) {
      setError('announcements', error.message);
      throw error;
    }
  };

  const addComment = async (announcementId: string, data: any) => {
    try {
      const { data: newComment, error } = await supabase
        .from('comments')
        .insert({
          ...data,
          announcement_id: announcementId,
          user_id: user?.id
        })
        .select(`
          *,
          author:users!user_id (name, email, picture)
        `)
        .single();

      if (error) throw error;

      // Update the announcement with the new comment
      setState(prev => ({
        ...prev,
        announcements: prev.announcements.map(a => 
          a.id === announcementId 
            ? { ...a, comments: [newComment, ...(a.comments || [])] }
            : a
        )
      }));
      
      return { success: true, data: newComment };
    } catch (error: any) {
      setError('announcements', error.message);
      throw error;
    }
  };

  // General functions
  const refreshAll = async () => {
    await Promise.all([
      refreshUsers(),
      refreshAttendance(),
      refreshTasks(),
      refreshAnnouncements()
    ]);
  };

  const clearErrors = () => {
    setState(prev => ({
      ...prev,
      errors: {
        users: null,
        attendance: null,
        tasks: null,
        announcements: null
      }
    }));
  };

  // Initial data loading
  useEffect(() => {
    if (user) {
      refreshAll();
    }
  }, [user]);

  const value: DataContextType = {
    ...state,
    refreshUsers,
    updateUser,
    deleteUser,
    refreshAttendance,
    clockAction,
    updateAttendanceRecord,
    refreshTasks,
    createTask,
    updateTask,
    deleteTask,
    refreshAnnouncements,
    createAnnouncement,
    updateAnnouncement,
    deleteAnnouncement,
    addComment,
    refreshAll,
    clearErrors
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};

export default DataProvider;
