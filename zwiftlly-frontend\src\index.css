@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100;
  }
  :root {
    --background: 0 0% 100%; /* White */
    --foreground: 0 0% 15%; /* Light Black */
    --card: 0 0% 100%; /* White */
    --card-foreground: 0 0% 15%; /* Light Black */
    --popover: 0 0% 100%; /* White */
    --popover-foreground: 0 0% 15%; /* Light Black */
    --primary: 0 0% 15%; /* Light Black */
    --primary-foreground: 0 0% 100%; /* White */
    --secondary: 0 0% 96%; /* Light Grey */
    --secondary-foreground: 0 0% 15%; /* Light Black */
    --muted: 0 0% 96%; /* Light Grey */
    --muted-foreground: 0 0% 45%; /* Grey */
    --accent: 0 0% 96%; /* Light Grey */
    --accent-foreground: 0 0% 15%; /* Light Black */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 90%; /* Light Grey border */
    --input: 0 0% 90%; /* Light Grey input */
    --ring: 0 0% 15%; /* Light Black ring */
    --chart-1: 0 0% 45%;
    --chart-2: 0 0% 35%;
    --chart-3: 0 0% 25%;
    --chart-4: 0 0% 55%;
    --chart-5: 0 0% 65%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 8%; /* Dark background */
    --foreground: 0 0% 95%; /* Light text */
    --card: 0 0% 12%; /* Dark card */
    --card-foreground: 0 0% 95%; /* Light text */
    --popover: 0 0% 12%; /* Dark popover */
    --popover-foreground: 0 0% 95%; /* Light text */
    --primary: 0 0% 95%; /* Light primary */
    --primary-foreground: 0 0% 8%; /* Dark text */
    --secondary: 0 0% 18%; /* Dark grey */
    --secondary-foreground: 0 0% 95%; /* Light text */
    --muted: 0 0% 18%; /* Dark grey */
    --muted-foreground: 0 0% 65%; /* Light grey text */
    --accent: 0 0% 18%; /* Dark grey */
    --accent-foreground: 0 0% 95%; /* Light text */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 95%;
    --border: 0 0% 18%; /* Dark border */
    --input: 0 0% 18%; /* Dark input */
    --ring: 0 0% 83.9%; /* Light ring */
    --chart-1: 0 0% 70%;
    --chart-2: 0 0% 60%;
    --chart-3: 0 0% 50%;
    --chart-4: 0 0% 80%;
    --chart-5: 0 0% 90%;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700;
  }

  .sidebar-item {
    @apply flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200;
  }

  .sidebar-item.active {
    @apply bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300;
  }

  /* Custom scrollbar styles */
  .volume-trends-scroll {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }

  .volume-trends-scroll::-webkit-scrollbar {
    width: 6px;
  }

  .volume-trends-scroll::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  .volume-trends-scroll::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .volume-trends-scroll::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  .dark .volume-trends-scroll {
    scrollbar-color: #4b5563 #1f2937;
  }

  .dark .volume-trends-scroll::-webkit-scrollbar-track {
    background: #1f2937;
  }

  .dark .volume-trends-scroll::-webkit-scrollbar-thumb {
    background: #4b5563;
  }

  .dark .volume-trends-scroll::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
  }

  /* Custom scrollbar for adherence board - following project theme */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) hsl(var(--muted) / 0.1);
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: hsl(var(--muted) / 0.1);
    border-radius: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
    border: 1px solid hsl(var(--background));
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }

  .custom-scrollbar::-webkit-scrollbar-corner {
    background: hsl(var(--muted) / 0.1);
  }

  /* Wave + Pulse animations for priority circles */
  .pulse-fast {
    position: relative;
    animation: pulse 0.8s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .pulse-medium {
    position: relative;
    animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .pulse-slow {
    position: relative;
    animation: pulse 2.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .pulse-fast::before,
  .pulse-fast::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: wave-fast 0.6s ease-out infinite;
    pointer-events: none;
  }

  .pulse-fast::after {
    animation-delay: 0.2s;
  }

  .pulse-medium::before,
  .pulse-medium::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: wave-medium 3s ease-out infinite;
    pointer-events: none;
  }

  .pulse-medium::after {
    animation-delay: 1s;
  }

  .pulse-slow::before,
  .pulse-slow::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: wave-slow 5s ease-out infinite;
    pointer-events: none;
  }

  .pulse-slow::after {
    animation-delay: 2s;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.1);
    }
  }

  @keyframes wave-fast {
    0% {
      opacity: 0.8;
      transform: translate(-50%, -50%) scale(1);
      border: 2px solid #ef4444;
    }
    50% {
      opacity: 0.4;
      transform: translate(-50%, -50%) scale(2);
      border: 1px solid #ef4444;
    }
    100% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(3);
      border: 0.5px solid #ef4444;
    }
  }

  @keyframes wave-medium {
    0% {
      opacity: 0.8;
      transform: translate(-50%, -50%) scale(1);
      border: 2px solid #eab308;
    }
    50% {
      opacity: 0.4;
      transform: translate(-50%, -50%) scale(2);
      border: 1px solid #eab308;
    }
    100% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(3);
      border: 0.5px solid #eab308;
    }
  }

  @keyframes wave-slow {
    0% {
      opacity: 0.8;
      transform: translate(-50%, -50%) scale(1);
      border: 2px solid #3b82f6;
    }
    50% {
      opacity: 0.4;
      transform: translate(-50%, -50%) scale(2);
      border: 1px solid #3b82f6;
    }
    100% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(3);
      border: 0.5px solid #3b82f6;
    }
  }
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Knowledge Base Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Login Page Animations */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px);
    opacity: 1;
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Google Sign-in Button Full Width */
.google-signin-wrapper > div {
  width: 100% !important;
}

.google-signin-wrapper > div > div {
  width: 100% !important;
}

/* Custom Dialog Animations */
@keyframes scale-in {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scale-in {
  animation: scale-in 0.2s ease-out;
}

/* Task Board Highlight Animation */
@keyframes pulse-highlight {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    border-color: rgb(59, 130, 246);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    border-color: rgb(147, 197, 253);
  }
}

.animate-pulse-highlight {
  animation: pulse-highlight 2s ease-in-out 3;
  border: 2px solid rgb(59, 130, 246);
}

/* Make all images and avatars circular */
img, .avatar, [class*="avatar"], [class*="Avatar"] {
  border-radius: 50% !important;
}

/* Specific circular styling for common image containers */
.w-16.h-16, .w-32.h-32, .w-12.h-12, .w-8.h-8 {
  border-radius: 50% !important;
}

/* Company logo and profile pictures */
.company-logo, .profile-picture, .user-avatar {
  border-radius: 50% !important;
  object-fit: cover;
}

/* AI Assistant Animations */
@keyframes float-bubble {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-15px) translateX(5px);
    opacity: 1;
  }
  50% {
    transform: translateY(-25px) translateX(-3px);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-10px) translateX(8px);
    opacity: 0.9;
  }
}

@keyframes bounce-gentle {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes wiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-3deg);
  }
  75% {
    transform: rotate(3deg);
  }
}

.animate-float-bubble {
  animation: float-bubble 3s ease-in-out infinite;
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s ease-in-out infinite;
}

.animate-wiggle {
  animation: wiggle 1s ease-in-out infinite;
}

/* Consistent Gradient Styling */
.gradient-primary {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--background)) 100%);
}

.gradient-card-header {
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted) / 0.2) 100%);
}

.gradient-button-primary {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%);
}

.gradient-button-secondary {
  background: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--secondary) / 0.8) 100%);
}

.gradient-ai-assistant {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
}

.gradient-login-bg {
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted) / 0.2) 50%, hsl(var(--background)) 100%);
}

.gradient-border {
  border: 1px solid transparent;
  background: linear-gradient(hsl(var(--background)), hsl(var(--background))) padding-box,
              linear-gradient(135deg, hsl(var(--primary) / 0.2) 0%, hsl(var(--accent) / 0.2) 100%) border-box;
}

/* Hover effects for gradients */
.gradient-primary:hover {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.9) 0%, hsl(var(--accent) / 0.9) 100%);
}

.gradient-button-primary:hover {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.9) 0%, hsl(var(--primary) / 0.7) 100%);
}

@keyframes gentlePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fadeIn {
  animation: fadeIn 0.4s ease-out forwards;
}

.animate-slideInRight {
  animation: slideInRight 0.5s ease-out forwards;
}

.animate-pulse-gentle {
  animation: gentlePulse 2s ease-in-out infinite;
}

/* Knowledge Base Module Cards */
.knowledge-module-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.knowledge-module-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.knowledge-module-card:hover .module-icon {
  transform: scale(1.1) rotate(5deg);
}

/* Search Bar Enhancements */
.search-container {
  position: relative;
}

/* Trending Documents Styling */
.trending-item {
  position: relative;
  overflow: hidden;
}

.trending-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.trending-item:hover::before {
  left: 100%;
}

/* Documents Page Animations */
.document-card {
  opacity: 0;
}

/* Global Animation Classes - Apply throughout the app */
.dashboard-widget,
.page-widget,
.content-card,
.animated-element {
  opacity: 0;
}

/* Animation utility classes for consistent use across all pages */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out forwards;
}

.animate-fade-in {
  animation: fadeIn 0.4s ease-out forwards;
}

.animate-pulse-gentle {
  animation: gentlePulse 2s ease-in-out infinite;
}

/* Staggered animation delays for consistent timing */
.animate-delay-0 { animation-delay: 0ms; }
.animate-delay-150 { animation-delay: 150ms; }
.animate-delay-300 { animation-delay: 300ms; }
.animate-delay-450 { animation-delay: 450ms; }
.animate-delay-600 { animation-delay: 600ms; }
.animate-delay-750 { animation-delay: 750ms; }
.animate-delay-900 { animation-delay: 900ms; }

/* Additional animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes gentlePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* Text truncation utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
