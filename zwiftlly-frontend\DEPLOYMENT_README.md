# ZWIFTLLY Team Management System

A comprehensive team management dashboard built with React, TypeScript, and Vite.

## Features

- 🔐 Secure Authentication System
- 📊 Real-time Dashboard & Analytics
- 📅 Team Schedules & Live Adherence Board
- 📚 Knowledge Base Management
- 📋 Kanban Task Board
- 📈 Team Performance Analytics
- 🤖 AI Assistant
- 👤 User Profile Management
- ⚙️ Admin Settings
- 📱 Mobile Responsive Design

## Admin Login Credentials

- Username: `admin` / Password: `brandyadmin@2025`
- Username: `rayadmin` / Password: `rayadmin@2025`
- Username: `otheradmin` / Password: `otheradmins@2025`

## Tech Stack

- React 18
- TypeScript
- Vite
- Tailwind CSS
- Shadcn/ui Components
- Lucide React Icons

## Deployment

This project is optimized for deployment on Vercel with automatic builds and deployments.

## Development

```bash
npm install
npm run dev
```

## Build

```bash
npm run build
```

Built with ❤️ for team management excellence.
