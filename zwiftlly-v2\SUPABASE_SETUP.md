# ZWIFTLLY V2 - Supabase Setup Guide

This guide will walk you through setting up Supa<PERSON> for the ZWIFTLLY V2 application.

## 🚀 Quick Setup

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Sign in or create an account
3. Click "New Project"
4. Choose your organization
5. Fill in project details:
   - **Name**: `zwiftlly-v2`
   - **Database Password**: Generate a strong password
   - **Region**: Choose closest to your users
6. Click "Create new project"

### 2. Get Project Credentials

Once your project is created:

1. Go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (e.g., `https://your-project-id.supabase.co`)
   - **Anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

### 3. Update Environment Variables

Update your `.env` file:

```env
REACT_APP_SUPABASE_URL=https://your-project-id.supabase.co
REACT_APP_SUPABASE_ANON_KEY=your-anon-key-here
```

## 🗄️ Database Setup

### 1. Run Database Schema

1. Go to **SQL Editor** in your Supabase dashboard
2. Create a new query
3. Copy and paste the contents of `supabase/schema.sql`
4. Click "Run" to execute

### 2. Apply RLS Policies

1. In the SQL Editor, create another new query
2. Copy and paste the contents of `supabase/policies.sql`
3. Click "Run" to execute

### 3. Add Seed Data

1. In the SQL Editor, create another new query
2. Copy and paste the contents of `supabase/seed.sql`
3. Click "Run" to execute

## 🔐 Authentication Setup

### 1. Configure Auth Settings

1. Go to **Authentication** → **Settings**
2. Under **Site URL**, add:
   - `http://localhost:3000` (for development)
   - Your production domain (when deploying)

### 2. Set Up Google OAuth

1. Go to **Authentication** → **Providers**
2. Find **Google** and click the toggle to enable it
3. You'll need to create a Google OAuth app:

#### Create Google OAuth App:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google+ API
4. Go to **Credentials** → **Create Credentials** → **OAuth 2.0 Client IDs**
5. Configure OAuth consent screen if prompted
6. For **Application type**, choose "Web application"
7. Add authorized redirect URIs:
   - `https://your-project-id.supabase.co/auth/v1/callback`
8. Copy the **Client ID** and **Client Secret**

#### Configure in Supabase:

1. Back in Supabase, paste the Google **Client ID** and **Client Secret**
2. Click "Save"

### 3. Configure Email Templates (Optional)

1. Go to **Authentication** → **Email Templates**
2. Customize the email templates for:
   - Confirm signup
   - Magic link
   - Change email address
   - Reset password

## 🛡️ Security Configuration

### 1. Domain Restrictions

The application automatically restricts access to specific domains through the database trigger in `seed.sql`:

- `zwiftlly.com` → ADMIN role
- `netic.ai` → AGENT role  
- `<EMAIL>` → ADMIN role (specific gmail exception)

### 2. Row Level Security

RLS is automatically enabled for all tables with policies that ensure:

- Users can only access data from their organization
- Users can only modify their own data (unless admin)
- Admins have full access within their organization

### 3. API Security

The anon key is safe to use in frontend code because:

- RLS policies protect all data access
- Users must be authenticated to access any data
- Domain restrictions prevent unauthorized signups

## 🧪 Testing the Setup

### 1. Test Authentication

1. Start your development server: `npm start`
2. Go to `http://localhost:3000`
3. Try signing up with an authorized email domain
4. Check that the user appears in the **Authentication** → **Users** section

### 2. Test Database Access

1. In Supabase, go to **Table Editor**
2. Check that your tables were created:
   - organizations
   - users
   - tasks
   - announcements
   - etc.

### 3. Test RLS Policies

1. Try accessing data through the API
2. Verify that users can only see data from their organization
3. Test that unauthorized domains are rejected

## 🚀 Production Deployment

### 1. Update Environment Variables

For production deployment on Vercel:

1. Go to your Vercel project settings
2. Add environment variables:
   - `REACT_APP_SUPABASE_URL`
   - `REACT_APP_SUPABASE_ANON_KEY`

### 2. Update Auth Settings

1. In Supabase **Authentication** → **Settings**
2. Add your production domain to **Site URL**
3. Update Google OAuth redirect URIs to include production domain

### 3. Update CORS Settings

1. Go to **Settings** → **API**
2. Add your production domain to **CORS origins**

## 📊 Monitoring and Maintenance

### 1. Database Monitoring

- Monitor database usage in **Settings** → **Usage**
- Set up alerts for high usage
- Regular backups are automatic

### 2. Auth Monitoring

- Monitor auth usage in **Authentication** → **Users**
- Check for suspicious login patterns
- Review user activity logs

### 3. Performance Optimization

- Monitor slow queries in **SQL Editor**
- Add indexes for frequently queried columns
- Use database functions for complex operations

## 🆘 Troubleshooting

### Common Issues:

1. **"Invalid JWT"** - Check that environment variables are correct
2. **"Row Level Security"** - Verify RLS policies are applied
3. **"Domain not authorized"** - Check domain restrictions in seed.sql
4. **Google OAuth fails** - Verify redirect URIs and credentials

### Getting Help:

- Check Supabase documentation: [supabase.com/docs](https://supabase.com/docs)
- Join Supabase Discord for community support
- Check application logs for detailed error messages

---

**Next Steps**: After completing this setup, proceed with connecting the frontend to Supabase and testing all features.
