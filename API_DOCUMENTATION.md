# ZWIFTLLY API Documentation

Complete API reference for the ZWIFTLLY Team Management System backend.

## 🔗 Base URL

- **Development**: `http://localhost:3001/api`
- **Production**: `https://your-backend-domain.railway.app/api`

## 🔐 Authentication

All protected endpoints require a JWT token in the Authorization header:

```http
Authorization: Bearer <your-jwt-token>
```

### **Getting a Token**
Tokens are obtained through Google OAuth login via the `/auth/google` endpoint.

## 📚 API Endpoints

### **Authentication**

#### **POST /auth/google**
Authenticate user with Google OAuth credential.

**Request Body:**
```json
{
  "credential": "google-jwt-token"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "name": "User Name",
    "role": "ADMIN",
    "organizationId": "org-id",
    "organization": {
      "id": "org-id",
      "name": "Organization Name",
      "domain": "example.com"
    }
  },
  "token": "jwt-token"
}
```

#### **GET /auth/me**
Get current authenticated user information.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "name": "User Name",
    "role": "ADMIN",
    "organizationId": "org-id",
    "organization": {
      "id": "org-id",
      "name": "Organization Name",
      "domain": "example.com"
    }
  }
}
```

#### **POST /auth/logout**
Logout current user.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

### **User Management**

#### **GET /users**
Get list of users (Admin/Manager only).

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 50)
- `search` (string): Search by name or email
- `role` (string): Filter by role
- `organizationId` (string): Filter by organization

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "user-id",
      "email": "<EMAIL>",
      "name": "User Name",
      "role": "AGENT",
      "isActive": true,
      "createdAt": "2025-01-01T00:00:00Z",
      "organization": {
        "id": "org-id",
        "name": "Organization Name"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 100,
    "pages": 2
  }
}
```

#### **PUT /users/:id**
Update user information.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "name": "Updated Name",
  "role": "MANAGER",
  "isActive": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user-id",
    "email": "<EMAIL>",
    "name": "Updated Name",
    "role": "MANAGER",
    "isActive": true,
    "updatedAt": "2025-01-01T00:00:00Z"
  },
  "message": "User updated successfully"
}
```

### **Attendance Management**

#### **GET /attendance**
Get attendance records.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page` (number): Page number
- `limit` (number): Items per page
- `date` (string): Specific date (YYYY-MM-DD)
- `startDate` (string): Start date range
- `endDate` (string): End date range
- `userId` (string): Filter by user
- `status` (string): Filter by status

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "record-id",
      "userId": "user-id",
      "date": "2025-01-01",
      "clockIn": "2025-01-01T08:00:00Z",
      "clockOut": "2025-01-01T17:00:00Z",
      "totalHours": 8,
      "regularHours": 8,
      "overtime": 0,
      "status": "PRESENT",
      "hourlyRate": 25.00,
      "totalPay": 200.00,
      "netPay": 180.00,
      "user": {
        "id": "user-id",
        "name": "User Name",
        "email": "<EMAIL>"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 100,
    "pages": 2
  }
}
```

#### **POST /attendance/clock-action**
Record a clock action (clock in/out, break start/end).

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "action": "CLOCK_IN",
  "location": "Office",
  "notes": "Starting work day"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "clockAction": {
      "id": "action-id",
      "userId": "user-id",
      "action": "CLOCK_IN",
      "timestamp": "2025-01-01T08:00:00Z",
      "location": "Office"
    },
    "attendanceRecord": {
      "id": "record-id",
      "userId": "user-id",
      "date": "2025-01-01",
      "clockIn": "2025-01-01T08:00:00Z",
      "status": "PRESENT"
    }
  },
  "message": "clock in recorded successfully"
}
```

#### **GET /attendance/summary**
Get attendance summary for dashboard.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `date` (string): Date for summary (default: today)

**Response:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalEmployees": 10,
      "present": 8,
      "late": 1,
      "absent": 1,
      "onBreak": 2,
      "totalHours": 64,
      "totalOvertimeHours": 4,
      "totalPay": 1600.00,
      "averageHours": 6.4
    },
    "records": [...]
  }
}
```

### **Task Management**

#### **GET /tasks**
Get list of tasks.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page` (number): Page number
- `limit` (number): Items per page
- `status` (string): Filter by status
- `priority` (string): Filter by priority
- `assigneeId` (string): Filter by assignee
- `teamId` (string): Filter by team
- `search` (string): Search in title/description

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "task-id",
      "title": "Task Title",
      "description": "Task description",
      "status": "TODO",
      "priority": "HIGH",
      "dueDate": "2025-01-15T00:00:00Z",
      "estimatedHours": 8,
      "actualHours": null,
      "createdAt": "2025-01-01T00:00:00Z",
      "createdBy": {
        "id": "user-id",
        "name": "Creator Name"
      },
      "assignee": {
        "id": "user-id",
        "name": "Assignee Name"
      },
      "team": {
        "id": "team-id",
        "name": "Team Name"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 25,
    "pages": 1
  }
}
```

#### **POST /tasks**
Create a new task.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "title": "New Task",
  "description": "Task description",
  "priority": "MEDIUM",
  "teamId": "team-id",
  "assigneeId": "user-id",
  "dueDate": "2025-01-15T00:00:00Z",
  "estimatedHours": 8,
  "tags": ["frontend", "urgent"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "task-id",
    "title": "New Task",
    "description": "Task description",
    "status": "TODO",
    "priority": "MEDIUM",
    "createdAt": "2025-01-01T00:00:00Z"
  },
  "message": "Task created successfully"
}
```

### **Announcements**

#### **GET /announcements**
Get list of announcements.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page` (number): Page number
- `limit` (number): Items per page
- `category` (string): Filter by category
- `priority` (string): Filter by priority
- `published` (string): Filter by published status
- `search` (string): Search in title/content

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "announcement-id",
      "title": "Important Update",
      "content": "Announcement content...",
      "category": "GENERAL",
      "priority": "HIGH",
      "isPublished": true,
      "publishedAt": "2025-01-01T00:00:00Z",
      "views": 25,
      "author": {
        "id": "user-id",
        "name": "Author Name"
      },
      "comments": [
        {
          "id": "comment-id",
          "content": "Great update!",
          "author": {
            "id": "user-id",
            "name": "Commenter Name"
          },
          "createdAt": "2025-01-01T01:00:00Z"
        }
      ],
      "_count": {
        "comments": 5
      }
    }
  ]
}
```

#### **POST /announcements**
Create a new announcement.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "title": "New Announcement",
  "content": "Announcement content...",
  "category": "GENERAL",
  "priority": "MEDIUM",
  "isPublished": true,
  "tags": ["important", "update"]
}
```

## 🔒 Error Responses

### **Standard Error Format**
```json
{
  "success": false,
  "error": "Error message",
  "details": ["Detailed error information"]
}
```

### **Common HTTP Status Codes**
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation error)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `409` - Conflict (duplicate resource)
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

## 🔄 Real-time Events (Socket.IO)

### **Connection**
```javascript
const socket = io('wss://your-backend-domain.railway.app', {
  auth: {
    token: 'your-jwt-token'
  }
});
```

### **Events**
- `user:status-changed` - User status updates
- `attendance:update` - Attendance record changes
- `task:updated` - Task modifications
- `announcement:new` - New announcements
- `notification:new` - New notifications

## 📝 Rate Limits

- **Authentication endpoints**: 5 requests per 15 minutes
- **General API endpoints**: 100 requests per 15 minutes
- **Real-time connections**: 10 connections per IP

## 🧪 Testing the API

### **Using cURL**
```bash
# Login
curl -X POST https://your-backend-domain.railway.app/api/auth/google \
  -H "Content-Type: application/json" \
  -d '{"credential": "google-jwt-token"}'

# Get current user
curl -X GET https://your-backend-domain.railway.app/api/auth/me \
  -H "Authorization: Bearer your-jwt-token"
```

### **Using Postman**
Import the API collection from `/docs/postman-collection.json` (if available).

---

**For more detailed examples and advanced usage, refer to the main documentation.**
