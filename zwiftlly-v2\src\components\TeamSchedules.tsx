import React, { useState } from 'react';
import { 
  Calendar, 
  Clock, 
  Users, 
  Filter,
  ChevronLeft,
  ChevronRight,
  Edit,
  Plus
} from 'lucide-react';

interface TeamSchedulesProps {
  darkMode?: boolean;
}

const TeamSchedules: React.FC<TeamSchedulesProps> = ({ darkMode = false }) => {
  const [currentView, setCurrentView] = useState<'schedule' | 'adherence'>('schedule');
  const [currentWeek, setCurrentWeek] = useState(new Date());

  // Mock schedule data
  const scheduleData = [
    {
      id: 1,
      name: '<PERSON>',
      schedule: {
        monday: { start: '8:00 AM', end: '5:00 PM', status: 'scheduled' },
        tuesday: { start: '8:00 AM', end: '5:00 PM', status: 'scheduled' },
        wednesday: { start: '8:00 AM', end: '5:00 PM', status: 'scheduled' },
        thursday: { start: '8:00 AM', end: '5:00 PM', status: 'scheduled' },
        friday: { start: '8:00 AM', end: '5:00 PM', status: 'scheduled' },
        saturday: 'off',
        sunday: 'off'
      }
    },
    {
      id: 2,
      name: '<PERSON>',
      schedule: {
        monday: { start: '9:00 AM', end: '6:00 PM', status: 'scheduled' },
        tuesday: { start: '9:00 AM', end: '6:00 PM', status: 'scheduled' },
        wednesday: { start: '9:00 AM', end: '6:00 PM', status: 'scheduled' },
        thursday: { start: '9:00 AM', end: '6:00 PM', status: 'scheduled' },
        friday: { start: '9:00 AM', end: '6:00 PM', status: 'scheduled' },
        saturday: 'off',
        sunday: 'off'
      }
    },
    {
      id: 3,
      name: 'Bliss Ann',
      schedule: {
        monday: { start: '10:00 AM', end: '7:00 PM', status: 'scheduled' },
        tuesday: { start: '10:00 AM', end: '7:00 PM', status: 'scheduled' },
        wednesday: { start: '10:00 AM', end: '7:00 PM', status: 'scheduled' },
        thursday: { start: '10:00 AM', end: '7:00 PM', status: 'scheduled' },
        friday: { start: '10:00 AM', end: '7:00 PM', status: 'scheduled' },
        saturday: 'off',
        sunday: 'off'
      }
    },
    {
      id: 4,
      name: 'Pearl',
      schedule: {
        monday: 'off',
        tuesday: { start: '8:00 AM', end: '5:00 PM', status: 'scheduled' },
        wednesday: { start: '8:00 AM', end: '5:00 PM', status: 'scheduled' },
        thursday: { start: '8:00 AM', end: '5:00 PM', status: 'scheduled' },
        friday: { start: '8:00 AM', end: '5:00 PM', status: 'scheduled' },
        saturday: { start: '8:00 AM', end: '5:00 PM', status: 'scheduled' },
        sunday: { start: '8:00 AM', end: '3:00 PM', status: 'scheduled' }
      }
    },
    {
      id: 5,
      name: 'Christine G.',
      schedule: {
        monday: { start: '7:00 AM', end: '4:00 PM', status: 'scheduled' },
        tuesday: { start: '7:00 AM', end: '4:00 PM', status: 'scheduled' },
        wednesday: { start: '7:00 AM', end: '4:00 PM', status: 'scheduled' },
        thursday: { start: '7:00 AM', end: '4:00 PM', status: 'scheduled' },
        friday: { start: '7:00 AM', end: '4:00 PM', status: 'scheduled' },
        saturday: 'off',
        sunday: 'off'
      }
    },
    {
      id: 6,
      name: 'Ray',
      schedule: {
        monday: { start: '6:00 AM', end: '3:00 PM', status: 'scheduled' },
        tuesday: { start: '6:00 AM', end: '3:00 PM', status: 'scheduled' },
        wednesday: { start: '6:00 AM', end: '3:00 PM', status: 'scheduled' },
        thursday: { start: '6:00 AM', end: '3:00 PM', status: 'scheduled' },
        friday: { start: '6:00 AM', end: '3:00 PM', status: 'scheduled' },
        saturday: 'off',
        sunday: 'off'
      }
    }
  ];

  // Live adherence data
  const adherenceData = [
    { name: 'Louise', status: 'auditing', currentTask: 'Auditing', idleTime: '20.5m idle', workStatus: 'Working' },
    { name: 'Rose', status: 'watermelon', currentTask: 'Watermelon', idleTime: '1h 29m idle', workStatus: 'On Break' },
    { name: 'Bliss Ann', status: 'system', currentTask: 'System', idleTime: '', workStatus: 'Working' },
    { name: 'Pearl', status: 'off', currentTask: 'Off', idleTime: '', workStatus: 'Offline' },
    { name: 'Christine G.', status: 'absent', currentTask: 'Absent', idleTime: '', workStatus: 'Absent' },
    { name: 'Ray', status: 'auditing', currentTask: 'Auditing', idleTime: '1h 5m idle', workStatus: 'Working' }
  ];

  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  const dayKeys = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'auditing':
        return 'bg-green-500';
      case 'watermelon':
        return 'bg-orange-500';
      case 'system':
        return 'bg-blue-500';
      case 'off':
        return 'bg-gray-500';
      case 'absent':
        return 'bg-red-500';
      default:
        return 'bg-gray-400';
    }
  };

  const formatWeekRange = (date: Date) => {
    const startOfWeek = new Date(date);
    startOfWeek.setDate(date.getDate() - date.getDay() + 1); // Monday
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6); // Sunday
    
    return `Week of June ${startOfWeek.getDate()}-${endOfWeek.getDate()}, 2025`;
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Calendar className="w-8 h-8 text-primary" />
              <div>
                <h1 className="text-3xl font-bold text-foreground">Team Schedules</h1>
                <p className="text-muted-foreground">Manage schedules, track time, and monitor team adherence</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex bg-muted rounded-lg p-1">
                <button
                  onClick={() => setCurrentView('schedule')}
                  className={`px-4 py-2 text-sm rounded-md transition-colors ${
                    currentView === 'schedule'
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                >
                  Weekly Schedule
                </button>
                <button
                  onClick={() => setCurrentView('adherence')}
                  className={`px-4 py-2 text-sm rounded-md transition-colors ${
                    currentView === 'adherence'
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                >
                  Live Adherence Board
                </button>
              </div>
              <button className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors">
                <Plus className="w-4 h-4" />
                <span>Attendance Management</span>
              </button>
            </div>
          </div>
        </div>

        {/* Weekly Schedule View */}
        {currentView === 'schedule' && (
          <div className="space-y-6">
            {/* Week Navigation */}
            <div className="flex items-center justify-between bg-card border border-border rounded-lg p-4">
              <div className="flex items-center space-x-4">
                <button className="p-2 hover:bg-muted rounded-lg transition-colors">
                  <ChevronLeft className="w-5 h-5 text-muted-foreground" />
                </button>
                <div className="flex items-center space-x-2">
                  <Calendar className="w-5 h-5 text-primary" />
                  <span className="font-semibold text-foreground">{formatWeekRange(currentWeek)}</span>
                </div>
                <button className="p-2 hover:bg-muted rounded-lg transition-colors">
                  <ChevronRight className="w-5 h-5 text-muted-foreground" />
                </button>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Users className="w-4 h-4" />
                <span>Team availability overview</span>
              </div>
            </div>

            {/* Schedule Grid */}
            <div className="bg-card border border-border rounded-lg overflow-hidden">
              <div className="grid grid-cols-8 bg-muted">
                <div className="p-4 font-semibold text-foreground border-r border-border">
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4" />
                    <span>Team Member</span>
                  </div>
                </div>
                {days.map((day, index) => (
                  <div key={day} className="p-4 text-center font-semibold text-foreground border-r border-border last:border-r-0">
                    <div className="text-sm">{day}</div>
                    <div className="text-xs text-muted-foreground">
                      Jun {17 + index}, 2025
                    </div>
                  </div>
                ))}
              </div>

              {scheduleData.map((member, memberIndex) => (
                <div key={member.id} className="grid grid-cols-8 border-b border-border last:border-b-0">
                  <div className="p-4 font-medium text-foreground border-r border-border bg-muted/50">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-primary text-sm font-bold">
                        {member.name.charAt(0)}
                      </div>
                      <span>{member.name}</span>
                    </div>
                  </div>
                  {dayKeys.map((dayKey) => {
                    const daySchedule = member.schedule[dayKey as keyof typeof member.schedule];
                    return (
                      <div key={dayKey} className="p-4 text-center border-r border-border last:border-r-0">
                        {daySchedule === 'off' ? (
                          <span className="text-muted-foreground text-sm">Off</span>
                        ) : (
                          <div className="space-y-1">
                            <div className="bg-primary/10 text-primary px-2 py-1 rounded text-xs font-medium">
                              {(daySchedule as any).start} - {(daySchedule as any).end}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              ))}
            </div>

            {/* Schedule Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-foreground">Scheduled Shifts: 42</span>
                </div>
                <p className="text-xs text-muted-foreground">Days Off: 14</p>
              </div>
              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium text-foreground">Total Coverage: 72.4%</span>
                </div>
                <p className="text-xs text-muted-foreground">Updates every 15 seconds</p>
              </div>
              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span className="text-sm font-medium text-foreground">Peak Hours: 9 AM - 5 PM</span>
                </div>
                <p className="text-xs text-muted-foreground">6 agents scheduled</p>
              </div>
            </div>
          </div>
        )}

        {/* Live Adherence Board */}
        {currentView === 'adherence' && (
          <div className="space-y-6">
            {/* Adherence Header */}
            <div className="bg-card border border-border rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h2 className="text-xl font-semibold text-foreground">Live Adherence Board</h2>
                  <p className="text-sm text-muted-foreground">Real-time team status and activity monitoring</p>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>Live updates every 30 seconds</span>
                </div>
              </div>

              {/* Status Legend */}
              <div className="flex flex-wrap gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-muted-foreground">Auditing</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <span className="text-muted-foreground">Watermelon</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-muted-foreground">System</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                  <span className="text-muted-foreground">Off</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-muted-foreground">Absent</span>
                </div>
              </div>
            </div>

            {/* Adherence Grid */}
            <div className="bg-card border border-border rounded-lg overflow-hidden">
              <div className="grid grid-cols-5 bg-muted">
                <div className="p-4 font-semibold text-foreground border-r border-border">Agent</div>
                <div className="p-4 font-semibold text-foreground border-r border-border">Status</div>
                <div className="p-4 font-semibold text-foreground border-r border-border">Current Task</div>
                <div className="p-4 font-semibold text-foreground border-r border-border">Idle Time</div>
                <div className="p-4 font-semibold text-foreground">Work Status</div>
              </div>

              {adherenceData.map((agent, index) => (
                <div key={index} className="grid grid-cols-5 border-b border-border last:border-b-0 hover:bg-muted/50 transition-colors">
                  <div className="p-4 border-r border-border">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-primary text-sm font-bold">
                        {agent.name.charAt(0)}
                      </div>
                      <span className="font-medium text-foreground">{agent.name}</span>
                    </div>
                  </div>
                  <div className="p-4 border-r border-border">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(agent.status)}`}></div>
                      <span className="text-sm text-foreground capitalize">{agent.status}</span>
                    </div>
                  </div>
                  <div className="p-4 border-r border-border">
                    <span className="text-sm text-foreground">{agent.currentTask}</span>
                  </div>
                  <div className="p-4 border-r border-border">
                    <span className={`text-sm ${agent.idleTime ? 'text-orange-600' : 'text-muted-foreground'}`}>
                      {agent.idleTime || '-'}
                    </span>
                  </div>
                  <div className="p-4">
                    <span className={`text-sm px-2 py-1 rounded-full ${
                      agent.workStatus === 'Working' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                      agent.workStatus === 'On Break' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400' :
                      agent.workStatus === 'Offline' ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400' :
                      'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                    }`}>
                      {agent.workStatus}
                    </span>
                  </div>
                </div>
              ))}
            </div>

            {/* Adherence Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-foreground">Active Agents: 3</span>
                </div>
                <p className="text-xs text-muted-foreground">Currently working</p>
              </div>
              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span className="text-sm font-medium text-foreground">On Break: 1</span>
                </div>
                <p className="text-xs text-muted-foreground">Watermelon status</p>
              </div>
              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span className="text-sm font-medium text-foreground">Absent: 1</span>
                </div>
                <p className="text-xs text-muted-foreground">Not scheduled</p>
              </div>
              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium text-foreground">Adherence: 87.5%</span>
                </div>
                <p className="text-xs text-muted-foreground">Team average</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TeamSchedules;
