{"name": "zwiftlly-v2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"@types/node": "^24.0.3", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "typescript": "~5.8.3", "vite": "^6.3.5"}, "dependencies": {"@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.81.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.522.0", "tailwind-merge": "^3.3.1"}}