-- ZWIFTLLY V2 Seed Data
-- This file contains initial data for development and testing

-- Insert organizations
INSERT INTO organizations (id, name, domain) VALUES
    ('550e8400-e29b-41d4-a716-446655440000', 'ZWIFTLLY', 'zwiftlly.com'),
    ('550e8400-e29b-41d4-a716-446655440001', 'Netic AI', 'netic.ai');

-- Insert sample system status
INSERT INTO system_status (service_name, status, description, organization_id) VALUES
    ('Authentication Service', 'operational', 'All authentication services running normally', '550e8400-e29b-41d4-a716-446655440000'),
    ('Database', 'operational', 'Database performance is optimal', '550e8400-e29b-41d4-a716-446655440000'),
    ('API Gateway', 'operational', 'API gateway responding normally', '550e8400-e29b-41d4-a716-446655440000'),
    ('Real-time Services', 'operational', 'WebSocket connections stable', '550e8400-e29b-41d4-a716-446655440000'),
    ('File Storage', 'operational', 'File upload and download services operational', '550e8400-e29b-41d4-a716-446655440000');

-- Insert sample announcements (these will be created after users are added via auth)
-- Note: User IDs will need to be updated after actual users sign up

-- Sample tasks (these will also need real user IDs)
-- Note: These are placeholder entries that will be updated with real data

-- Sample performance metrics structure (will be populated by application)
-- Note: This shows the expected data structure

-- Sample volume tracking structure (will be populated by application)
-- Note: This shows the expected data structure

-- Create a function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    user_domain TEXT;
    org_id UUID;
    user_role user_role;
BEGIN
    -- Extract domain from email
    user_domain := split_part(NEW.email, '@', 2);
    
    -- Determine organization and role based on domain
    CASE user_domain
        WHEN 'zwiftlly.com' THEN
            SELECT id INTO org_id FROM organizations WHERE domain = 'zwiftlly.com';
            user_role := 'ADMIN';
        WHEN 'netic.ai' THEN
            SELECT id INTO org_id FROM organizations WHERE domain = 'netic.ai';
            user_role := 'AGENT';
        WHEN 'gmail.com' THEN
            -- Only allow specific gmail addresses
            IF NEW.email = '<EMAIL>' THEN
                SELECT id INTO org_id FROM organizations WHERE domain = 'zwiftlly.com';
                user_role := 'ADMIN';
            ELSE
                RAISE EXCEPTION 'Unauthorized email domain: %', NEW.email;
            END IF;
        ELSE
            RAISE EXCEPTION 'Unauthorized email domain: %', user_domain;
    END CASE;
    
    -- Insert user record
    INSERT INTO users (
        id,
        email,
        full_name,
        role,
        organization_id,
        last_login
    ) VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        user_role,
        org_id,
        NOW()
    );
    
    -- Create welcome notification
    INSERT INTO notifications (
        user_id,
        title,
        message,
        type
    ) VALUES (
        NEW.id,
        'Welcome to ZWIFTLLY!',
        'Your account has been successfully created. Explore the dashboard to get started.',
        'welcome'
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Create a function to update user last login
CREATE OR REPLACE FUNCTION update_user_last_login()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE users 
    SET last_login = NOW()
    WHERE id = NEW.id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for user login tracking
CREATE TRIGGER on_auth_user_login
    AFTER UPDATE OF last_sign_in_at ON auth.users
    FOR EACH ROW 
    WHEN (OLD.last_sign_in_at IS DISTINCT FROM NEW.last_sign_in_at)
    EXECUTE FUNCTION update_user_last_login();

-- Function to update announcement counts
CREATE OR REPLACE FUNCTION update_announcement_counts()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF TG_TABLE_NAME = 'announcement_likes' THEN
            UPDATE announcements 
            SET likes_count = likes_count + 1 
            WHERE id = NEW.announcement_id;
        ELSIF TG_TABLE_NAME = 'announcement_comments' THEN
            UPDATE announcements 
            SET comments_count = comments_count + 1 
            WHERE id = NEW.announcement_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        IF TG_TABLE_NAME = 'announcement_likes' THEN
            UPDATE announcements 
            SET likes_count = likes_count - 1 
            WHERE id = OLD.announcement_id;
        ELSIF TG_TABLE_NAME = 'announcement_comments' THEN
            UPDATE announcements 
            SET comments_count = comments_count - 1 
            WHERE id = OLD.announcement_id;
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for announcement counts
CREATE TRIGGER update_announcement_likes_count
    AFTER INSERT OR DELETE ON announcement_likes
    FOR EACH ROW EXECUTE FUNCTION update_announcement_counts();

CREATE TRIGGER update_announcement_comments_count
    AFTER INSERT OR DELETE ON announcement_comments
    FOR EACH ROW EXECUTE FUNCTION update_announcement_counts();

-- Function to create notifications for announcement interactions
CREATE OR REPLACE FUNCTION create_announcement_notification()
RETURNS TRIGGER AS $$
DECLARE
    announcement_author UUID;
    announcement_title TEXT;
BEGIN
    -- Get announcement details
    SELECT author_id, title INTO announcement_author, announcement_title
    FROM announcements WHERE id = NEW.announcement_id;
    
    -- Don't notify if user is interacting with their own announcement
    IF announcement_author = NEW.user_id THEN
        RETURN NEW;
    END IF;
    
    -- Create notification based on interaction type
    IF TG_TABLE_NAME = 'announcement_likes' THEN
        INSERT INTO notifications (
            user_id,
            title,
            message,
            type,
            related_id,
            related_type
        ) VALUES (
            announcement_author,
            'New Like on Your Announcement',
            'Someone liked your announcement: ' || announcement_title,
            'like',
            NEW.announcement_id,
            'announcement'
        );
    ELSIF TG_TABLE_NAME = 'announcement_comments' THEN
        INSERT INTO notifications (
            user_id,
            title,
            message,
            type,
            related_id,
            related_type
        ) VALUES (
            announcement_author,
            'New Comment on Your Announcement',
            'Someone commented on your announcement: ' || announcement_title,
            'comment',
            NEW.announcement_id,
            'announcement'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for announcement notifications
CREATE TRIGGER create_like_notification
    AFTER INSERT ON announcement_likes
    FOR EACH ROW EXECUTE FUNCTION create_announcement_notification();

CREATE TRIGGER create_comment_notification
    AFTER INSERT ON announcement_comments
    FOR EACH ROW EXECUTE FUNCTION create_announcement_notification();
