# Development/Staging Environment
# Google OAuth Configuration
VITE_GOOGLE_CLIENT_ID=672148527626-ikrhv4ipi0opcbd4e7eddvvfod3dcafk.apps.googleusercontent.com
VITE_GOOGLE_CLIENT_SECRET=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

# Application Configuration
VITE_APP_NAME=ZWIFTLLY Team Management System (STAGING)
VITE_APP_URL=https://zwiftlly-team-management-system-git-development-daddyrays-projects.vercel.app
VITE_API_URL=https://zwiftlly-backend-staging.up.railway.app/api
VITE_ENVIRONMENT=staging

# Organization Configuration (Default allowed domains)
VITE_DEFAULT_ALLOWED_DOMAINS=zwiftlly.com,netic.ai

# Specific allowed emails (for restricted domains like Gmail)
VITE_ALLOWED_EMAILS=<EMAIL>

# Debug mode for staging
VITE_DEBUG_MODE=true
