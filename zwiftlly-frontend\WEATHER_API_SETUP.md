# 🌤️ Weather API Setup Guide

## Overview
The ATLAS dashboard now includes super accurate weather forecasts powered by OpenWeatherMap API. This guide shows how to set up real weather data.

## Current Status
- ✅ **Weather component implemented** with enhanced data structure
- ✅ **Fallback system** provides realistic demo data
- ✅ **Auto-refresh** every 15 minutes
- ✅ **Error handling** with graceful fallbacks
- ✅ **Visual indicators** show data source status
- 🔄 **Ready for API integration** (requires API key)

## Quick Setup for Real Weather Data

### Step 1: Get OpenWeatherMap API Key
1. Visit [OpenWeatherMap](https://openweathermap.org/api)
2. Sign up for a free account
3. Get your API key from the dashboard
4. Free tier includes: 1,000 calls/day, 60 calls/minute

### Step 2: Add API Key to Frontend
Update `WeatherTimeDisplay.tsx`:

```typescript
// Replace this line:
const API_KEY = 'demo_key';

// With your actual API key:
const API_KEY = 'your_actual_api_key_here';
```

### Step 3: Enable Real API Calls
Uncomment the real API call in `fetchCityWeather` function:

```typescript
// Replace the fallback data section with:
const response = await fetch(
  `https://api.openweathermap.org/data/2.5/weather?q=${city}&appid=${API_KEY}&units=imperial`
);

if (!response.ok) {
  throw new Error(`HTTP error! status: ${response.status}`);
}

const data = await response.json();

return {
  city: data.name,
  country: data.sys.country,
  temperature: Math.round(data.main.temp),
  feelsLike: Math.round(data.main.feels_like),
  condition: data.weather[0].main,
  description: data.weather[0].description,
  humidity: data.main.humidity,
  pressure: data.main.pressure,
  windSpeed: Math.round(data.wind.speed),
  windDirection: data.wind.deg,
  visibility: data.visibility,
  cloudiness: data.clouds.all,
  icon: data.weather[0].icon,
  emoji: getWeatherEmoji(data.weather[0].icon),
  sunrise: data.sys.sunrise,
  sunset: data.sys.sunset,
  timezone: data.timezone,
  lastUpdated: Date.now()
};
```

## Features

### 🎯 Enhanced Weather Data
- **Real-time conditions** with accurate temperatures
- **Detailed metrics**: humidity, pressure, wind speed/direction
- **Visual indicators**: weather emojis and condition descriptions
- **Location data**: city, country, timezone information

### 🔄 Smart Caching & Updates
- **15-minute refresh** cycle for optimal API usage
- **Graceful fallbacks** if API is unavailable
- **Visual status indicators**: 🌐 for live data, ⚠️ for fallback

### 🌍 Multi-City Support
Current cities monitored:
- **New York** (Eastern US)
- **Chicago** (Central US) 
- **Denver** (Mountain US)
- **San Francisco** (Pacific US)
- **Manila** (Philippines) - highlighted in green

### 📱 Responsive Design
- **Mobile-optimized** grid layout
- **Temperature toggle**: Fahrenheit ↔ Celsius
- **Compact display** with essential information

## API Rate Limits & Best Practices

### Free Tier Limits
- **1,000 calls/day** (sufficient for 5 cities × 96 updates/day)
- **60 calls/minute** (well above our 5 calls every 15 minutes)

### Optimization Features
- **Batch requests** for multiple cities
- **Error handling** prevents API spam
- **Fallback data** ensures uptime
- **Caching** reduces unnecessary calls

## Troubleshooting

### Common Issues
1. **API Key Invalid**: Check key format and account status
2. **Rate Limit Exceeded**: Increase refresh interval
3. **Network Errors**: Fallback data will be used automatically
4. **City Not Found**: Update city names in the cities array

### Debug Mode
Check browser console for weather fetch logs and error messages.

## Future Enhancements
- **Weather forecasts** (5-day outlook)
- **Severe weather alerts**
- **Location-based auto-detection**
- **Historical weather data**
- **Weather maps integration**

---

**Note**: The current implementation uses enhanced fallback data that provides realistic weather variations for demonstration purposes. Replace with real API calls when ready for production use.
