import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, BookOpen, Users, FileText, Settings, TrendingUp, Eye, Plus, Briefcase, Shield, ChevronRight } from 'lucide-react';

interface Document {
  id: string;
  title: string;
  module: string;
  views: number;
  lastUpdated: string;
  description: string;
}

interface Module {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  description: string;
  documentCount: number;
  gradient: string;
}

interface KnowledgeBaseProps {
  onNavigateToDocuments?: (moduleId: string, moduleName: string) => void;
}

const KnowledgeBase: React.FC<KnowledgeBaseProps> = ({ onNavigateToDocuments }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedModule, setSelectedModule] = useState<string | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isSearchFocused, setIsSearchFocused] = useState(false);

  // Mock data for modules
  const modules: Module[] = [
    {
      id: 'tenants',
      name: 'Tenants',
      icon: <Briefcase className="w-8 h-8" />,
      color: 'text-blue-600',
      description: 'Tenant management, onboarding, and relationship guidelines',
      documentCount: 24,
      gradient: 'from-blue-500 to-blue-600'
    },
    {
      id: 'guidelines',
      name: 'Guidelines',
      icon: <Shield className="w-8 h-8" />,
      color: 'text-green-600',
      description: 'Best practices, standards, and operational guidelines',
      documentCount: 18,
      gradient: 'from-green-500 to-green-600'
    },
    {
      id: 'policies',
      name: 'Policies',
      icon: <FileText className="w-8 h-8" />,
      color: 'text-purple-600',
      description: 'Company policies, compliance, and regulatory documents',
      documentCount: 31,
      gradient: 'from-purple-500 to-purple-600'
    },
    {
      id: 'process',
      name: 'Process',
      icon: <Settings className="w-8 h-8" />,
      color: 'text-orange-600',
      description: 'Workflows, procedures, and step-by-step processes',
      documentCount: 27,
      gradient: 'from-orange-500 to-orange-600'
    }
  ];

  // Mock data for documents
  const mockDocuments: Document[] = [
    {
      id: '1',
      title: 'Tenant Onboarding Checklist',
      module: 'tenants',
      views: 1247,
      lastUpdated: '2024-01-15',
      description: 'Complete checklist for new tenant onboarding process'
    },
    {
      id: '2',
      title: 'Data Privacy Policy',
      module: 'policies',
      views: 892,
      lastUpdated: '2024-01-12',
      description: 'Comprehensive data privacy and protection guidelines'
    },
    {
      id: '3',
      title: 'Quality Assurance Guidelines',
      module: 'guidelines',
      views: 756,
      lastUpdated: '2024-01-10',
      description: 'Standards and procedures for quality assurance'
    },
    {
      id: '4',
      title: 'Incident Response Process',
      module: 'process',
      views: 634,
      lastUpdated: '2024-01-08',
      description: 'Step-by-step incident response and escalation procedures'
    },
    {
      id: '5',
      title: 'Tenant Communication Standards',
      module: 'tenants',
      views: 523,
      lastUpdated: '2024-01-05',
      description: 'Communication protocols and best practices for tenant relations'
    },
    {
      id: '6',
      title: 'Security Compliance Policy',
      module: 'policies',
      views: 445,
      lastUpdated: '2024-01-03',
      description: 'Security standards and compliance requirements'
    },
    {
      id: '7',
      title: 'Code Review Guidelines',
      module: 'guidelines',
      views: 398,
      lastUpdated: '2024-01-01',
      description: 'Best practices for code review and approval processes'
    },
    {
      id: '8',
      title: 'Change Management Process',
      module: 'process',
      views: 367,
      lastUpdated: '2023-12-28',
      description: 'Procedures for managing and implementing changes'
    }
  ];

  useEffect(() => {
    setDocuments(mockDocuments);
  }, []);

  // Get top 5 most viewed documents
  const topDocuments = documents
    .sort((a, b) => b.views - a.views)
    .slice(0, 5);

  const handleModuleClick = (moduleId: string) => {
    // Module click now only provides visual feedback, no preview
    // Use "View Documents" button to actually view documents
  };

  const handleViewDocuments = (moduleId: string) => {
    const module = modules.find(m => m.id === moduleId);
    if (module && onNavigateToDocuments) {
      onNavigateToDocuments(moduleId, module.name);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would perform the search
    console.log('Searching for:', searchQuery);
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header Section */}
        <div className="mb-8">
          {/* Top Bar with Search and Add Button */}
          <div className="flex items-center justify-center mb-8 relative">
            {/* Google-style Search Bar */}
            <div className="max-w-2xl w-full">
              <form onSubmit={handleSearch} className="relative">
                <div className={`search-container relative transition-all duration-300 ${
                  isSearchFocused ? 'transform scale-105' : ''
                }`}>
                  <Search className={`absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 transition-colors duration-300 ${
                    isSearchFocused ? 'text-primary' : 'text-muted-foreground'
                  }`} />
                  <Input
                    type="text"
                    placeholder="Search knowledge base..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onFocus={() => setIsSearchFocused(true)}
                    onBlur={() => setIsSearchFocused(false)}
                    className="w-full pl-12 pr-24 py-4 text-lg rounded-full border border-border focus:border-primary focus:ring-1 focus:ring-primary/30 shadow-lg hover:shadow-xl transition-all duration-300 bg-background"
                  />
                  <Button
                    type="submit"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 rounded-full px-6 bg-primary hover:bg-primary/90 transition-all duration-300"
                  >
                    Search
                  </Button>
                </div>
              </form>
            </div>


          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content Area */}
          <div className="lg:col-span-3">
            {/* Module Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {modules.map((module, index) => (
                <Card
                  key={module.id}
                  className={`knowledge-module-card cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-xl group relative overflow-hidden animate-fade-in-up ${index === 0 ? 'animate-delay-0' : index === 1 ? 'animate-delay-150' : index === 2 ? 'animate-delay-300' : 'animate-delay-450'}`}
                >
                  {/* Hover Background */}
                  <div className="absolute inset-0 bg-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  
                  <CardHeader className="relative">
                    <div className="flex items-center space-x-4">
                      <div className={`module-icon p-3 rounded-lg bg-muted ${module.color} group-hover:scale-110 transition-all duration-300`}>
                        {module.icon}
                      </div>
                      <div>
                        <CardTitle className="text-xl font-semibold group-hover:text-primary transition-colors duration-300">
                          {module.name}
                        </CardTitle>
                        <p className="text-sm text-muted-foreground">
                          {module.documentCount} documents
                        </p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="relative">
                    <p className="text-muted-foreground mb-4">
                      {module.description}
                    </p>
                    <div className="flex items-center justify-end mt-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewDocuments(module.id);
                        }}
                        className="group relative inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-foreground bg-background border border-border rounded-lg shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-200 ease-out overflow-hidden hover:bg-muted/50"
                      >
                        {/* Subtle hover background */}
                        <div className="absolute inset-0 bg-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />

                        {/* Button content */}
                        <div className="relative flex items-center space-x-2">
                          <FileText className="w-4 h-4 group-hover:rotate-6 transition-transform duration-200" />
                          <span className="font-medium">View Documents</span>
                          <ChevronRight className="w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-200" />
                        </div>
                      </button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>


          </div>

          {/* Sidebar - Top Documents */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-lg">
                  <TrendingUp className="w-5 h-5 text-green-500" />
                  <span>Trending Documents</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {topDocuments.map((doc, index) => (
                    <div
                      key={doc.id}
                      className={`trending-item p-3 rounded-lg hover:bg-muted/50 cursor-pointer transition-all duration-300 border border-border hover:border-primary/50 animate-slide-in-right ${
                        index === 0 ? 'animate-delay-300' : index === 1 ? 'animate-delay-450' : index === 2 ? 'animate-delay-600' : index === 3 ? 'animate-delay-750' : 'animate-delay-900'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0">
                          {/* Award Ribbon Icons */}
                          {index === 0 && (
                            <div className="w-6 h-6 flex items-center justify-center text-lg" title="Most Popular">
                              🏆
                            </div>
                          )}
                          {index === 1 && (
                            <div className="w-6 h-6 flex items-center justify-center text-lg" title="Second Most Popular">
                              🥈
                            </div>
                          )}
                          {index === 2 && (
                            <div className="w-6 h-6 flex items-center justify-center text-lg" title="Third Most Popular">
                              🥉
                            </div>
                          )}
                          {index === 3 && (
                            <div className="w-6 h-6 flex items-center justify-center text-lg" title="Popular">
                              🎖️
                            </div>
                          )}
                          {index === 4 && (
                            <div className="w-6 h-6 flex items-center justify-center text-lg" title="Trending">
                              🏅
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-foreground truncate">
                            {doc.title}
                          </h4>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className="text-xs text-muted-foreground capitalize">
                              {doc.module}
                            </span>
                            <span className="text-xs text-muted-foreground">•</span>
                            <span className="text-xs text-muted-foreground flex items-center space-x-1">
                              <Eye className="w-3 h-3" />
                              <span>{doc.views}</span>
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KnowledgeBase;
