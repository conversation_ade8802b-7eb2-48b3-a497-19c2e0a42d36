import React, { useState, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Avatar, AvatarFallback } from './ui/avatar';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './ui/dialog';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Plus, Calendar, Clock, User, Flag, MoreHorizontal, Eye, MessageSquare, Paperclip, CheckSquare, Star, Archive, Copy, Edit3, Send } from 'lucide-react';
import './TaskBoard.css';

const TaskBoard: React.FC = () => {
  const [selectedView, setSelectedView] = useState('board');
  const [draggedTask, setDraggedTask] = useState<any>(null);
  const [draggedOver, setDraggedOver] = useState<string | null>(null);
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const [showQuickActions, setShowQuickActions] = useState<number | null>(null);
  const [showNewTaskModal, setShowNewTaskModal] = useState(false);
  const [newTaskColumn, setNewTaskColumn] = useState<string>('todo');
  const [newTaskForm, setNewTaskForm] = useState({
    title: '',
    description: '',
    assignee: '',
    priority: 'medium' as 'high' | 'medium' | 'low',
    dueDate: '',
    tags: ''
  });

  const columns = [
    { id: 'todo', title: 'To Do', color: 'bg-gray-100 dark:bg-gray-800' },
    { id: 'in-progress', title: 'In Progress', color: 'bg-blue-100 dark:bg-blue-900/20' },
    { id: 'review', title: 'Review', color: 'bg-yellow-100 dark:bg-yellow-900/20' },
    { id: 'done', title: 'Done', color: 'bg-green-100 dark:bg-green-900/20' }
  ];

  const [tasks, setTasks] = useState([
    {
      id: 1,
      title: 'Review customer support tickets',
      description: 'Audit quality of customer support responses for Q4',
      assignee: 'Louise Chen',
      priority: 'high',
      dueDate: '2024-01-15',
      status: 'todo',
      tags: ['audit', 'customer-support'],
      cover: 'https://images.unsplash.com/photo-1551434678-e076c223a692?w=400&h=200&fit=crop',
      comments: 3,
      attachments: 2,
      checklist: { completed: 2, total: 5 },
      watchers: 2,
      starred: false
    },
    {
      id: 2,
      title: 'Update QA documentation',
      description: 'Revise quality assurance guidelines based on new requirements',
      assignee: 'Sarah Wilson',
      priority: 'medium',
      dueDate: '2024-01-20',
      status: 'in-progress',
      tags: ['documentation', 'qa'],
      cover: null,
      comments: 1,
      attachments: 0,
      checklist: { completed: 1, total: 3 },
      watchers: 1,
      starred: true
    },
    {
      id: 3,
      title: 'Team performance analysis',
      description: 'Analyze team performance metrics for monthly report',
      assignee: 'Mike Johnson',
      priority: 'high',
      dueDate: '2024-01-18',
      status: 'review',
      tags: ['analytics', 'reporting'],
      cover: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=200&fit=crop',
      comments: 5,
      attachments: 3,
      checklist: { completed: 4, total: 4 },
      watchers: 3,
      starred: false
    },
    {
      id: 4,
      title: 'System maintenance checklist',
      description: 'Complete routine system maintenance tasks',
      assignee: 'John Smith',
      priority: 'low',
      dueDate: '2024-01-12',
      status: 'done',
      tags: ['maintenance', 'system'],
      cover: null,
      comments: 0,
      attachments: 1,
      checklist: { completed: 6, total: 6 },
      watchers: 1,
      starred: false
    }
  ]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 dark:text-red-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'low': return 'text-green-600 dark:text-green-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return '🔴';
      case 'medium': return '🟡';
      case 'low': return '🟢';
      default: return '⚪';
    }
  };

  // Drag and Drop handlers
  const handleDragStart = (e: React.DragEvent, task: any) => {
    setDraggedTask(task);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent, columnId: string) => {
    e.preventDefault();
    setDraggedOver(columnId);
  };

  const handleDragLeave = () => {
    setDraggedOver(null);
  };

  const handleDrop = (e: React.DragEvent, columnId: string) => {
    e.preventDefault();
    if (draggedTask && draggedTask.status !== columnId) {
      setTasks(prev => prev.map(task =>
        task.id === draggedTask.id
          ? { ...task, status: columnId }
          : task
      ));
    }
    setDraggedTask(null);
    setDraggedOver(null);
  };

  const handleStarTask = (taskId: number) => {
    setTasks(prev => prev.map(task =>
      task.id === taskId
        ? { ...task, starred: !task.starred }
        : task
    ));
  };

  const handleCreateTask = () => {
    if (!newTaskForm.title.trim()) return;

    const newTask = {
      id: Math.max(...tasks.map(t => t.id)) + 1,
      title: newTaskForm.title,
      description: newTaskForm.description,
      assignee: newTaskForm.assignee || 'Unassigned',
      priority: newTaskForm.priority,
      dueDate: newTaskForm.dueDate || new Date().toISOString().split('T')[0],
      status: newTaskColumn,
      tags: newTaskForm.tags ? newTaskForm.tags.split(',').map(tag => tag.trim()) : [],
      cover: null,
      comments: 0,
      attachments: 0,
      checklist: { completed: 0, total: 0 },
      watchers: 0,
      starred: false
    };

    setTasks(prev => [...prev, newTask]);
    setShowNewTaskModal(false);
    setNewTaskForm({
      title: '',
      description: '',
      assignee: '',
      priority: 'medium',
      dueDate: '',
      tags: ''
    });
    setNewTaskColumn('todo');
  };

  const openNewTaskModal = (columnId?: string) => {
    if (columnId) {
      setNewTaskColumn(columnId);
    }
    setShowNewTaskModal(true);
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Task Board</h1>
          <p className="text-muted-foreground">Manage and track team tasks and projects</p>
        </div>
        <div className="flex items-center space-x-2">
          <div className="flex bg-muted rounded-md p-1">
            <Button
              variant={selectedView === 'board' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setSelectedView('board')}
              className="h-8"
            >
              Board
            </Button>
            <Button
              variant={selectedView === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setSelectedView('list')}
              className="h-8"
            >
              List
            </Button>
          </div>
          <Button
            className="bg-primary hover:bg-primary/90"
            onClick={() => openNewTaskModal()}
          >
            <Plus className="h-4 w-4 mr-2" />
            New Task
          </Button>
        </div>
      </div>

      {/* Board View */}
      {selectedView === 'board' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {columns.map((column) => (
            <div
              key={column.id}
              className={`space-y-3 transition-all duration-200 ${
                draggedOver === column.id ? 'scale-105 bg-primary/5 rounded-lg p-2' : ''
              }`}
              onDragOver={(e) => handleDragOver(e, column.id)}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, column.id)}
            >
              <div className={`p-4 rounded-xl ${column.color} shadow-sm column-header`}>
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-foreground">{column.title}</h3>
                  <span className="text-sm text-muted-foreground bg-white/50 dark:bg-black/20 px-2 py-1 rounded-full">
                    {tasks.filter(task => task.status === column.id).length}
                  </span>
                </div>
              </div>

              <div className="space-y-3 min-h-[200px]">
                {tasks
                  .filter(task => task.status === column.id)
                  .map((task, index) => (
                    <Card
                      key={task.id}
                      className={`task-card card-hover-effect cursor-pointer group stagger-animation ${
                        draggedTask?.id === task.id ? 'drag-preview' : ''
                      } ${
                        task.priority === 'high' ? 'priority-high' :
                        task.priority === 'medium' ? 'priority-medium' :
                        task.priority === 'low' ? 'priority-low' : ''
                      }`}
                      draggable
                      onDragStart={(e) => handleDragStart(e, task)}
                      onMouseEnter={() => setHoveredCard(task.id)}
                      onMouseLeave={() => setHoveredCard(null)}
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      {/* Card Cover Image */}
                      {task.cover && (
                        <div className="h-24 bg-cover bg-center rounded-t-lg"
                             style={{ backgroundImage: `url(${task.cover})` }}>
                          <div className="h-full bg-black/20 rounded-t-lg flex items-end p-2">
                            {task.starred && (
                              <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            )}
                          </div>
                        </div>
                      )}

                      <CardContent className="p-4">
                        {/* Header with star and actions */}
                        <div className="flex items-start justify-between mb-3">
                          <h4 className="font-medium text-foreground text-sm leading-tight flex-1 pr-2">
                            {task.title}
                          </h4>
                          <div className="flex items-center space-x-1">
                            {!task.cover && task.starred && (
                              <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            )}
                            <div className="relative">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => setShowQuickActions(showQuickActions === task.id ? null : task.id)}
                              >
                                <MoreHorizontal className="h-3 w-3" />
                              </Button>

                              {/* Quick Actions Menu */}
                              {showQuickActions === task.id && (
                                <div className="absolute right-0 top-8 bg-card border border-border rounded-lg shadow-lg z-10 p-1 min-w-[120px] quick-action-menu">
                                  <button
                                    onClick={() => handleStarTask(task.id)}
                                    className="w-full flex items-center space-x-2 px-2 py-1 text-xs hover:bg-muted rounded text-left"
                                  >
                                    <Star className="w-3 h-3" />
                                    <span>{task.starred ? 'Unstar' : 'Star'}</span>
                                  </button>
                                  <button className="w-full flex items-center space-x-2 px-2 py-1 text-xs hover:bg-muted rounded text-left">
                                    <Edit3 className="w-3 h-3" />
                                    <span>Edit</span>
                                  </button>
                                  <button className="w-full flex items-center space-x-2 px-2 py-1 text-xs hover:bg-muted rounded text-left">
                                    <Copy className="w-3 h-3" />
                                    <span>Copy</span>
                                  </button>
                                  <button className="w-full flex items-center space-x-2 px-2 py-1 text-xs hover:bg-muted rounded text-left">
                                    <Archive className="w-3 h-3" />
                                    <span>Archive</span>
                                  </button>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-1 mb-3">
                          {task.tags.map((tag) => (
                            <span
                              key={tag}
                              className="px-2 py-1 text-xs bg-primary/10 text-primary rounded-md font-medium"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>

                        {/* Card Stats */}
                        <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
                          <div className="flex items-center space-x-3">
                            {task.comments > 0 && (
                              <div className="flex items-center space-x-1">
                                <MessageSquare className="h-3 w-3" />
                                <span>{task.comments}</span>
                              </div>
                            )}
                            {task.attachments > 0 && (
                              <div className="flex items-center space-x-1">
                                <Paperclip className="h-3 w-3" />
                                <span>{task.attachments}</span>
                              </div>
                            )}
                            {task.checklist.total > 0 && (
                              <div className="flex items-center space-x-1">
                                <CheckSquare className="h-3 w-3" />
                                <span>{task.checklist.completed}/{task.checklist.total}</span>
                              </div>
                            )}
                          </div>

                          <div className="flex items-center space-x-2">
                            <span className={getPriorityColor(task.priority)}>
                              {getPriorityIcon(task.priority)}
                            </span>
                            <div className="flex items-center space-x-1">
                              <Calendar className="h-3 w-3" />
                              <span>{new Date(task.dueDate).toLocaleDateString()}</span>
                            </div>
                          </div>
                        </div>

                        {/* Assignee and Watchers */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-1">
                            <Avatar className="h-6 w-6">
                              <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                                {task.assignee.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <span className="text-xs text-muted-foreground">{task.assignee}</span>
                          </div>

                          {task.watchers > 0 && (
                            <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                              <Eye className="h-3 w-3" />
                              <span>{task.watchers}</span>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}

                {/* Add Card Button */}
                <Button
                  variant="ghost"
                  className="w-full h-12 border-2 border-dashed border-muted-foreground/30 hover:border-primary/50 hover:bg-primary/5 add-card-button"
                  onClick={() => openNewTaskModal(column.id)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add a card
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* List View */}
      {selectedView === 'list' && (
        <Card>
          <CardHeader>
            <CardTitle>All Tasks</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {tasks.map((task) => (
                <div
                  key={task.id}
                  className="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <span className={getPriorityColor(task.priority)}>
                        {getPriorityIcon(task.priority)}
                      </span>
                      <div>
                        <h4 className="font-medium text-foreground">{task.title}</h4>
                        <p className="text-sm text-muted-foreground">{task.description}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                          {task.assignee.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm text-muted-foreground">{task.assignee}</span>
                    </div>
                    
                    <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>{new Date(task.dueDate).toLocaleDateString()}</span>
                    </div>
                    
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      task.status === 'done' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                      task.status === 'in-progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
                      task.status === 'review' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-400'
                    }`}>
                      {columns.find(col => col.id === task.status)?.title}
                    </span>
                    
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* New Task Modal */}
      <Dialog open={showNewTaskModal} onOpenChange={setShowNewTaskModal}>
        <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Plus className="w-5 h-5 text-primary" />
              <span>Create New Task</span>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 mt-4">
            {/* Title */}
            <div className="space-y-2">
              <Label htmlFor="task-title">Title *</Label>
              <Input
                id="task-title"
                placeholder="Enter task title..."
                value={newTaskForm.title}
                onChange={(e) => setNewTaskForm(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="task-description">Description</Label>
              <Textarea
                id="task-description"
                placeholder="Enter task description..."
                value={newTaskForm.description}
                onChange={(e) => setNewTaskForm(prev => ({ ...prev, description: e.target.value }))}
                className="min-h-[80px]"
              />
            </div>

            {/* Assignee and Priority */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="task-assignee">Assignee</Label>
                <Input
                  id="task-assignee"
                  placeholder="Enter assignee name..."
                  value={newTaskForm.assignee}
                  onChange={(e) => setNewTaskForm(prev => ({ ...prev, assignee: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="task-priority">Priority</Label>
                <Select value={newTaskForm.priority} onValueChange={(value: 'high' | 'medium' | 'low') => setNewTaskForm(prev => ({ ...prev, priority: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">🟢 Low</SelectItem>
                    <SelectItem value="medium">🟡 Medium</SelectItem>
                    <SelectItem value="high">🔴 High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Due Date and Column */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="task-due-date">Due Date</Label>
                <Input
                  id="task-due-date"
                  type="date"
                  value={newTaskForm.dueDate}
                  onChange={(e) => setNewTaskForm(prev => ({ ...prev, dueDate: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="task-column">Column</Label>
                <Select value={newTaskColumn} onValueChange={setNewTaskColumn}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select column" />
                  </SelectTrigger>
                  <SelectContent>
                    {columns.map(column => (
                      <SelectItem key={column.id} value={column.id}>
                        {column.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Tags */}
            <div className="space-y-2">
              <Label htmlFor="task-tags">Tags</Label>
              <Input
                id="task-tags"
                placeholder="Enter tags separated by commas..."
                value={newTaskForm.tags}
                onChange={(e) => setNewTaskForm(prev => ({ ...prev, tags: e.target.value }))}
              />
              <p className="text-xs text-muted-foreground">Separate multiple tags with commas</p>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button variant="outline" onClick={() => setShowNewTaskModal(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleCreateTask}
                disabled={!newTaskForm.title.trim()}
                className="bg-primary hover:bg-primary/90"
              >
                <Send className="w-4 h-4 mr-2" />
                Create Task
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TaskBoard;
