# API Documentation

## Overview

ZWIFTLLY uses Supabase as its backend-as-a-service, providing auto-generated REST APIs, real-time subscriptions, and database functions.

## Authentication

### Authentication Flow
```
1. User initiates Google OAuth
2. Supabase handles OAuth flow
3. JWT token issued on success
4. Token used for subsequent API calls
```

### Headers
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## Database Schema

### Users Table
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  first_name TEXT,
  last_name TEXT,
  picture TEXT,
  role user_role DEFAULT 'AGENT',
  organization_id UUID REFERENCES organizations(id),
  is_active BOOLEAN DEFAULT true,
  last_login_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Organizations Table
```sql
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  domain TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  default_role user_role DEFAULT 'AGENT',
  admin_emails TEXT[] DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Tasks Table
```sql
CREATE TABLE tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  description TEXT,
  status task_status DEFAULT 'TODO',
  priority task_priority DEFAULT 'MEDIUM',
  assignee_id UUID REFERENCES users(id),
  created_by UUID REFERENCES users(id),
  due_date TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Attendance Records Table
```sql
CREATE TABLE attendance_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  date DATE NOT NULL,
  clock_in TIMESTAMPTZ,
  clock_out TIMESTAMPTZ,
  break_start TIMESTAMPTZ,
  break_end TIMESTAMPTZ,
  total_hours DECIMAL(5,2),
  status attendance_status DEFAULT 'PRESENT',
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Announcements Table
```sql
CREATE TABLE announcements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  category announcement_category DEFAULT 'GENERAL',
  is_published BOOLEAN DEFAULT false,
  published_at TIMESTAMPTZ,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## REST API Endpoints

### Users

#### Get All Users
```http
GET /rest/v1/users
```

**Query Parameters:**
- `select` - Fields to select
- `is_active` - Filter by active status
- `organization_id` - Filter by organization

**Response:**
```json
[
  {
    "id": "uuid",
    "email": "<EMAIL>",
    "name": "John Doe",
    "role": "AGENT",
    "organization": {
      "id": "uuid",
      "name": "Company Name"
    }
  }
]
```

#### Get User by ID
```http
GET /rest/v1/users?id=eq.{user_id}
```

#### Update User
```http
PATCH /rest/v1/users?id=eq.{user_id}
Content-Type: application/json

{
  "name": "Updated Name",
  "role": "MANAGER"
}
```

### Tasks

#### Get All Tasks
```http
GET /rest/v1/tasks
```

**Query Parameters:**
- `assignee_id` - Filter by assignee
- `status` - Filter by status
- `order` - Sort order

**Response:**
```json
[
  {
    "id": "uuid",
    "title": "Task Title",
    "description": "Task description",
    "status": "TODO",
    "priority": "HIGH",
    "assignee": {
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "due_date": "2024-12-31T23:59:59Z"
  }
]
```

#### Create Task
```http
POST /rest/v1/tasks
Content-Type: application/json

{
  "title": "New Task",
  "description": "Task description",
  "assignee_id": "uuid",
  "priority": "HIGH",
  "due_date": "2024-12-31T23:59:59Z"
}
```

#### Update Task
```http
PATCH /rest/v1/tasks?id=eq.{task_id}
Content-Type: application/json

{
  "status": "IN_PROGRESS",
  "notes": "Started working on this"
}
```

### Attendance

#### Get Attendance Records
```http
GET /rest/v1/attendance_records
```

**Query Parameters:**
- `user_id` - Filter by user
- `date` - Filter by date
- `order` - Sort order

#### Clock Action (Database Function)
```http
POST /rest/v1/rpc/handle_clock_action
Content-Type: application/json

{
  "action_type": "clock_in",
  "location": "Office"
}
```

**Response:**
```json
{
  "success": true,
  "attendance_record": {
    "id": "uuid",
    "user_id": "uuid",
    "date": "2024-06-22",
    "clock_in": "2024-06-22T09:00:00Z"
  }
}
```

### Announcements

#### Get Announcements
```http
GET /rest/v1/announcements?is_published=eq.true
```

#### Create Announcement
```http
POST /rest/v1/announcements
Content-Type: application/json

{
  "title": "Important Update",
  "content": "Announcement content",
  "category": "GENERAL",
  "is_published": true
}
```

## Real-time Subscriptions

### Subscribe to Table Changes
```javascript
const subscription = supabase
  .channel('table-changes')
  .on('postgres_changes', 
    { 
      event: '*', 
      schema: 'public', 
      table: 'tasks' 
    }, 
    (payload) => {
      console.log('Change received!', payload);
    }
  )
  .subscribe();
```

### Subscribe to User-specific Changes
```javascript
const subscription = supabase
  .channel('user-tasks')
  .on('postgres_changes', 
    { 
      event: '*', 
      schema: 'public', 
      table: 'tasks',
      filter: `assignee_id=eq.${userId}`
    }, 
    (payload) => {
      console.log('Task update:', payload);
    }
  )
  .subscribe();
```

## Database Functions

### handle_clock_action
Processes clock in/out actions and updates attendance records.

**Parameters:**
- `action_type` - 'clock_in', 'clock_out', 'break_start', 'break_end'
- `location` - Optional location string

### get_user_dashboard
Returns aggregated dashboard data for a user.

**Returns:**
```json
{
  "user": { /* user data */ },
  "today_attendance": { /* today's attendance */ },
  "pending_tasks": [ /* user's pending tasks */ ],
  "recent_announcements": [ /* recent announcements */ ],
  "unread_notifications": 5
}
```

### create_notification
Creates a new notification for a user.

**Parameters:**
- `user_id` - Target user UUID
- `type` - Notification type
- `title` - Notification title
- `message` - Notification message
- `data` - Optional JSON data

## Row Level Security (RLS)

### Users Table Policy
```sql
-- Users can view users in their organization
CREATE POLICY "Users can view organization users" ON users
  FOR SELECT USING (
    organization_id = (
      SELECT organization_id FROM users WHERE id = auth.uid()
    )
  );

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (id = auth.uid());
```

### Tasks Table Policy
```sql
-- Users can view tasks assigned to them or created by them
CREATE POLICY "Users can view relevant tasks" ON tasks
  FOR SELECT USING (
    assignee_id = auth.uid() OR 
    created_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('SUPER_ADMIN', 'ADMIN', 'MANAGER')
    )
  );
```

## Error Handling

### Standard Error Response
```json
{
  "error": {
    "message": "Error description",
    "details": "Additional error details",
    "hint": "Suggestion for fixing the error",
    "code": "ERROR_CODE"
  }
}
```

### Common Error Codes
- `PGRST116` - No rows found
- `23505` - Unique constraint violation
- `23503` - Foreign key constraint violation
- `42501` - Insufficient privileges

## Rate Limiting

Supabase applies rate limiting based on your plan:
- **Free tier**: 500 requests per second
- **Pro tier**: 1000 requests per second
- **Enterprise**: Custom limits

## Best Practices

### Query Optimization
```javascript
// Good: Select only needed fields
const { data } = await supabase
  .from('users')
  .select('id, name, email')
  .eq('is_active', true);

// Good: Use filters to reduce data transfer
const { data } = await supabase
  .from('tasks')
  .select('*, assignee:users(name)')
  .eq('status', 'TODO')
  .limit(10);
```

### Error Handling
```javascript
try {
  const { data, error } = await supabase
    .from('users')
    .select('*');
    
  if (error) throw error;
  
  return data;
} catch (error) {
  console.error('Database error:', error);
  throw new Error('Failed to fetch users');
}
```

### Real-time Cleanup
```javascript
useEffect(() => {
  const subscription = supabase
    .channel('changes')
    .on('postgres_changes', { /* config */ }, handler)
    .subscribe();
    
  return () => {
    subscription.unsubscribe();
  };
}, []);
```
