# Development Guide

## Getting Started

### Prerequisites
- Node.js 18+ and npm 9+
- Git
- Supabase account
- Vercel account (for deployment)

### Initial Setup

1. **Clone the repository**
```bash
git clone https://github.com/DaddyRay18/ZWIFTLLY-Team-Management-System.git
cd ZWIFTLLY-Team-Management-System
```

2. **Install dependencies**
```bash
npm install
```

3. **Environment Configuration**
```bash
cd zwiftlly-frontend
cp .env.example .env.local
```

Edit `.env.local` with your configuration:
```env
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Application Configuration
VITE_APP_NAME=ZWIFTLLY Team Management System
VITE_ENVIRONMENT=development
```

4. **Start development server**
```bash
npm run dev
```

## Development Workflow

### Branch Strategy
- `master` - Production branch
- `development` - Development branch
- `feature/*` - Feature branches
- `hotfix/*` - Hotfix branches

### Commit Convention
Follow conventional commits:
```
feat: add new feature
fix: bug fix
docs: documentation changes
style: formatting changes
refactor: code refactoring
test: adding tests
chore: maintenance tasks
```

### Code Quality Standards

#### TypeScript
- Use strict TypeScript configuration
- Define interfaces for all data structures
- Avoid `any` type usage
- Use proper type annotations

#### React Best Practices
- Use functional components with hooks
- Implement proper error boundaries
- Follow React naming conventions
- Use React.memo for performance optimization

#### Component Structure
```tsx
// ComponentName.tsx
import React from 'react';
import { ComponentProps } from './types';

interface ComponentNameProps {
  // Props interface
}

const ComponentName: React.FC<ComponentNameProps> = ({ 
  prop1, 
  prop2 
}) => {
  // Component logic
  
  return (
    <div>
      {/* JSX */}
    </div>
  );
};

export default ComponentName;
```

#### Custom Hooks
```tsx
// useCustomHook.ts
import { useState, useEffect } from 'react';

export const useCustomHook = (dependency: string) => {
  const [state, setState] = useState(null);
  
  useEffect(() => {
    // Effect logic
  }, [dependency]);
  
  return { state, setState };
};
```

### Testing Guidelines

#### Unit Testing
```tsx
// Component.test.tsx
import { render, screen } from '@testing-library/react';
import Component from './Component';

describe('Component', () => {
  it('should render correctly', () => {
    render(<Component />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });
});
```

#### Integration Testing
```tsx
// Feature.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { AuthProvider } from '@/contexts/AuthContext';
import Feature from './Feature';

describe('Feature Integration', () => {
  it('should handle user interaction', async () => {
    render(
      <AuthProvider>
        <Feature />
      </AuthProvider>
    );
    
    fireEvent.click(screen.getByRole('button'));
    expect(await screen.findByText('Success')).toBeInTheDocument();
  });
});
```

### Performance Guidelines

#### Code Splitting
```tsx
// Lazy loading components
const LazyComponent = React.lazy(() => import('./LazyComponent'));

const App = () => (
  <Suspense fallback={<Loading />}>
    <LazyComponent />
  </Suspense>
);
```

#### Memoization
```tsx
// Memoize expensive calculations
const ExpensiveComponent = React.memo(({ data }) => {
  const expensiveValue = useMemo(() => {
    return data.reduce((acc, item) => acc + item.value, 0);
  }, [data]);
  
  return <div>{expensiveValue}</div>;
});
```

### State Management

#### Context Pattern
```tsx
// DataContext.tsx
interface DataContextType {
  data: Data[];
  loading: boolean;
  error: string | null;
  fetchData: () => Promise<void>;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within DataProvider');
  }
  return context;
};
```

### API Integration

#### Supabase Queries
```tsx
// Data fetching
const fetchUsers = async () => {
  const { data, error } = await supabase
    .from('users')
    .select(`
      *,
      organizations (*)
    `)
    .eq('is_active', true);
    
  if (error) throw error;
  return data;
};
```

#### Error Handling
```tsx
// Error boundary
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true };
  }
  
  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }
    
    return this.props.children;
  }
}
```

### Styling Guidelines

#### Tailwind CSS
- Use utility classes for styling
- Create custom components for repeated patterns
- Use CSS variables for theming
- Follow mobile-first responsive design

#### Component Styling
```tsx
// Styled component example
const StyledButton = ({ variant, children, ...props }) => {
  const baseClasses = "px-4 py-2 rounded-lg font-medium transition-colors";
  const variantClasses = {
    primary: "bg-blue-600 text-white hover:bg-blue-700",
    secondary: "bg-gray-200 text-gray-900 hover:bg-gray-300"
  };
  
  return (
    <button 
      className={`${baseClasses} ${variantClasses[variant]}`}
      {...props}
    >
      {children}
    </button>
  );
};
```

### Debugging

#### Development Tools
- React Developer Tools
- Redux DevTools (if using Redux)
- Browser DevTools
- Supabase Dashboard

#### Logging
```tsx
// Structured logging
const logger = {
  info: (message: string, data?: any) => {
    console.log(`[INFO] ${message}`, data);
  },
  error: (message: string, error?: Error) => {
    console.error(`[ERROR] ${message}`, error);
  },
  debug: (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[DEBUG] ${message}`, data);
    }
  }
};
```

### Deployment

#### Build Process
```bash
npm run build        # Build for production
npm run preview      # Preview production build
npm run deploy       # Deploy to Vercel
```

#### Environment Variables
- Development: `.env.local`
- Production: Vercel dashboard
- Never commit sensitive data

### Troubleshooting

#### Common Issues
1. **Build Errors**: Check TypeScript errors and missing dependencies
2. **Runtime Errors**: Use error boundaries and proper error handling
3. **Performance Issues**: Use React DevTools Profiler
4. **Authentication Issues**: Check Supabase configuration and RLS policies

#### Debug Commands
```bash
npm run type-check   # Check TypeScript errors
npm run lint         # Check code quality
npm run build        # Test production build
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Ensure all tests pass
6. Submit a pull request

### Resources

- [React Documentation](https://react.dev/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Supabase Documentation](https://supabase.com/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Vercel Documentation](https://vercel.com/docs)
