import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { User, X, Edit } from 'lucide-react';

interface ProfileUpdatePopupProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdateProfile: () => void;
  onSkip: () => void;
}

const ProfileUpdatePopup: React.FC<ProfileUpdatePopupProps> = ({
  isOpen,
  onClose,
  onUpdateProfile,
  onSkip
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm animate-fade-in"
        onClick={onClose}
      />
      
      {/* Popup */}
      <div className="relative bg-card border border-border/50 rounded-xl shadow-2xl max-w-md w-full mx-4 animate-scale-in">
        {/* Header */}
        <CardHeader className="p-6 border-b border-border/50 bg-gradient-to-r from-background to-muted/20">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl flex items-center space-x-3">
              <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-xl shadow-sm border border-blue-200 dark:border-blue-800/30">
                <User className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <span className="text-foreground font-bold text-xl">Welcome to ZWIFTLLY!</span>
                <div className="text-sm text-muted-foreground font-medium">Complete your profile</div>
              </div>
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="w-8 h-8 p-0 hover:bg-muted"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>

        {/* Content */}
        <CardContent className="p-6 space-y-4">
          <div className="text-center space-y-3">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto">
              <Edit className="w-8 h-8 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-foreground">Update Your Profile</h3>
              <p className="text-muted-foreground text-sm leading-relaxed">
                We recommend updating your profile information to get the most out of ZWIFTLLY. 
                This helps your team members know more about you and improves collaboration.
              </p>
            </div>
          </div>

          <div className="bg-muted/20 rounded-lg p-4 border border-border/30">
            <h4 className="font-medium text-foreground mb-2">What you can update:</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Personal information (name, phone)</li>
              <li>• Company role and position</li>
              <li>• Hire date for service calculation</li>
              <li>• Profile picture or avatar</li>
            </ul>
          </div>

          <div className="text-xs text-muted-foreground text-center">
            <p>Note: You can only update your profile information once. After that, you'll need to request changes through an admin.</p>
          </div>
        </CardContent>

        {/* Footer */}
        <div className="flex items-center justify-between space-x-3 p-6 border-t border-border/50 bg-muted/20">
          <Button
            variant="outline"
            onClick={onSkip}
            className="border-border/50"
          >
            Skip for Now
          </Button>
          <Button
            onClick={onUpdateProfile}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
          >
            Update Profile
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProfileUpdatePopup;
