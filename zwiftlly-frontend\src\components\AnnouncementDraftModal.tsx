import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  X, 
  Send, 
  Save, 
  Eye, 
  Bold, 
  Italic, 
  List, 
  Link, 
  Image, 
  Paperclip,
  Users,
  Calendar,
  Clock,
  AlertCircle,
  CheckCircle,
  Sparkles
} from 'lucide-react';
import { useAuth } from '@/contexts/SupabaseAuthContext';

interface AnnouncementDraftModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (announcement: any) => void;
}

const AnnouncementDraftModal: React.FC<AnnouncementDraftModalProps> = ({
  isOpen,
  onClose,
  onSave
}) => {
  const { user } = useAuth();
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [category, setCategory] = useState<'qa' | 'engineering' | 'management'>('qa');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high' | 'urgent'>('medium');
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');
  const [scheduleDate, setScheduleDate] = useState('');
  const [isDraft, setIsDraft] = useState(true);
  const [isPreview, setIsPreview] = useState(false);

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleSave = (publish = false) => {
    const announcement = {
      id: Date.now().toString(),
      title,
      content,
      author: user?.name || 'Unknown',
      authorEmail: user?.email || '',
      category,
      priority,
      tags,
      scheduleDate: scheduleDate || new Date().toISOString(),
      isDraft: !publish,
      createdAt: new Date().toLocaleString(),
      views: 0,
      comments: []
    };

    onSave(announcement);
    handleClose();
  };

  const handleClose = () => {
    setTitle('');
    setContent('');
    setCategory('qa');
    setPriority('medium');
    setTags([]);
    setNewTag('');
    setScheduleDate('');
    setIsDraft(true);
    setIsPreview(false);
    onClose();
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400';
      case 'medium': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'qa': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400';
      case 'engineering': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'management': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-card border border-border/50">
        <DialogHeader className="border-b border-border/50 pb-6">
          <DialogTitle className="text-2xl font-bold text-foreground flex items-center space-x-3">
            <div className="w-12 h-12 gradient-ai-assistant rounded-xl flex items-center justify-center shadow-lg">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <div>
              <span>Create Announcement</span>
              <div className="text-sm text-muted-foreground font-normal">Draft and publish team announcements</div>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 p-6">
          {!isPreview ? (
            <>
              {/* Title and Category Row */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <div className="lg:col-span-2">
                  <Label htmlFor="title" className="text-sm font-medium text-foreground mb-2 block">
                    Announcement Title *
                  </Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Enter announcement title..."
                    className="text-lg font-medium"
                  />
                </div>
                <div>
                  <Label htmlFor="category" className="text-sm font-medium text-foreground mb-2 block">
                    Category *
                  </Label>
                  <Select value={category} onValueChange={(value: any) => setCategory(value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="qa">QA</SelectItem>
                      <SelectItem value="engineering">Engineering</SelectItem>
                      <SelectItem value="management">Management</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Priority and Schedule Row */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="priority" className="text-sm font-medium text-foreground mb-2 block">
                    Priority Level
                  </Label>
                  <Select value={priority} onValueChange={(value: any) => setPriority(value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low Priority</SelectItem>
                      <SelectItem value="medium">Medium Priority</SelectItem>
                      <SelectItem value="high">High Priority</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="schedule" className="text-sm font-medium text-foreground mb-2 block">
                    Schedule Publication (Optional)
                  </Label>
                  <Input
                    id="schedule"
                    type="datetime-local"
                    value={scheduleDate}
                    onChange={(e) => setScheduleDate(e.target.value)}
                  />
                </div>
              </div>

              {/* Content Editor */}
              <div>
                <Label htmlFor="content" className="text-sm font-medium text-foreground mb-2 block">
                  Announcement Content *
                </Label>
                
                {/* Formatting Toolbar */}
                <div className="flex items-center space-x-2 p-3 bg-muted/30 rounded-t-lg border border-b-0 border-border/50">
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <Bold className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <Italic className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <List className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <Link className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <Image className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <Paperclip className="w-4 h-4" />
                  </Button>
                </div>

                <Textarea
                  id="content"
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="Write your announcement content here..."
                  className="min-h-[200px] rounded-t-none border-t-0 resize-none"
                />
              </div>

              {/* Tags */}
              <div>
                <Label className="text-sm font-medium text-foreground mb-2 block">
                  Tags (Optional)
                </Label>
                <div className="flex flex-wrap gap-2 mb-3">
                  {tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center space-x-1">
                      <span>{tag}</span>
                      <button
                        onClick={() => handleRemoveTag(tag)}
                        className="ml-1 hover:text-destructive"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
                <div className="flex space-x-2">
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="Add a tag..."
                    onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                    className="flex-1"
                  />
                  <Button onClick={handleAddTag} variant="outline" size="sm">
                    Add Tag
                  </Button>
                </div>
              </div>
            </>
          ) : (
            /* Preview Mode */
            <div className="space-y-6">
              <div className="bg-muted/20 rounded-lg p-6 border border-border/50">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <Badge className={getCategoryColor(category)}>
                      {category.toUpperCase()}
                    </Badge>
                    <Badge className={getPriorityColor(priority)}>
                      {priority.toUpperCase()}
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {scheduleDate ? `Scheduled: ${new Date(scheduleDate).toLocaleString()}` : 'Publish immediately'}
                  </div>
                </div>
                
                <h2 className="text-2xl font-bold text-foreground mb-4">{title || 'Untitled Announcement'}</h2>
                
                <div className="prose prose-sm max-w-none text-foreground mb-4">
                  {content.split('\n').map((paragraph, index) => (
                    <p key={index} className="mb-2">{paragraph}</p>
                  ))}
                </div>

                {tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mb-4">
                    {tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        #{tag}
                      </Badge>
                    ))}
                  </div>
                )}

                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Users className="w-4 h-4" />
                    <span>By {user?.name || 'Unknown'}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4" />
                    <span>{new Date().toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>{new Date().toLocaleTimeString()}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-6 border-t border-border/50">
            <div className="flex items-center space-x-3">
              <Button
                onClick={() => setIsPreview(!isPreview)}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <Eye className="w-4 h-4" />
                <span>{isPreview ? 'Edit' : 'Preview'}</span>
              </Button>
            </div>

            <div className="flex items-center space-x-3">
              <Button onClick={handleClose} variant="outline">
                Cancel
              </Button>
              <Button
                onClick={() => handleSave(false)}
                variant="secondary"
                className="flex items-center space-x-2"
                disabled={!title.trim() || !content.trim()}
              >
                <Save className="w-4 h-4" />
                <span>Save Draft</span>
              </Button>
              <Button
                onClick={() => handleSave(true)}
                className="gradient-button-primary hover:opacity-90 text-primary-foreground flex items-center space-x-2"
                disabled={!title.trim() || !content.trim()}
              >
                <Send className="w-4 h-4" />
                <span>Publish Now</span>
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AnnouncementDraftModal;
