import React from 'react';

interface StatusItem {
  id: string;
  label: string;
  color: string;
  pattern: 'solid' | 'diagonal' | 'dots' | 'waves';
  bgColor: string;
}

const StatusLegend: React.FC = () => {
  const statusItems: StatusItem[] = [
    {
      id: 'online',
      label: 'Online',
      color: '#22c55e',
      pattern: 'solid',
      bgColor: 'bg-green-500'
    },
    {
      id: 'technical',
      label: 'Technical Issue',
      color: '#3b82f6',
      pattern: 'diagonal',
      bgColor: 'bg-blue-500'
    },
    {
      id: 'offline',
      label: 'Offline',
      color: '#8b5cf6',
      pattern: 'solid',
      bgColor: 'bg-purple-500'
    },
    {
      id: 'break',
      label: 'Break',
      color: '#f59e0b',
      pattern: 'dots',
      bgColor: 'bg-amber-500'
    },
    {
      id: 'meeting',
      label: 'Meeting',
      color: '#ef4444',
      pattern: 'waves',
      bgColor: 'bg-red-500'
    },
    {
      id: 'scheduled',
      label: 'Scheduled',
      color: '#6b7280',
      pattern: 'solid',
      bgColor: 'bg-gray-500'
    },
    {
      id: 'absent',
      label: 'Absent',
      color: '#dc2626',
      pattern: 'solid',
      bgColor: 'bg-red-600'
    }
  ];

  const getPatternStyle = (item: StatusItem) => {
    const baseStyle = {
      width: '24px',
      height: '16px',
      borderRadius: '2px',
      display: 'inline-block',
      marginRight: '8px'
    };

    switch (item.pattern) {
      case 'solid':
        return {
          ...baseStyle,
          backgroundColor: item.color
        };
      case 'diagonal':
        return {
          ...baseStyle,
          background: `repeating-linear-gradient(
            45deg,
            ${item.color},
            ${item.color} 2px,
            transparent 2px,
            transparent 4px
          )`
        };
      case 'dots':
        return {
          ...baseStyle,
          background: `radial-gradient(circle, ${item.color} 1px, transparent 1px)`,
          backgroundSize: '4px 4px'
        };
      case 'waves':
        return {
          ...baseStyle,
          background: `repeating-linear-gradient(
            90deg,
            ${item.color} 0px,
            ${item.color} 2px,
            transparent 2px,
            transparent 4px
          )`
        };
      default:
        return {
          ...baseStyle,
          backgroundColor: item.color
        };
    }
  };

  return (
    <div className="bg-card rounded-lg p-4 shadow-sm border">
      <h3 className="text-sm font-semibold text-foreground mb-3">Status Legend</h3>
      <div className="flex flex-wrap gap-4">
        {statusItems.map((item) => (
          <div key={item.id} className="flex items-center">
            <div style={getPatternStyle(item)}></div>
            <span className="text-xs text-muted-foreground">{item.label}</span>
          </div>
        ))}
        {/* Live Time Indicator */}
        <div className="flex items-center">
          <div className="w-6 h-4 bg-red-500 rounded-sm mr-2"></div>
          <span className="text-xs text-muted-foreground">Live Time</span>
        </div>
      </div>
    </div>
  );
};

export default StatusLegend;
