import React, { useState } from 'react';
import { 
  TrendingUp, 
  Users, 
  Target, 
  Clock, 
  Award, 
  BarChart3,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  ChevronDown,
  Star,
  Activity
} from 'lucide-react';
import { useCompany } from '../contexts/CompanyContext';
import { useData } from '../contexts/DataContext';

interface TeamPerformanceProps {
  darkMode?: boolean;
}

const TeamPerformance: React.FC<TeamPerformanceProps> = ({ darkMode = false }) => {
  const { users } = useCompany();
  const { tasks } = useData();
  
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [selectedMetric, setSelectedMetric] = useState('all');
  const [loading, setLoading] = useState(false);

  // Mock performance data
  const performanceMetrics = {
    overall: {
      score: 8.7,
      trend: '+12%',
      tasks: 156,
      hours: 1240,
      attendance: 94.5,
      quality: 9.2
    },
    teamMembers: users.map((user, index) => ({
      id: user.id,
      name: user.full_name || 'Unknown User',
      role: user.role || 'USER',
      avatar: user.avatar_url,
      metrics: {
        auditQuality: 8.5 + (Math.random() * 1.5),
        idleTime: Math.floor(Math.random() * 15) + 5,
        attendance: 90 + (Math.random() * 10),
        totalAudited: Math.floor(Math.random() * 50) + 20,
        performanceScore: 7.5 + (Math.random() * 2.5)
      }
    })),
    trends: [
      { period: 'Jan', score: 8.2, tasks: 120, hours: 960 },
      { period: 'Feb', score: 8.4, tasks: 135, hours: 1080 },
      { period: 'Mar', score: 8.7, tasks: 156, hours: 1240 },
      { period: 'Apr', score: 8.9, tasks: 168, hours: 1344 }
    ]
  };

  const handleRefresh = async () => {
    setLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setLoading(false);
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 9) return 'text-green-600 dark:text-green-400';
    if (score >= 7) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getPerformanceBg = (score: number) => {
    if (score >= 9) return 'bg-green-100 dark:bg-green-900/20';
    if (score >= 7) return 'bg-yellow-100 dark:bg-yellow-900/20';
    return 'bg-red-100 dark:bg-red-900/20';
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <TrendingUp className="w-8 h-8 text-primary" />
              <div>
                <h1 className="text-3xl font-bold text-foreground">Team Performance</h1>
                <p className="text-muted-foreground">Monitor team metrics and productivity insights</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className={`px-3 py-2 border rounded-lg ${
                  darkMode 
                    ? 'bg-gray-800 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
                <option value="year">This Year</option>
              </select>
              <button
                onClick={handleRefresh}
                disabled={loading}
                className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:opacity-90 disabled:opacity-50 transition-opacity"
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                <span>Refresh</span>
              </button>
            </div>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className={`rounded-lg border p-6 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Overall Score
                </p>
                <p className="text-2xl font-bold text-foreground mt-1">
                  {performanceMetrics.overall.score}/10
                </p>
                <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                  {performanceMetrics.overall.trend} from last period
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <Star className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </div>

          <div className={`rounded-lg border p-6 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Tasks Completed
                </p>
                <p className="text-2xl font-bold text-foreground mt-1">
                  {performanceMetrics.overall.tasks}
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  This {selectedPeriod}
                </p>
              </div>
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <Target className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </div>

          <div className={`rounded-lg border p-6 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Hours Worked
                </p>
                <p className="text-2xl font-bold text-foreground mt-1">
                  {performanceMetrics.overall.hours}h
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  Team total
                </p>
              </div>
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <Clock className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </div>

          <div className={`rounded-lg border p-6 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Attendance Rate
                </p>
                <p className="text-2xl font-bold text-foreground mt-1">
                  {performanceMetrics.overall.attendance}%
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  Team average
                </p>
              </div>
              <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
          </div>
        </div>

        {/* Team Members Performance */}
        <div className={`rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-foreground">Team Members Performance</h2>
              <div className="flex items-center space-x-3">
                <select
                  value={selectedMetric}
                  onChange={(e) => setSelectedMetric(e.target.value)}
                  className={`px-3 py-2 border rounded-lg text-sm ${
                    darkMode 
                      ? 'bg-gray-700 border-gray-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                >
                  <option value="all">All Metrics</option>
                  <option value="quality">Audit Quality</option>
                  <option value="attendance">Attendance</option>
                  <option value="productivity">Productivity</option>
                </select>
                <button className={`flex items-center space-x-2 px-3 py-2 border rounded-lg text-sm transition-colors ${
                  darkMode 
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-700' 
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}>
                  <Download className="w-4 h-4" />
                  <span>Export</span>
                </button>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <tr>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    darkMode ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    Team Member
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    darkMode ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    Audit Quality
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    darkMode ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    Idle Time
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    darkMode ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    Attendance
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    darkMode ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    Total Audited
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    darkMode ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    Performance
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {performanceMetrics.teamMembers.map((member) => (
                  <tr key={member.id} className={`${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'} transition-colors`}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                          {member.avatar ? (
                            <img src={member.avatar} alt={member.name} className="w-10 h-10 rounded-full object-cover" />
                          ) : (
                            member.name.charAt(0).toUpperCase()
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-foreground">{member.name}</div>
                          <div className="text-sm text-muted-foreground">{member.role}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        getPerformanceBg(member.metrics.auditQuality)
                      }`}>
                        <span className={getPerformanceColor(member.metrics.auditQuality)}>
                          {member.metrics.auditQuality.toFixed(1)}/10
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                      {member.metrics.idleTime}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                      {member.metrics.attendance.toFixed(1)}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                      {member.metrics.totalAudited}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-3">
                          <div 
                            className="bg-primary h-2 rounded-full" 
                            style={{ width: `${(member.metrics.performanceScore / 10) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium text-foreground">
                          {member.metrics.performanceScore.toFixed(1)}
                        </span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeamPerformance;
