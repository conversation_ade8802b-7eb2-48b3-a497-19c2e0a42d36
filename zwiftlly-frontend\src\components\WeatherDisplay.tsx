import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface WeatherData {
  city: string;
  temperature: number;
  condition: string;
  humidity: number;
  icon: string;
}

const WeatherDisplay: React.FC = () => {
  const [weatherData, setWeatherData] = useState<WeatherData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [useCelsius, setUseCelsius] = useState(false);

  // Mock weather data for demonstration
  const mockWeatherData: WeatherData[] = [
    {
      city: 'New York',
      temperature: 72,
      condition: 'Partly Cloudy',
      humidity: 65,
      icon: '⛅'
    },
    {
      city: 'Chicago',
      temperature: 68,
      condition: 'Sunny',
      humidity: 58,
      icon: '☀️'
    },
    {
      city: 'Denver',
      temperature: 75,
      condition: 'Clear',
      humidity: 45,
      icon: '☀️'
    },
    {
      city: 'Los Angeles',
      temperature: 78,
      condition: 'Sunny',
      humidity: 70,
      icon: '☀️'
    },
    {
      city: 'Manila',
      temperature: 84,
      condition: 'Partly Cloudy',
      humidity: 78,
      icon: '⛅'
    }
  ];

  useEffect(() => {
    // Simulate API call
    const fetchWeatherData = async () => {
      try {
        setLoading(true);
        // In a real implementation, this would be an actual API call
        // const response = await fetch('/api/weather');
        // const data = await response.json();
        
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setWeatherData(mockWeatherData);
        setError(null);
      } catch (err) {
        setError('Failed to fetch weather data');
      } finally {
        setLoading(false);
      }
    };

    fetchWeatherData();
    
    // Refresh weather data every 15 minutes
    const interval = setInterval(fetchWeatherData, 15 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  const convertTemperature = (fahrenheit: number) => {
    return useCelsius ? Math.round((fahrenheit - 32) * 5/9) : fahrenheit;
  };

  const getTemperatureColor = (temp: number) => {
    const tempInF = useCelsius ? (temp * 9/5) + 32 : temp;
    if (tempInF >= 80) return 'text-red-600 dark:text-red-400';
    if (tempInF >= 70) return 'text-orange-600 dark:text-orange-400';
    if (tempInF >= 60) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-blue-600 dark:text-blue-400';
  };

  if (loading) {
    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Weather</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex space-x-3 overflow-x-auto">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse flex-shrink-0">
                <div className="w-28 p-2 bg-muted rounded-lg">
                  <div className="w-6 h-6 bg-muted-foreground/20 rounded mb-1 mx-auto"></div>
                  <div className="w-16 h-3 bg-muted-foreground/20 rounded mb-1 mx-auto"></div>
                  <div className="w-12 h-3 bg-muted-foreground/20 rounded mx-auto"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Weather</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="text-red-500 mb-2">⚠️</div>
            <p className="text-muted-foreground">{error}</p>
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
              className="mt-2"
            >
              Try again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">Weather</CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setUseCelsius(!useCelsius)}
              className="h-7 px-2 text-xs"
            >
              °{useCelsius ? 'C' : 'F'}
            </Button>
            <div className="text-xs text-muted-foreground">
              {new Date().toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex space-x-3 overflow-x-auto pb-2">
          {weatherData.map((weather, index) => (
            <div key={index} className="flex-shrink-0 w-28">
              <div className="p-2 bg-muted rounded-lg text-center">
                <span className="text-2xl block mb-1">{weather.icon}</span>
                <div className="font-medium text-foreground text-xs mb-1">
                  {weather.city}
                </div>
                <div className="text-xs text-muted-foreground mb-1">
                  {weather.condition}
                </div>
                <div className={`text-sm font-bold ${getTemperatureColor(convertTemperature(weather.temperature))}`}>
                  {convertTemperature(weather.temperature)}°{useCelsius ? 'C' : 'F'}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {weather.humidity}%
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-3 text-xs text-muted-foreground text-center">
          Refreshes every 15 minutes
        </div>
      </CardContent>
    </Card>
  );
};

export default WeatherDisplay;
