import React from 'react';
import { Button } from './ui/button';
import { Home, Calendar, Clipboard, BookOpen, Settings, ChevronLeft, ChevronRight, Lightbulb, Megaphone, TrendingUp, Terminal } from 'lucide-react';

interface SidebarProps {
  collapsed: boolean;
  onToggle: () => void;
  currentPage: string;
  onPageChange: (page: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ collapsed, onToggle, currentPage, onPageChange }) => {
  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Home },
    { id: 'announcements', label: 'Announcements', icon: Megaphone },
    { id: 'calendar', label: 'Team Schedules', icon: Calendar },
    { id: 'tasks', label: 'Task Board', icon: Clipboard },
    { id: 'performance', label: 'Team Performance', icon: TrendingUp },
    { id: 'knowledge', label: 'Knowledge Base', icon: BookOpen },
  ];

  return (
    <div className={`fixed left-0 top-0 h-full bg-card border-r border-border transition-all duration-300 z-30 ${
      collapsed ? 'w-12 sm:w-14' : 'w-48 sm:w-56'
    }`}>
      {/* Header */}
      <div className="flex items-center justify-between p-2 sm:p-3 border-b border-border">
        {!collapsed && (
          <div className="flex items-center space-x-1.5 sm:space-x-2">
            <div className="w-6 h-6 sm:w-7 sm:h-7 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xs sm:text-sm transform -rotate-12" style={{fontFamily: 'Comic Sans MS, cursive'}}>Z</span>
            </div>
            <span className="font-semibold text-foreground text-xs sm:text-sm">ZWIFTLLY</span>
          </div>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggle}
          className="h-6 w-6 sm:h-7 sm:w-7 p-0 hover:scale-110 transition-all duration-200 hover:bg-primary/10"
        >
          {collapsed ? <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 transition-transform duration-200" /> : <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4 transition-transform duration-200" />}
        </Button>
      </div>

      {/* Navigation Menu */}
      <nav className="p-2 sm:p-3 space-y-0.5 sm:space-y-1">
        {menuItems.map((item) => {
          const IconComponent = item.icon;
          return (
            <Button
              key={item.id}
              variant={currentPage === item.id ? "secondary" : "ghost"}
              onClick={() => onPageChange(item.id)}
              className={`group w-full justify-start h-8 sm:h-9 ${
                collapsed ? 'px-1.5 sm:px-2' : 'px-2 sm:px-3'
              } ${
                currentPage === item.id
                  ? 'bg-accent text-accent-foreground'
                  : 'text-muted-foreground hover:text-foreground'
              } hover:scale-105 transition-all duration-200 hover:bg-primary/10 hover:shadow-sm`}
            >
              <IconComponent className="h-3 w-3 sm:h-4 sm:w-4 transition-transform duration-200" />
              {!collapsed && (
                <span className="ml-2 sm:ml-3 text-xs sm:text-sm font-medium transition-all duration-200">
                  {item.label}
                </span>
              )}
            </Button>
          );
        })}
      </nav>

      {/* Bottom Section */}
      <div className="absolute bottom-0 left-0 right-0 p-2 sm:p-3 border-t border-border">
        <Button
          variant="ghost"
          onClick={() => onPageChange('settings')}
          className={`group w-full justify-start h-8 sm:h-9 ${
            collapsed ? 'px-1.5 sm:px-2' : 'px-2 sm:px-3'
          } ${
            currentPage === 'settings'
              ? 'bg-accent text-accent-foreground'
              : 'text-muted-foreground hover:text-foreground'
          } hover:scale-105 transition-all duration-200 hover:bg-primary/10 hover:shadow-sm`}
        >
          <Settings className="h-3 w-3 sm:h-4 sm:w-4 transition-transform duration-200" />
          {!collapsed && (
            <span className="ml-2 sm:ml-3 text-xs sm:text-sm font-medium transition-all duration-200">
              Settings
            </span>
          )}
        </Button>
      </div>
    </div>
  );
};

export default Sidebar;
